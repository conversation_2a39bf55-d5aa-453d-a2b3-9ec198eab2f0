(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const m of document.querySelectorAll('link[rel="modulepreload"]'))o(m);new MutationObserver(m=>{for(const b of m)if(b.type==="childList")for(const z of b.addedNodes)z.tagName==="LINK"&&z.rel==="modulepreload"&&o(z)}).observe(document,{childList:!0,subtree:!0});function f(m){const b={};return m.integrity&&(b.integrity=m.integrity),m.referrerPolicy&&(b.referrerPolicy=m.referrerPolicy),m.crossOrigin==="use-credentials"?b.credentials="include":m.crossOrigin==="anonymous"?b.credentials="omit":b.credentials="same-origin",b}function o(m){if(m.ep)return;m.ep=!0;const b=f(m);fetch(m.href,b)}})();var Nr={exports:{}},Yn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yd;function r0(){if(Yd)return Yn;Yd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function f(o,m,b){var z=null;if(b!==void 0&&(z=""+b),m.key!==void 0&&(z=""+m.key),"key"in m){b={};for(var M in m)M!=="key"&&(b[M]=m[M])}else b=m;return m=b.ref,{$$typeof:c,type:o,key:z,ref:m!==void 0?m:null,props:b}}return Yn.Fragment=s,Yn.jsx=f,Yn.jsxs=f,Yn}var Gd;function o0(){return Gd||(Gd=1,Nr.exports=r0()),Nr.exports}var T=o0(),Mr={exports:{}},nt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xd;function s0(){if(Xd)return nt;Xd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),m=Symbol.for("react.profiler"),b=Symbol.for("react.consumer"),z=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),N=Symbol.for("react.lazy"),C=Symbol.iterator;function H(h){return h===null||typeof h!="object"?null:(h=C&&h[C]||h["@@iterator"],typeof h=="function"?h:null)}var k={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},J=Object.assign,tt={};function ot(h,U,Y){this.props=h,this.context=U,this.refs=tt,this.updater=Y||k}ot.prototype.isReactComponent={},ot.prototype.setState=function(h,U){if(typeof h!="object"&&typeof h!="function"&&h!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,h,U,"setState")},ot.prototype.forceUpdate=function(h){this.updater.enqueueForceUpdate(this,h,"forceUpdate")};function Tt(){}Tt.prototype=ot.prototype;function jt(h,U,Y){this.props=h,this.context=U,this.refs=tt,this.updater=Y||k}var pt=jt.prototype=new Tt;pt.constructor=jt,J(pt,ot.prototype),pt.isPureReactComponent=!0;var et=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},St=Object.prototype.hasOwnProperty;function Nt(h,U,Y,B,X,ft){return Y=ft.ref,{$$typeof:c,type:h,key:U,ref:Y!==void 0?Y:null,props:ft}}function Z(h,U){return Nt(h.type,U,void 0,void 0,void 0,h.props)}function at(h){return typeof h=="object"&&h!==null&&h.$$typeof===c}function Ut(h){var U={"=":"=0",":":"=2"};return"$"+h.replace(/[=:]/g,function(Y){return U[Y]})}var G=/\/+/g;function lt(h,U){return typeof h=="object"&&h!==null&&h.key!=null?Ut(""+h.key):U.toString(36)}function ct(){}function Mt(h){switch(h.status){case"fulfilled":return h.value;case"rejected":throw h.reason;default:switch(typeof h.status=="string"?h.then(ct,ct):(h.status="pending",h.then(function(U){h.status==="pending"&&(h.status="fulfilled",h.value=U)},function(U){h.status==="pending"&&(h.status="rejected",h.reason=U)})),h.status){case"fulfilled":return h.value;case"rejected":throw h.reason}}throw h}function st(h,U,Y,B,X){var ft=typeof h;(ft==="undefined"||ft==="boolean")&&(h=null);var I=!1;if(h===null)I=!0;else switch(ft){case"bigint":case"string":case"number":I=!0;break;case"object":switch(h.$$typeof){case c:case s:I=!0;break;case N:return I=h._init,st(I(h._payload),U,Y,B,X)}}if(I)return X=X(h),I=B===""?"."+lt(h,0):B,et(X)?(Y="",I!=null&&(Y=I.replace(G,"$&/")+"/"),st(X,U,Y,"",function(ce){return ce})):X!=null&&(at(X)&&(X=Z(X,Y+(X.key==null||h&&h.key===X.key?"":(""+X.key).replace(G,"$&/")+"/")+I)),U.push(X)),1;I=0;var vt=B===""?".":B+":";if(et(h))for(var Rt=0;Rt<h.length;Rt++)B=h[Rt],ft=vt+lt(B,Rt),I+=st(B,U,Y,ft,X);else if(Rt=H(h),typeof Rt=="function")for(h=Rt.call(h),Rt=0;!(B=h.next()).done;)B=B.value,ft=vt+lt(B,Rt++),I+=st(B,U,Y,ft,X);else if(ft==="object"){if(typeof h.then=="function")return st(Mt(h),U,Y,B,X);throw U=String(h),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(h).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return I}function _(h,U,Y){if(h==null)return h;var B=[],X=0;return st(h,B,"","",function(ft){return U.call(Y,ft,X++)}),B}function q(h){if(h._status===-1){var U=h._result;U=U(),U.then(function(Y){(h._status===0||h._status===-1)&&(h._status=1,h._result=Y)},function(Y){(h._status===0||h._status===-1)&&(h._status=2,h._result=Y)}),h._status===-1&&(h._status=0,h._result=U)}if(h._status===1)return h._result.default;throw h._result}var j=typeof reportError=="function"?reportError:function(h){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof h=="object"&&h!==null&&typeof h.message=="string"?String(h.message):String(h),error:h});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",h);return}console.error(h)};function bt(){}return nt.Children={map:_,forEach:function(h,U,Y){_(h,function(){U.apply(this,arguments)},Y)},count:function(h){var U=0;return _(h,function(){U++}),U},toArray:function(h){return _(h,function(U){return U})||[]},only:function(h){if(!at(h))throw Error("React.Children.only expected to receive a single React element child.");return h}},nt.Component=ot,nt.Fragment=f,nt.Profiler=m,nt.PureComponent=jt,nt.StrictMode=o,nt.Suspense=p,nt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,nt.__COMPILER_RUNTIME={__proto__:null,c:function(h){return P.H.useMemoCache(h)}},nt.cache=function(h){return function(){return h.apply(null,arguments)}},nt.cloneElement=function(h,U,Y){if(h==null)throw Error("The argument must be a React element, but you passed "+h+".");var B=J({},h.props),X=h.key,ft=void 0;if(U!=null)for(I in U.ref!==void 0&&(ft=void 0),U.key!==void 0&&(X=""+U.key),U)!St.call(U,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&U.ref===void 0||(B[I]=U[I]);var I=arguments.length-2;if(I===1)B.children=Y;else if(1<I){for(var vt=Array(I),Rt=0;Rt<I;Rt++)vt[Rt]=arguments[Rt+2];B.children=vt}return Nt(h.type,X,void 0,void 0,ft,B)},nt.createContext=function(h){return h={$$typeof:z,_currentValue:h,_currentValue2:h,_threadCount:0,Provider:null,Consumer:null},h.Provider=h,h.Consumer={$$typeof:b,_context:h},h},nt.createElement=function(h,U,Y){var B,X={},ft=null;if(U!=null)for(B in U.key!==void 0&&(ft=""+U.key),U)St.call(U,B)&&B!=="key"&&B!=="__self"&&B!=="__source"&&(X[B]=U[B]);var I=arguments.length-2;if(I===1)X.children=Y;else if(1<I){for(var vt=Array(I),Rt=0;Rt<I;Rt++)vt[Rt]=arguments[Rt+2];X.children=vt}if(h&&h.defaultProps)for(B in I=h.defaultProps,I)X[B]===void 0&&(X[B]=I[B]);return Nt(h,ft,void 0,void 0,null,X)},nt.createRef=function(){return{current:null}},nt.forwardRef=function(h){return{$$typeof:M,render:h}},nt.isValidElement=at,nt.lazy=function(h){return{$$typeof:N,_payload:{_status:-1,_result:h},_init:q}},nt.memo=function(h,U){return{$$typeof:g,type:h,compare:U===void 0?null:U}},nt.startTransition=function(h){var U=P.T,Y={};P.T=Y;try{var B=h(),X=P.S;X!==null&&X(Y,B),typeof B=="object"&&B!==null&&typeof B.then=="function"&&B.then(bt,j)}catch(ft){j(ft)}finally{P.T=U}},nt.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},nt.use=function(h){return P.H.use(h)},nt.useActionState=function(h,U,Y){return P.H.useActionState(h,U,Y)},nt.useCallback=function(h,U){return P.H.useCallback(h,U)},nt.useContext=function(h){return P.H.useContext(h)},nt.useDebugValue=function(){},nt.useDeferredValue=function(h,U){return P.H.useDeferredValue(h,U)},nt.useEffect=function(h,U,Y){var B=P.H;if(typeof Y=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return B.useEffect(h,U)},nt.useId=function(){return P.H.useId()},nt.useImperativeHandle=function(h,U,Y){return P.H.useImperativeHandle(h,U,Y)},nt.useInsertionEffect=function(h,U){return P.H.useInsertionEffect(h,U)},nt.useLayoutEffect=function(h,U){return P.H.useLayoutEffect(h,U)},nt.useMemo=function(h,U){return P.H.useMemo(h,U)},nt.useOptimistic=function(h,U){return P.H.useOptimistic(h,U)},nt.useReducer=function(h,U,Y){return P.H.useReducer(h,U,Y)},nt.useRef=function(h){return P.H.useRef(h)},nt.useState=function(h){return P.H.useState(h)},nt.useSyncExternalStore=function(h,U,Y){return P.H.useSyncExternalStore(h,U,Y)},nt.useTransition=function(){return P.H.useTransition()},nt.version="19.1.0",nt}var Ld;function Gr(){return Ld||(Ld=1,Mr.exports=s0()),Mr.exports}var w=Gr(),Rr={exports:{}},Gn={},Or={exports:{}},Dr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function f0(){return Qd||(Qd=1,function(c){function s(_,q){var j=_.length;_.push(q);t:for(;0<j;){var bt=j-1>>>1,h=_[bt];if(0<m(h,q))_[bt]=q,_[j]=h,j=bt;else break t}}function f(_){return _.length===0?null:_[0]}function o(_){if(_.length===0)return null;var q=_[0],j=_.pop();if(j!==q){_[0]=j;t:for(var bt=0,h=_.length,U=h>>>1;bt<U;){var Y=2*(bt+1)-1,B=_[Y],X=Y+1,ft=_[X];if(0>m(B,j))X<h&&0>m(ft,B)?(_[bt]=ft,_[X]=j,bt=X):(_[bt]=B,_[Y]=j,bt=Y);else if(X<h&&0>m(ft,j))_[bt]=ft,_[X]=j,bt=X;else break t}}return q}function m(_,q){var j=_.sortIndex-q.sortIndex;return j!==0?j:_.id-q.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var b=performance;c.unstable_now=function(){return b.now()}}else{var z=Date,M=z.now();c.unstable_now=function(){return z.now()-M}}var p=[],g=[],N=1,C=null,H=3,k=!1,J=!1,tt=!1,ot=!1,Tt=typeof setTimeout=="function"?setTimeout:null,jt=typeof clearTimeout=="function"?clearTimeout:null,pt=typeof setImmediate<"u"?setImmediate:null;function et(_){for(var q=f(g);q!==null;){if(q.callback===null)o(g);else if(q.startTime<=_)o(g),q.sortIndex=q.expirationTime,s(p,q);else break;q=f(g)}}function P(_){if(tt=!1,et(_),!J)if(f(p)!==null)J=!0,St||(St=!0,lt());else{var q=f(g);q!==null&&st(P,q.startTime-_)}}var St=!1,Nt=-1,Z=5,at=-1;function Ut(){return ot?!0:!(c.unstable_now()-at<Z)}function G(){if(ot=!1,St){var _=c.unstable_now();at=_;var q=!0;try{t:{J=!1,tt&&(tt=!1,jt(Nt),Nt=-1),k=!0;var j=H;try{e:{for(et(_),C=f(p);C!==null&&!(C.expirationTime>_&&Ut());){var bt=C.callback;if(typeof bt=="function"){C.callback=null,H=C.priorityLevel;var h=bt(C.expirationTime<=_);if(_=c.unstable_now(),typeof h=="function"){C.callback=h,et(_),q=!0;break e}C===f(p)&&o(p),et(_)}else o(p);C=f(p)}if(C!==null)q=!0;else{var U=f(g);U!==null&&st(P,U.startTime-_),q=!1}}break t}finally{C=null,H=j,k=!1}q=void 0}}finally{q?lt():St=!1}}}var lt;if(typeof pt=="function")lt=function(){pt(G)};else if(typeof MessageChannel<"u"){var ct=new MessageChannel,Mt=ct.port2;ct.port1.onmessage=G,lt=function(){Mt.postMessage(null)}}else lt=function(){Tt(G,0)};function st(_,q){Nt=Tt(function(){_(c.unstable_now())},q)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(_){_.callback=null},c.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Z=0<_?Math.floor(1e3/_):5},c.unstable_getCurrentPriorityLevel=function(){return H},c.unstable_next=function(_){switch(H){case 1:case 2:case 3:var q=3;break;default:q=H}var j=H;H=q;try{return _()}finally{H=j}},c.unstable_requestPaint=function(){ot=!0},c.unstable_runWithPriority=function(_,q){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var j=H;H=_;try{return q()}finally{H=j}},c.unstable_scheduleCallback=function(_,q,j){var bt=c.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?bt+j:bt):j=bt,_){case 1:var h=-1;break;case 2:h=250;break;case 5:h=1073741823;break;case 4:h=1e4;break;default:h=5e3}return h=j+h,_={id:N++,callback:q,priorityLevel:_,startTime:j,expirationTime:h,sortIndex:-1},j>bt?(_.sortIndex=j,s(g,_),f(p)===null&&_===f(g)&&(tt?(jt(Nt),Nt=-1):tt=!0,st(P,j-bt))):(_.sortIndex=h,s(p,_),J||k||(J=!0,St||(St=!0,lt()))),_},c.unstable_shouldYield=Ut,c.unstable_wrapCallback=function(_){var q=H;return function(){var j=H;H=q;try{return _.apply(this,arguments)}finally{H=j}}}}(Dr)),Dr}var Vd;function d0(){return Vd||(Vd=1,Or.exports=f0()),Or.exports}var wr={exports:{}},te={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zd;function m0(){if(Zd)return te;Zd=1;var c=Gr();function s(p){var g="https://react.dev/errors/"+p;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var N=2;N<arguments.length;N++)g+="&args[]="+encodeURIComponent(arguments[N])}return"Minified React error #"+p+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var o={d:{f,r:function(){throw Error(s(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},m=Symbol.for("react.portal");function b(p,g,N){var C=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:m,key:C==null?null:""+C,children:p,containerInfo:g,implementation:N}}var z=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function M(p,g){if(p==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,te.createPortal=function(p,g){var N=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(s(299));return b(p,g,null,N)},te.flushSync=function(p){var g=z.T,N=o.p;try{if(z.T=null,o.p=2,p)return p()}finally{z.T=g,o.p=N,o.d.f()}},te.preconnect=function(p,g){typeof p=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,o.d.C(p,g))},te.prefetchDNS=function(p){typeof p=="string"&&o.d.D(p)},te.preinit=function(p,g){if(typeof p=="string"&&g&&typeof g.as=="string"){var N=g.as,C=M(N,g.crossOrigin),H=typeof g.integrity=="string"?g.integrity:void 0,k=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;N==="style"?o.d.S(p,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:C,integrity:H,fetchPriority:k}):N==="script"&&o.d.X(p,{crossOrigin:C,integrity:H,fetchPriority:k,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},te.preinitModule=function(p,g){if(typeof p=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var N=M(g.as,g.crossOrigin);o.d.M(p,{crossOrigin:N,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&o.d.M(p)},te.preload=function(p,g){if(typeof p=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var N=g.as,C=M(N,g.crossOrigin);o.d.L(p,N,{crossOrigin:C,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},te.preloadModule=function(p,g){if(typeof p=="string")if(g){var N=M(g.as,g.crossOrigin);o.d.m(p,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:N,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else o.d.m(p)},te.requestFormReset=function(p){o.d.r(p)},te.unstable_batchedUpdates=function(p,g){return p(g)},te.useFormState=function(p,g,N){return z.H.useFormState(p,g,N)},te.useFormStatus=function(){return z.H.useHostTransitionStatus()},te.version="19.1.0",te}var kd;function om(){if(kd)return wr.exports;kd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),wr.exports=m0(),wr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kd;function h0(){if(Kd)return Gn;Kd=1;var c=d0(),s=Gr(),f=om();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function m(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function b(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function z(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function M(t){if(b(t)!==t)throw Error(o(188))}function p(t){var e=t.alternate;if(!e){if(e=b(t),e===null)throw Error(o(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return M(n),t;if(u===a)return M(n),e;u=u.sibling}throw Error(o(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,r=n.child;r;){if(r===l){i=!0,l=n,a=u;break}if(r===a){i=!0,a=n,l=u;break}r=r.sibling}if(!i){for(r=u.child;r;){if(r===l){i=!0,l=u,a=n;break}if(r===a){i=!0,a=u,l=n;break}r=r.sibling}if(!i)throw Error(o(189))}}if(l.alternate!==a)throw Error(o(190))}if(l.tag!==3)throw Error(o(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var N=Object.assign,C=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),k=Symbol.for("react.portal"),J=Symbol.for("react.fragment"),tt=Symbol.for("react.strict_mode"),ot=Symbol.for("react.profiler"),Tt=Symbol.for("react.provider"),jt=Symbol.for("react.consumer"),pt=Symbol.for("react.context"),et=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),St=Symbol.for("react.suspense_list"),Nt=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),at=Symbol.for("react.activity"),Ut=Symbol.for("react.memo_cache_sentinel"),G=Symbol.iterator;function lt(t){return t===null||typeof t!="object"?null:(t=G&&t[G]||t["@@iterator"],typeof t=="function"?t:null)}var ct=Symbol.for("react.client.reference");function Mt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ct?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case J:return"Fragment";case ot:return"Profiler";case tt:return"StrictMode";case P:return"Suspense";case St:return"SuspenseList";case at:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case k:return"Portal";case pt:return(t.displayName||"Context")+".Provider";case jt:return(t._context.displayName||"Context")+".Consumer";case et:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Nt:return e=t.displayName||null,e!==null?e:Mt(t.type)||"Memo";case Z:e=t._payload,t=t._init;try{return Mt(t(e))}catch{}}return null}var st=Array.isArray,_=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j={pending:!1,data:null,method:null,action:null},bt=[],h=-1;function U(t){return{current:t}}function Y(t){0>h||(t.current=bt[h],bt[h]=null,h--)}function B(t,e){h++,bt[h]=t.current,t.current=e}var X=U(null),ft=U(null),I=U(null),vt=U(null);function Rt(t,e){switch(B(I,e),B(ft,t),B(X,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?md(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=md(e),t=hd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Y(X),B(X,t)}function ce(){Y(X),Y(ft),Y(I)}function ll(t){t.memoizedState!==null&&B(vt,t);var e=X.current,l=hd(e,t.type);e!==l&&(B(ft,t),B(X,l))}function al(t){ft.current===t&&(Y(X),Y(ft)),vt.current===t&&(Y(vt),Cn._currentValue=j)}var nl=Object.prototype.hasOwnProperty,hi=c.unstable_scheduleCallback,vi=c.unstable_cancelCallback,Gm=c.unstable_shouldYield,Xm=c.unstable_requestPaint,De=c.unstable_now,Lm=c.unstable_getCurrentPriorityLevel,Zr=c.unstable_ImmediatePriority,kr=c.unstable_UserBlockingPriority,Vn=c.unstable_NormalPriority,Qm=c.unstable_LowPriority,Kr=c.unstable_IdlePriority,Vm=c.log,Zm=c.unstable_setDisableYieldValue,Xa=null,re=null;function ul(t){if(typeof Vm=="function"&&Zm(t),re&&typeof re.setStrictMode=="function")try{re.setStrictMode(Xa,t)}catch{}}var oe=Math.clz32?Math.clz32:Jm,km=Math.log,Km=Math.LN2;function Jm(t){return t>>>=0,t===0?32:31-(km(t)/Km|0)|0}var Zn=256,kn=4194304;function Ol(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Kn(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var r=a&134217727;return r!==0?(a=r&~u,a!==0?n=Ol(a):(i&=r,i!==0?n=Ol(i):l||(l=r&~t,l!==0&&(n=Ol(l))))):(r=a&~u,r!==0?n=Ol(r):i!==0?n=Ol(i):l||(l=a&~t,l!==0&&(n=Ol(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function La(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Wm(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jr(){var t=Zn;return Zn<<=1,(Zn&4194048)===0&&(Zn=256),t}function Wr(){var t=kn;return kn<<=1,(kn&62914560)===0&&(kn=4194304),t}function gi(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Qa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function $m(t,e,l,a,n,u){var i=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var r=t.entanglements,d=t.expirationTimes,x=t.hiddenUpdates;for(l=i&~l;0<l;){var R=31-oe(l),D=1<<R;r[R]=0,d[R]=-1;var A=x[R];if(A!==null)for(x[R]=null,R=0;R<A.length;R++){var E=A[R];E!==null&&(E.lane&=-536870913)}l&=~D}a!==0&&$r(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~e))}function $r(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-oe(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Fr(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-oe(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function yi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function bi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Pr(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:Ud(t.type))}function Fm(t,e){var l=q.p;try{return q.p=t,e()}finally{q.p=l}}var il=Math.random().toString(36).slice(2),Pt="__reactFiber$"+il,le="__reactProps$"+il,Pl="__reactContainer$"+il,pi="__reactEvents$"+il,Pm="__reactListeners$"+il,Im="__reactHandles$"+il,Ir="__reactResources$"+il,Va="__reactMarker$"+il;function Si(t){delete t[Pt],delete t[le],delete t[pi],delete t[Pm],delete t[Im]}function Il(t){var e=t[Pt];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Pl]||l[Pt]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=bd(t);t!==null;){if(l=t[Pt])return l;t=bd(t)}return e}t=l,l=t.parentNode}return null}function ta(t){if(t=t[Pt]||t[Pl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Za(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function ea(t){var e=t[Ir];return e||(e=t[Ir]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[Va]=!0}var to=new Set,eo={};function Dl(t,e){la(t,e),la(t+"Capture",e)}function la(t,e){for(eo[t]=e,t=0;t<e.length;t++)to.add(e[t])}var th=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),lo={},ao={};function eh(t){return nl.call(ao,t)?!0:nl.call(lo,t)?!1:th.test(t)?ao[t]=!0:(lo[t]=!0,!1)}function Jn(t,e,l){if(eh(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Wn(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Ye(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var xi,no;function aa(t){if(xi===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);xi=e&&e[1]||"",no=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+xi+t+no}var Ai=!1;function Ti(t,e){if(!t||Ai)return"";Ai=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var D=function(){throw Error()};if(Object.defineProperty(D.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(D,[])}catch(E){var A=E}Reflect.construct(t,[],D)}else{try{D.call()}catch(E){A=E}t.call(D.prototype)}}else{try{throw Error()}catch(E){A=E}(D=t())&&typeof D.catch=="function"&&D.catch(function(){})}}catch(E){if(E&&A&&typeof E.stack=="string")return[E.stack,A.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],r=u[1];if(i&&r){var d=i.split(`
`),x=r.split(`
`);for(n=a=0;a<d.length&&!d[a].includes("DetermineComponentFrameRoot");)a++;for(;n<x.length&&!x[n].includes("DetermineComponentFrameRoot");)n++;if(a===d.length||n===x.length)for(a=d.length-1,n=x.length-1;1<=a&&0<=n&&d[a]!==x[n];)n--;for(;1<=a&&0<=n;a--,n--)if(d[a]!==x[n]){if(a!==1||n!==1)do if(a--,n--,0>n||d[a]!==x[n]){var R=`
`+d[a].replace(" at new "," at ");return t.displayName&&R.includes("<anonymous>")&&(R=R.replace("<anonymous>",t.displayName)),R}while(1<=a&&0<=n);break}}}finally{Ai=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?aa(l):""}function lh(t){switch(t.tag){case 26:case 27:case 5:return aa(t.type);case 16:return aa("Lazy");case 13:return aa("Suspense");case 19:return aa("SuspenseList");case 0:case 15:return Ti(t.type,!1);case 11:return Ti(t.type.render,!1);case 1:return Ti(t.type,!0);case 31:return aa("Activity");default:return""}}function uo(t){try{var e="";do e+=lh(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function ye(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function io(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function ah(t){var e=io(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function $n(t){t._valueTracker||(t._valueTracker=ah(t))}function co(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=io(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Fn(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var nh=/[\n"\\]/g;function be(t){return t.replace(nh,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ei(t,e,l,a,n,u,i,r){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),e!=null?i==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ye(e)):t.value!==""+ye(e)&&(t.value=""+ye(e)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),e!=null?zi(t,i,ye(e)):l!=null?zi(t,i,ye(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?t.name=""+ye(r):t.removeAttribute("name")}function ro(t,e,l,a,n,u,i,r){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+ye(l):"",e=e!=null?""+ye(e):l,r||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=r?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function zi(t,e,l){e==="number"&&Fn(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function na(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+ye(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function oo(t,e,l){if(e!=null&&(e=""+ye(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+ye(l):""}function so(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(o(92));if(st(a)){if(1<a.length)throw Error(o(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=ye(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function ua(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var uh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function fo(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||uh.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function mo(t,e,l){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&fo(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&fo(t,u,e[u])}function _i(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ih=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ch=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pn(t){return ch.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ni=null;function Mi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ia=null,ca=null;function ho(t){var e=ta(t);if(e&&(t=e.stateNode)){var l=t[le]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ei(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+be(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[le]||null;if(!n)throw Error(o(90));Ei(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&co(a)}break t;case"textarea":oo(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&na(t,!!l.multiple,e,!1)}}}var Ri=!1;function vo(t,e,l){if(Ri)return t(e,l);Ri=!0;try{var a=t(e);return a}finally{if(Ri=!1,(ia!==null||ca!==null)&&(Bu(),ia&&(e=ia,t=ca,ca=ia=null,ho(e),t)))for(e=0;e<t.length;e++)ho(t[e])}}function ka(t,e){var l=t.stateNode;if(l===null)return null;var a=l[le]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(o(231,e,typeof l));return l}var Ge=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Oi=!1;if(Ge)try{var Ka={};Object.defineProperty(Ka,"passive",{get:function(){Oi=!0}}),window.addEventListener("test",Ka,Ka),window.removeEventListener("test",Ka,Ka)}catch{Oi=!1}var cl=null,Di=null,In=null;function go(){if(In)return In;var t,e=Di,l=e.length,a,n="value"in cl?cl.value:cl.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var i=l-t;for(a=1;a<=i&&e[l-a]===n[u-a];a++);return In=n.slice(t,1<a?1-a:void 0)}function tu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function eu(){return!0}function yo(){return!1}function ae(t){function e(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var r in t)t.hasOwnProperty(r)&&(l=t[r],this[r]=l?l(u):u[r]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?eu:yo,this.isPropagationStopped=yo,this}return N(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=eu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=eu)},persist:function(){},isPersistent:eu}),e}var wl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},lu=ae(wl),Ja=N({},wl,{view:0,detail:0}),rh=ae(Ja),wi,Ui,Wa,au=N({},Ja,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ji,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Wa&&(Wa&&t.type==="mousemove"?(wi=t.screenX-Wa.screenX,Ui=t.screenY-Wa.screenY):Ui=wi=0,Wa=t),wi)},movementY:function(t){return"movementY"in t?t.movementY:Ui}}),bo=ae(au),oh=N({},au,{dataTransfer:0}),sh=ae(oh),fh=N({},Ja,{relatedTarget:0}),Ci=ae(fh),dh=N({},wl,{animationName:0,elapsedTime:0,pseudoElement:0}),mh=ae(dh),hh=N({},wl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),vh=ae(hh),gh=N({},wl,{data:0}),po=ae(gh),yh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},bh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ph={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sh(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=ph[t])?!!e[t]:!1}function ji(){return Sh}var xh=N({},Ja,{key:function(t){if(t.key){var e=yh[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=tu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?bh[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ji,charCode:function(t){return t.type==="keypress"?tu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?tu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Ah=ae(xh),Th=N({},au,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),So=ae(Th),Eh=N({},Ja,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ji}),zh=ae(Eh),_h=N({},wl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Nh=ae(_h),Mh=N({},au,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Rh=ae(Mh),Oh=N({},wl,{newState:0,oldState:0}),Dh=ae(Oh),wh=[9,13,27,32],Hi=Ge&&"CompositionEvent"in window,$a=null;Ge&&"documentMode"in document&&($a=document.documentMode);var Uh=Ge&&"TextEvent"in window&&!$a,xo=Ge&&(!Hi||$a&&8<$a&&11>=$a),Ao=" ",To=!1;function Eo(t,e){switch(t){case"keyup":return wh.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ra=!1;function Ch(t,e){switch(t){case"compositionend":return zo(e);case"keypress":return e.which!==32?null:(To=!0,Ao);case"textInput":return t=e.data,t===Ao&&To?null:t;default:return null}}function jh(t,e){if(ra)return t==="compositionend"||!Hi&&Eo(t,e)?(t=go(),In=Di=cl=null,ra=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return xo&&e.locale!=="ko"?null:e.data;default:return null}}var Hh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _o(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Hh[t.type]:e==="textarea"}function No(t,e,l,a){ia?ca?ca.push(a):ca=[a]:ia=a,e=Qu(e,"onChange"),0<e.length&&(l=new lu("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Fa=null,Pa=null;function Bh(t){rd(t,0)}function nu(t){var e=Za(t);if(co(e))return t}function Mo(t,e){if(t==="change")return e}var Ro=!1;if(Ge){var Bi;if(Ge){var qi="oninput"in document;if(!qi){var Oo=document.createElement("div");Oo.setAttribute("oninput","return;"),qi=typeof Oo.oninput=="function"}Bi=qi}else Bi=!1;Ro=Bi&&(!document.documentMode||9<document.documentMode)}function Do(){Fa&&(Fa.detachEvent("onpropertychange",wo),Pa=Fa=null)}function wo(t){if(t.propertyName==="value"&&nu(Pa)){var e=[];No(e,Pa,t,Mi(t)),vo(Bh,e)}}function qh(t,e,l){t==="focusin"?(Do(),Fa=e,Pa=l,Fa.attachEvent("onpropertychange",wo)):t==="focusout"&&Do()}function Yh(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return nu(Pa)}function Gh(t,e){if(t==="click")return nu(e)}function Xh(t,e){if(t==="input"||t==="change")return nu(e)}function Lh(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var se=typeof Object.is=="function"?Object.is:Lh;function Ia(t,e){if(se(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!nl.call(e,n)||!se(t[n],e[n]))return!1}return!0}function Uo(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Co(t,e){var l=Uo(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Uo(l)}}function jo(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?jo(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Ho(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Fn(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Fn(t.document)}return e}function Yi(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Qh=Ge&&"documentMode"in document&&11>=document.documentMode,oa=null,Gi=null,tn=null,Xi=!1;function Bo(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Xi||oa==null||oa!==Fn(a)||(a=oa,"selectionStart"in a&&Yi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),tn&&Ia(tn,a)||(tn=a,a=Qu(Gi,"onSelect"),0<a.length&&(e=new lu("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=oa)))}function Ul(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var sa={animationend:Ul("Animation","AnimationEnd"),animationiteration:Ul("Animation","AnimationIteration"),animationstart:Ul("Animation","AnimationStart"),transitionrun:Ul("Transition","TransitionRun"),transitionstart:Ul("Transition","TransitionStart"),transitioncancel:Ul("Transition","TransitionCancel"),transitionend:Ul("Transition","TransitionEnd")},Li={},qo={};Ge&&(qo=document.createElement("div").style,"AnimationEvent"in window||(delete sa.animationend.animation,delete sa.animationiteration.animation,delete sa.animationstart.animation),"TransitionEvent"in window||delete sa.transitionend.transition);function Cl(t){if(Li[t])return Li[t];if(!sa[t])return t;var e=sa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in qo)return Li[t]=e[l];return t}var Yo=Cl("animationend"),Go=Cl("animationiteration"),Xo=Cl("animationstart"),Vh=Cl("transitionrun"),Zh=Cl("transitionstart"),kh=Cl("transitioncancel"),Lo=Cl("transitionend"),Qo=new Map,Qi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Qi.push("scrollEnd");function Ne(t,e){Qo.set(t,e),Dl(e,[t])}var Vo=new WeakMap;function pe(t,e){if(typeof t=="object"&&t!==null){var l=Vo.get(t);return l!==void 0?l:(e={value:t,source:e,stack:uo(e)},Vo.set(t,e),e)}return{value:t,source:e,stack:uo(e)}}var Se=[],fa=0,Vi=0;function uu(){for(var t=fa,e=Vi=fa=0;e<t;){var l=Se[e];Se[e++]=null;var a=Se[e];Se[e++]=null;var n=Se[e];Se[e++]=null;var u=Se[e];if(Se[e++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&Zo(l,n,u)}}function iu(t,e,l,a){Se[fa++]=t,Se[fa++]=e,Se[fa++]=l,Se[fa++]=a,Vi|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Zi(t,e,l,a){return iu(t,e,l,a),cu(t)}function da(t,e){return iu(t,null,null,e),cu(t)}function Zo(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-oe(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function cu(t){if(50<_n)throw _n=0,Fc=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ma={};function Kh(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function fe(t,e,l,a){return new Kh(t,e,l,a)}function ki(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Xe(t,e){var l=t.alternate;return l===null?(l=fe(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function ko(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ru(t,e,l,a,n,u){var i=0;if(a=t,typeof t=="function")ki(t)&&(i=1);else if(typeof t=="string")i=Wv(t,l,X.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case at:return t=fe(31,l,e,n),t.elementType=at,t.lanes=u,t;case J:return jl(l.children,n,u,e);case tt:i=8,n|=24;break;case ot:return t=fe(12,l,e,n|2),t.elementType=ot,t.lanes=u,t;case P:return t=fe(13,l,e,n),t.elementType=P,t.lanes=u,t;case St:return t=fe(19,l,e,n),t.elementType=St,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Tt:case pt:i=10;break t;case jt:i=9;break t;case et:i=11;break t;case Nt:i=14;break t;case Z:i=16,a=null;break t}i=29,l=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=fe(i,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function jl(t,e,l,a){return t=fe(7,t,a,e),t.lanes=l,t}function Ki(t,e,l){return t=fe(6,t,null,e),t.lanes=l,t}function Ji(t,e,l){return e=fe(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ha=[],va=0,ou=null,su=0,xe=[],Ae=0,Hl=null,Le=1,Qe="";function Bl(t,e){ha[va++]=su,ha[va++]=ou,ou=t,su=e}function Ko(t,e,l){xe[Ae++]=Le,xe[Ae++]=Qe,xe[Ae++]=Hl,Hl=t;var a=Le;t=Qe;var n=32-oe(a)-1;a&=~(1<<n),l+=1;var u=32-oe(e)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Le=1<<32-oe(e)+n|l<<n|a,Qe=u+t}else Le=1<<u|l<<n|a,Qe=t}function Wi(t){t.return!==null&&(Bl(t,1),Ko(t,1,0))}function $i(t){for(;t===ou;)ou=ha[--va],ha[va]=null,su=ha[--va],ha[va]=null;for(;t===Hl;)Hl=xe[--Ae],xe[Ae]=null,Qe=xe[--Ae],xe[Ae]=null,Le=xe[--Ae],xe[Ae]=null}var ee=null,Ht=null,yt=!1,ql=null,we=!1,Fi=Error(o(519));function Yl(t){var e=Error(o(418,""));throw an(pe(e,t)),Fi}function Jo(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[Pt]=t,e[le]=a,l){case"dialog":mt("cancel",e),mt("close",e);break;case"iframe":case"object":case"embed":mt("load",e);break;case"video":case"audio":for(l=0;l<Mn.length;l++)mt(Mn[l],e);break;case"source":mt("error",e);break;case"img":case"image":case"link":mt("error",e),mt("load",e);break;case"details":mt("toggle",e);break;case"input":mt("invalid",e),ro(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),$n(e);break;case"select":mt("invalid",e);break;case"textarea":mt("invalid",e),so(e,a.value,a.defaultValue,a.children),$n(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||dd(e.textContent,l)?(a.popover!=null&&(mt("beforetoggle",e),mt("toggle",e)),a.onScroll!=null&&mt("scroll",e),a.onScrollEnd!=null&&mt("scrollend",e),a.onClick!=null&&(e.onclick=Vu),e=!0):e=!1,e||Yl(t)}function Wo(t){for(ee=t.return;ee;)switch(ee.tag){case 5:case 13:we=!1;return;case 27:case 3:we=!0;return;default:ee=ee.return}}function en(t){if(t!==ee)return!1;if(!yt)return Wo(t),yt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||mr(t.type,t.memoizedProps)),l=!l),l&&Ht&&Yl(t),Wo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Ht=Re(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Ht=null}}else e===27?(e=Ht,Tl(t.type)?(t=yr,yr=null,Ht=t):Ht=e):Ht=ee?Re(t.stateNode.nextSibling):null;return!0}function ln(){Ht=ee=null,yt=!1}function $o(){var t=ql;return t!==null&&(ie===null?ie=t:ie.push.apply(ie,t),ql=null),t}function an(t){ql===null?ql=[t]:ql.push(t)}var Pi=U(null),Gl=null,Ve=null;function rl(t,e,l){B(Pi,e._currentValue),e._currentValue=l}function Ze(t){t._currentValue=Pi.current,Y(Pi)}function Ii(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function tc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var r=u;u=n;for(var d=0;d<e.length;d++)if(r.context===e[d]){u.lanes|=l,r=u.alternate,r!==null&&(r.lanes|=l),Ii(u.return,l,t),a||(i=null);break t}u=r.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(o(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),Ii(i,l,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function nn(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(o(387));if(i=i.memoizedProps,i!==null){var r=n.type;se(n.pendingProps.value,i.value)||(t!==null?t.push(r):t=[r])}}else if(n===vt.current){if(i=n.alternate,i===null)throw Error(o(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Cn):t=[Cn])}n=n.return}t!==null&&tc(e,t,l,a),e.flags|=262144}function fu(t){for(t=t.firstContext;t!==null;){if(!se(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Xl(t){Gl=t,Ve=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function It(t){return Fo(Gl,t)}function du(t,e){return Gl===null&&Xl(t),Fo(t,e)}function Fo(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Ve===null){if(t===null)throw Error(o(308));Ve=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ve=Ve.next=e;return l}var Jh=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},Wh=c.unstable_scheduleCallback,$h=c.unstable_NormalPriority,Lt={$$typeof:pt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ec(){return{controller:new Jh,data:new Map,refCount:0}}function un(t){t.refCount--,t.refCount===0&&Wh($h,function(){t.controller.abort()})}var cn=null,lc=0,ga=0,ya=null;function Fh(t,e){if(cn===null){var l=cn=[];lc=0,ga=nr(),ya={status:"pending",value:void 0,then:function(a){l.push(a)}}}return lc++,e.then(Po,Po),e}function Po(){if(--lc===0&&cn!==null){ya!==null&&(ya.status="fulfilled");var t=cn;cn=null,ga=0,ya=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Ph(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Io=_.S;_.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Fh(t,e),Io!==null&&Io(t,e)};var Ll=U(null);function ac(){var t=Ll.current;return t!==null?t:Dt.pooledCache}function mu(t,e){e===null?B(Ll,Ll.current):B(Ll,e.pool)}function ts(){var t=ac();return t===null?null:{parent:Lt._currentValue,pool:t}}var rn=Error(o(460)),es=Error(o(474)),hu=Error(o(542)),nc={then:function(){}};function ls(t){return t=t.status,t==="fulfilled"||t==="rejected"}function vu(){}function as(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(vu,vu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,us(t),t;default:if(typeof e.status=="string")e.then(vu,vu);else{if(t=Dt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,us(t),t}throw on=e,rn}}var on=null;function ns(){if(on===null)throw Error(o(459));var t=on;return on=null,t}function us(t){if(t===rn||t===hu)throw Error(o(483))}var ol=!1;function uc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ic(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function sl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function fl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(xt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=cu(t),Zo(t,null,l),e}return iu(t,a,e,l),cu(t)}function sn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Fr(t,l)}}function cc(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var rc=!1;function fn(){if(rc){var t=ya;if(t!==null)throw t}}function dn(t,e,l,a){rc=!1;var n=t.updateQueue;ol=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,r=n.shared.pending;if(r!==null){n.shared.pending=null;var d=r,x=d.next;d.next=null,i===null?u=x:i.next=x,i=d;var R=t.alternate;R!==null&&(R=R.updateQueue,r=R.lastBaseUpdate,r!==i&&(r===null?R.firstBaseUpdate=x:r.next=x,R.lastBaseUpdate=d))}if(u!==null){var D=n.baseState;i=0,R=x=d=null,r=u;do{var A=r.lane&-536870913,E=A!==r.lane;if(E?(ht&A)===A:(a&A)===A){A!==0&&A===ga&&(rc=!0),R!==null&&(R=R.next={lane:0,tag:r.tag,payload:r.payload,callback:null,next:null});t:{var F=t,W=r;A=e;var _t=l;switch(W.tag){case 1:if(F=W.payload,typeof F=="function"){D=F.call(_t,D,A);break t}D=F;break t;case 3:F.flags=F.flags&-65537|128;case 0:if(F=W.payload,A=typeof F=="function"?F.call(_t,D,A):F,A==null)break t;D=N({},D,A);break t;case 2:ol=!0}}A=r.callback,A!==null&&(t.flags|=64,E&&(t.flags|=8192),E=n.callbacks,E===null?n.callbacks=[A]:E.push(A))}else E={lane:A,tag:r.tag,payload:r.payload,callback:r.callback,next:null},R===null?(x=R=E,d=D):R=R.next=E,i|=A;if(r=r.next,r===null){if(r=n.shared.pending,r===null)break;E=r,r=E.next,E.next=null,n.lastBaseUpdate=E,n.shared.pending=null}}while(!0);R===null&&(d=D),n.baseState=d,n.firstBaseUpdate=x,n.lastBaseUpdate=R,u===null&&(n.shared.lanes=0),pl|=i,t.lanes=i,t.memoizedState=D}}function is(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function cs(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)is(l[t],e)}var ba=U(null),gu=U(0);function rs(t,e){t=Pe,B(gu,t),B(ba,e),Pe=t|e.baseLanes}function oc(){B(gu,Pe),B(ba,ba.current)}function sc(){Pe=gu.current,Y(ba),Y(gu)}var dl=0,ut=null,Et=null,Gt=null,yu=!1,pa=!1,Ql=!1,bu=0,mn=0,Sa=null,Ih=0;function qt(){throw Error(o(321))}function fc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!se(t[l],e[l]))return!1;return!0}function dc(t,e,l,a,n,u){return dl=u,ut=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,_.H=t===null||t.memoizedState===null?Zs:ks,Ql=!1,u=l(a,n),Ql=!1,pa&&(u=ss(e,l,a,n)),os(t),u}function os(t){_.H=Eu;var e=Et!==null&&Et.next!==null;if(dl=0,Gt=Et=ut=null,yu=!1,mn=0,Sa=null,e)throw Error(o(300));t===null||kt||(t=t.dependencies,t!==null&&fu(t)&&(kt=!0))}function ss(t,e,l,a){ut=t;var n=0;do{if(pa&&(Sa=null),mn=0,pa=!1,25<=n)throw Error(o(301));if(n+=1,Gt=Et=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}_.H=iv,u=e(l,a)}while(pa);return u}function tv(){var t=_.H,e=t.useState()[0];return e=typeof e.then=="function"?hn(e):e,t=t.useState()[0],(Et!==null?Et.memoizedState:null)!==t&&(ut.flags|=1024),e}function mc(){var t=bu!==0;return bu=0,t}function hc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function vc(t){if(yu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}yu=!1}dl=0,Gt=Et=ut=null,pa=!1,mn=bu=0,Sa=null}function ne(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Gt===null?ut.memoizedState=Gt=t:Gt=Gt.next=t,Gt}function Xt(){if(Et===null){var t=ut.alternate;t=t!==null?t.memoizedState:null}else t=Et.next;var e=Gt===null?ut.memoizedState:Gt.next;if(e!==null)Gt=e,Et=t;else{if(t===null)throw ut.alternate===null?Error(o(467)):Error(o(310));Et=t,t={memoizedState:Et.memoizedState,baseState:Et.baseState,baseQueue:Et.baseQueue,queue:Et.queue,next:null},Gt===null?ut.memoizedState=Gt=t:Gt=Gt.next=t}return Gt}function gc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function hn(t){var e=mn;return mn+=1,Sa===null&&(Sa=[]),t=as(Sa,t,e),e=ut,(Gt===null?e.memoizedState:Gt.next)===null&&(e=e.alternate,_.H=e===null||e.memoizedState===null?Zs:ks),t}function pu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return hn(t);if(t.$$typeof===pt)return It(t)}throw Error(o(438,String(t)))}function yc(t){var e=null,l=ut.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=ut.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=gc(),ut.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=Ut;return e.index++,l}function ke(t,e){return typeof e=="function"?e(t):e}function Su(t){var e=Xt();return bc(e,Et,t)}function bc(t,e,l){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var r=i=null,d=null,x=e,R=!1;do{var D=x.lane&-536870913;if(D!==x.lane?(ht&D)===D:(dl&D)===D){var A=x.revertLane;if(A===0)d!==null&&(d=d.next={lane:0,revertLane:0,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null}),D===ga&&(R=!0);else if((dl&A)===A){x=x.next,A===ga&&(R=!0);continue}else D={lane:0,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},d===null?(r=d=D,i=u):d=d.next=D,ut.lanes|=A,pl|=A;D=x.action,Ql&&l(u,D),u=x.hasEagerState?x.eagerState:l(u,D)}else A={lane:D,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},d===null?(r=d=A,i=u):d=d.next=A,ut.lanes|=D,pl|=D;x=x.next}while(x!==null&&x!==e);if(d===null?i=u:d.next=r,!se(u,t.memoizedState)&&(kt=!0,R&&(l=ya,l!==null)))throw l;t.memoizedState=u,t.baseState=i,t.baseQueue=d,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function pc(t){var e=Xt(),l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);se(u,e.memoizedState)||(kt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function fs(t,e,l){var a=ut,n=Xt(),u=yt;if(u){if(l===void 0)throw Error(o(407));l=l()}else l=e();var i=!se((Et||n).memoizedState,l);i&&(n.memoizedState=l,kt=!0),n=n.queue;var r=hs.bind(null,a,n,t);if(vn(2048,8,r,[t]),n.getSnapshot!==e||i||Gt!==null&&Gt.memoizedState.tag&1){if(a.flags|=2048,xa(9,xu(),ms.bind(null,a,n,l,e),null),Dt===null)throw Error(o(349));u||(dl&124)!==0||ds(a,e,l)}return l}function ds(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=ut.updateQueue,e===null?(e=gc(),ut.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function ms(t,e,l,a){e.value=l,e.getSnapshot=a,vs(e)&&gs(t)}function hs(t,e,l){return l(function(){vs(e)&&gs(t)})}function vs(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!se(t,l)}catch{return!0}}function gs(t){var e=da(t,2);e!==null&&ge(e,t,2)}function Sc(t){var e=ne();if(typeof t=="function"){var l=t;if(t=l(),Ql){ul(!0);try{l()}finally{ul(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:t},e}function ys(t,e,l,a){return t.baseState=l,bc(t,Et,typeof a=="function"?a:ke)}function ev(t,e,l,a,n){if(Tu(t))throw Error(o(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};_.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,bs(e,u)):(u.next=l.next,e.pending=l.next=u)}}function bs(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=_.T,i={};_.T=i;try{var r=l(n,a),d=_.S;d!==null&&d(i,r),ps(t,e,r)}catch(x){xc(t,e,x)}finally{_.T=u}}else try{u=l(n,a),ps(t,e,u)}catch(x){xc(t,e,x)}}function ps(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Ss(t,e,a)},function(a){return xc(t,e,a)}):Ss(t,e,l)}function Ss(t,e,l){e.status="fulfilled",e.value=l,xs(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,bs(t,l)))}function xc(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,xs(e),e=e.next;while(e!==a)}t.action=null}function xs(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function As(t,e){return e}function Ts(t,e){if(yt){var l=Dt.formState;if(l!==null){t:{var a=ut;if(yt){if(Ht){e:{for(var n=Ht,u=we;n.nodeType!==8;){if(!u){n=null;break e}if(n=Re(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Ht=Re(n.nextSibling),a=n.data==="F!";break t}}Yl(a)}a=!1}a&&(e=l[0])}}return l=ne(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:As,lastRenderedState:e},l.queue=a,l=Ls.bind(null,ut,a),a.dispatch=l,a=Sc(!1),u=_c.bind(null,ut,!1,a.queue),a=ne(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=ev.bind(null,ut,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Es(t){var e=Xt();return zs(e,Et,t)}function zs(t,e,l){if(e=bc(t,e,As)[0],t=Su(ke)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=hn(e)}catch(i){throw i===rn?hu:i}else a=e;e=Xt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(ut.flags|=2048,xa(9,xu(),lv.bind(null,n,l),null)),[a,u,t]}function lv(t,e){t.action=e}function _s(t){var e=Xt(),l=Et;if(l!==null)return zs(e,l,t);Xt(),e=e.memoizedState,l=Xt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function xa(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=ut.updateQueue,e===null&&(e=gc(),ut.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function xu(){return{destroy:void 0,resource:void 0}}function Ns(){return Xt().memoizedState}function Au(t,e,l,a){var n=ne();a=a===void 0?null:a,ut.flags|=t,n.memoizedState=xa(1|e,xu(),l,a)}function vn(t,e,l,a){var n=Xt();a=a===void 0?null:a;var u=n.memoizedState.inst;Et!==null&&a!==null&&fc(a,Et.memoizedState.deps)?n.memoizedState=xa(e,u,l,a):(ut.flags|=t,n.memoizedState=xa(1|e,u,l,a))}function Ms(t,e){Au(8390656,8,t,e)}function Rs(t,e){vn(2048,8,t,e)}function Os(t,e){return vn(4,2,t,e)}function Ds(t,e){return vn(4,4,t,e)}function ws(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Us(t,e,l){l=l!=null?l.concat([t]):null,vn(4,4,ws.bind(null,e,t),l)}function Ac(){}function Cs(t,e){var l=Xt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&fc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function js(t,e){var l=Xt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&fc(e,a[1]))return a[0];if(a=t(),Ql){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a}function Tc(t,e,l){return l===void 0||(dl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Yf(),ut.lanes|=t,pl|=t,l)}function Hs(t,e,l,a){return se(l,e)?l:ba.current!==null?(t=Tc(t,l,a),se(t,e)||(kt=!0),t):(dl&42)===0?(kt=!0,t.memoizedState=l):(t=Yf(),ut.lanes|=t,pl|=t,e)}function Bs(t,e,l,a,n){var u=q.p;q.p=u!==0&&8>u?u:8;var i=_.T,r={};_.T=r,_c(t,!1,e,l);try{var d=n(),x=_.S;if(x!==null&&x(r,d),d!==null&&typeof d=="object"&&typeof d.then=="function"){var R=Ph(d,a);gn(t,e,R,ve(t))}else gn(t,e,a,ve(t))}catch(D){gn(t,e,{then:function(){},status:"rejected",reason:D},ve())}finally{q.p=u,_.T=i}}function av(){}function Ec(t,e,l,a){if(t.tag!==5)throw Error(o(476));var n=qs(t).queue;Bs(t,n,e,j,l===null?av:function(){return Ys(t),l(a)})}function qs(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:j,baseState:j,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:j},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Ys(t){var e=qs(t).next.queue;gn(t,e,{},ve())}function zc(){return It(Cn)}function Gs(){return Xt().memoizedState}function Xs(){return Xt().memoizedState}function nv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=ve();t=sl(l);var a=fl(e,t,l);a!==null&&(ge(a,e,l),sn(a,e,l)),e={cache:ec()},t.payload=e;return}e=e.return}}function uv(t,e,l){var a=ve();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Tu(t)?Qs(e,l):(l=Zi(t,e,l,a),l!==null&&(ge(l,t,a),Vs(l,e,a)))}function Ls(t,e,l){var a=ve();gn(t,e,l,a)}function gn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Tu(t))Qs(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var i=e.lastRenderedState,r=u(i,l);if(n.hasEagerState=!0,n.eagerState=r,se(r,i))return iu(t,e,n,0),Dt===null&&uu(),!1}catch{}finally{}if(l=Zi(t,e,n,a),l!==null)return ge(l,t,a),Vs(l,e,a),!0}return!1}function _c(t,e,l,a){if(a={lane:2,revertLane:nr(),action:a,hasEagerState:!1,eagerState:null,next:null},Tu(t)){if(e)throw Error(o(479))}else e=Zi(t,l,a,2),e!==null&&ge(e,t,2)}function Tu(t){var e=t.alternate;return t===ut||e!==null&&e===ut}function Qs(t,e){pa=yu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Vs(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Fr(t,l)}}var Eu={readContext:It,use:pu,useCallback:qt,useContext:qt,useEffect:qt,useImperativeHandle:qt,useLayoutEffect:qt,useInsertionEffect:qt,useMemo:qt,useReducer:qt,useRef:qt,useState:qt,useDebugValue:qt,useDeferredValue:qt,useTransition:qt,useSyncExternalStore:qt,useId:qt,useHostTransitionStatus:qt,useFormState:qt,useActionState:qt,useOptimistic:qt,useMemoCache:qt,useCacheRefresh:qt},Zs={readContext:It,use:pu,useCallback:function(t,e){return ne().memoizedState=[t,e===void 0?null:e],t},useContext:It,useEffect:Ms,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Au(4194308,4,ws.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Au(4194308,4,t,e)},useInsertionEffect:function(t,e){Au(4,2,t,e)},useMemo:function(t,e){var l=ne();e=e===void 0?null:e;var a=t();if(Ql){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=ne();if(l!==void 0){var n=l(e);if(Ql){ul(!0);try{l(e)}finally{ul(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=uv.bind(null,ut,t),[a.memoizedState,t]},useRef:function(t){var e=ne();return t={current:t},e.memoizedState=t},useState:function(t){t=Sc(t);var e=t.queue,l=Ls.bind(null,ut,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:Ac,useDeferredValue:function(t,e){var l=ne();return Tc(l,t,e)},useTransition:function(){var t=Sc(!1);return t=Bs.bind(null,ut,t.queue,!0,!1),ne().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=ut,n=ne();if(yt){if(l===void 0)throw Error(o(407));l=l()}else{if(l=e(),Dt===null)throw Error(o(349));(ht&124)!==0||ds(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,Ms(hs.bind(null,a,u,t),[t]),a.flags|=2048,xa(9,xu(),ms.bind(null,a,u,l,e),null),l},useId:function(){var t=ne(),e=Dt.identifierPrefix;if(yt){var l=Qe,a=Le;l=(a&~(1<<32-oe(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=bu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=Ih++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:zc,useFormState:Ts,useActionState:Ts,useOptimistic:function(t){var e=ne();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=_c.bind(null,ut,!0,l),l.dispatch=e,[t,e]},useMemoCache:yc,useCacheRefresh:function(){return ne().memoizedState=nv.bind(null,ut)}},ks={readContext:It,use:pu,useCallback:Cs,useContext:It,useEffect:Rs,useImperativeHandle:Us,useInsertionEffect:Os,useLayoutEffect:Ds,useMemo:js,useReducer:Su,useRef:Ns,useState:function(){return Su(ke)},useDebugValue:Ac,useDeferredValue:function(t,e){var l=Xt();return Hs(l,Et.memoizedState,t,e)},useTransition:function(){var t=Su(ke)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:hn(t),e]},useSyncExternalStore:fs,useId:Gs,useHostTransitionStatus:zc,useFormState:Es,useActionState:Es,useOptimistic:function(t,e){var l=Xt();return ys(l,Et,t,e)},useMemoCache:yc,useCacheRefresh:Xs},iv={readContext:It,use:pu,useCallback:Cs,useContext:It,useEffect:Rs,useImperativeHandle:Us,useInsertionEffect:Os,useLayoutEffect:Ds,useMemo:js,useReducer:pc,useRef:Ns,useState:function(){return pc(ke)},useDebugValue:Ac,useDeferredValue:function(t,e){var l=Xt();return Et===null?Tc(l,t,e):Hs(l,Et.memoizedState,t,e)},useTransition:function(){var t=pc(ke)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:hn(t),e]},useSyncExternalStore:fs,useId:Gs,useHostTransitionStatus:zc,useFormState:_s,useActionState:_s,useOptimistic:function(t,e){var l=Xt();return Et!==null?ys(l,Et,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:yc,useCacheRefresh:Xs},Aa=null,yn=0;function zu(t){var e=yn;return yn+=1,Aa===null&&(Aa=[]),as(Aa,t,e)}function bn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function _u(t,e){throw e.$$typeof===C?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Ks(t){var e=t._init;return e(t._payload)}function Js(t){function e(y,v){if(t){var S=y.deletions;S===null?(y.deletions=[v],y.flags|=16):S.push(v)}}function l(y,v){if(!t)return null;for(;v!==null;)e(y,v),v=v.sibling;return null}function a(y){for(var v=new Map;y!==null;)y.key!==null?v.set(y.key,y):v.set(y.index,y),y=y.sibling;return v}function n(y,v){return y=Xe(y,v),y.index=0,y.sibling=null,y}function u(y,v,S){return y.index=S,t?(S=y.alternate,S!==null?(S=S.index,S<v?(y.flags|=67108866,v):S):(y.flags|=67108866,v)):(y.flags|=1048576,v)}function i(y){return t&&y.alternate===null&&(y.flags|=67108866),y}function r(y,v,S,O){return v===null||v.tag!==6?(v=Ki(S,y.mode,O),v.return=y,v):(v=n(v,S),v.return=y,v)}function d(y,v,S,O){var L=S.type;return L===J?R(y,v,S.props.children,O,S.key):v!==null&&(v.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Z&&Ks(L)===v.type)?(v=n(v,S.props),bn(v,S),v.return=y,v):(v=ru(S.type,S.key,S.props,null,y.mode,O),bn(v,S),v.return=y,v)}function x(y,v,S,O){return v===null||v.tag!==4||v.stateNode.containerInfo!==S.containerInfo||v.stateNode.implementation!==S.implementation?(v=Ji(S,y.mode,O),v.return=y,v):(v=n(v,S.children||[]),v.return=y,v)}function R(y,v,S,O,L){return v===null||v.tag!==7?(v=jl(S,y.mode,O,L),v.return=y,v):(v=n(v,S),v.return=y,v)}function D(y,v,S){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=Ki(""+v,y.mode,S),v.return=y,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case H:return S=ru(v.type,v.key,v.props,null,y.mode,S),bn(S,v),S.return=y,S;case k:return v=Ji(v,y.mode,S),v.return=y,v;case Z:var O=v._init;return v=O(v._payload),D(y,v,S)}if(st(v)||lt(v))return v=jl(v,y.mode,S,null),v.return=y,v;if(typeof v.then=="function")return D(y,zu(v),S);if(v.$$typeof===pt)return D(y,du(y,v),S);_u(y,v)}return null}function A(y,v,S,O){var L=v!==null?v.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return L!==null?null:r(y,v,""+S,O);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case H:return S.key===L?d(y,v,S,O):null;case k:return S.key===L?x(y,v,S,O):null;case Z:return L=S._init,S=L(S._payload),A(y,v,S,O)}if(st(S)||lt(S))return L!==null?null:R(y,v,S,O,null);if(typeof S.then=="function")return A(y,v,zu(S),O);if(S.$$typeof===pt)return A(y,v,du(y,S),O);_u(y,S)}return null}function E(y,v,S,O,L){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return y=y.get(S)||null,r(v,y,""+O,L);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case H:return y=y.get(O.key===null?S:O.key)||null,d(v,y,O,L);case k:return y=y.get(O.key===null?S:O.key)||null,x(v,y,O,L);case Z:var rt=O._init;return O=rt(O._payload),E(y,v,S,O,L)}if(st(O)||lt(O))return y=y.get(S)||null,R(v,y,O,L,null);if(typeof O.then=="function")return E(y,v,S,zu(O),L);if(O.$$typeof===pt)return E(y,v,S,du(v,O),L);_u(v,O)}return null}function F(y,v,S,O){for(var L=null,rt=null,K=v,$=v=0,Jt=null;K!==null&&$<S.length;$++){K.index>$?(Jt=K,K=null):Jt=K.sibling;var gt=A(y,K,S[$],O);if(gt===null){K===null&&(K=Jt);break}t&&K&&gt.alternate===null&&e(y,K),v=u(gt,v,$),rt===null?L=gt:rt.sibling=gt,rt=gt,K=Jt}if($===S.length)return l(y,K),yt&&Bl(y,$),L;if(K===null){for(;$<S.length;$++)K=D(y,S[$],O),K!==null&&(v=u(K,v,$),rt===null?L=K:rt.sibling=K,rt=K);return yt&&Bl(y,$),L}for(K=a(K);$<S.length;$++)Jt=E(K,y,$,S[$],O),Jt!==null&&(t&&Jt.alternate!==null&&K.delete(Jt.key===null?$:Jt.key),v=u(Jt,v,$),rt===null?L=Jt:rt.sibling=Jt,rt=Jt);return t&&K.forEach(function(Ml){return e(y,Ml)}),yt&&Bl(y,$),L}function W(y,v,S,O){if(S==null)throw Error(o(151));for(var L=null,rt=null,K=v,$=v=0,Jt=null,gt=S.next();K!==null&&!gt.done;$++,gt=S.next()){K.index>$?(Jt=K,K=null):Jt=K.sibling;var Ml=A(y,K,gt.value,O);if(Ml===null){K===null&&(K=Jt);break}t&&K&&Ml.alternate===null&&e(y,K),v=u(Ml,v,$),rt===null?L=Ml:rt.sibling=Ml,rt=Ml,K=Jt}if(gt.done)return l(y,K),yt&&Bl(y,$),L;if(K===null){for(;!gt.done;$++,gt=S.next())gt=D(y,gt.value,O),gt!==null&&(v=u(gt,v,$),rt===null?L=gt:rt.sibling=gt,rt=gt);return yt&&Bl(y,$),L}for(K=a(K);!gt.done;$++,gt=S.next())gt=E(K,y,$,gt.value,O),gt!==null&&(t&&gt.alternate!==null&&K.delete(gt.key===null?$:gt.key),v=u(gt,v,$),rt===null?L=gt:rt.sibling=gt,rt=gt);return t&&K.forEach(function(c0){return e(y,c0)}),yt&&Bl(y,$),L}function _t(y,v,S,O){if(typeof S=="object"&&S!==null&&S.type===J&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case H:t:{for(var L=S.key;v!==null;){if(v.key===L){if(L=S.type,L===J){if(v.tag===7){l(y,v.sibling),O=n(v,S.props.children),O.return=y,y=O;break t}}else if(v.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Z&&Ks(L)===v.type){l(y,v.sibling),O=n(v,S.props),bn(O,S),O.return=y,y=O;break t}l(y,v);break}else e(y,v);v=v.sibling}S.type===J?(O=jl(S.props.children,y.mode,O,S.key),O.return=y,y=O):(O=ru(S.type,S.key,S.props,null,y.mode,O),bn(O,S),O.return=y,y=O)}return i(y);case k:t:{for(L=S.key;v!==null;){if(v.key===L)if(v.tag===4&&v.stateNode.containerInfo===S.containerInfo&&v.stateNode.implementation===S.implementation){l(y,v.sibling),O=n(v,S.children||[]),O.return=y,y=O;break t}else{l(y,v);break}else e(y,v);v=v.sibling}O=Ji(S,y.mode,O),O.return=y,y=O}return i(y);case Z:return L=S._init,S=L(S._payload),_t(y,v,S,O)}if(st(S))return F(y,v,S,O);if(lt(S)){if(L=lt(S),typeof L!="function")throw Error(o(150));return S=L.call(S),W(y,v,S,O)}if(typeof S.then=="function")return _t(y,v,zu(S),O);if(S.$$typeof===pt)return _t(y,v,du(y,S),O);_u(y,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,v!==null&&v.tag===6?(l(y,v.sibling),O=n(v,S),O.return=y,y=O):(l(y,v),O=Ki(S,y.mode,O),O.return=y,y=O),i(y)):l(y,v)}return function(y,v,S,O){try{yn=0;var L=_t(y,v,S,O);return Aa=null,L}catch(K){if(K===rn||K===hu)throw K;var rt=fe(29,K,null,y.mode);return rt.lanes=O,rt.return=y,rt}finally{}}}var Ta=Js(!0),Ws=Js(!1),Te=U(null),Ue=null;function ml(t){var e=t.alternate;B(Qt,Qt.current&1),B(Te,t),Ue===null&&(e===null||ba.current!==null||e.memoizedState!==null)&&(Ue=t)}function $s(t){if(t.tag===22){if(B(Qt,Qt.current),B(Te,t),Ue===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ue=t)}}else hl()}function hl(){B(Qt,Qt.current),B(Te,Te.current)}function Ke(t){Y(Te),Ue===t&&(Ue=null),Y(Qt)}var Qt=U(0);function Nu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||gr(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Nc(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:N({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Mc={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=ve(),n=sl(a);n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(ge(e,t,a),sn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=ve(),n=sl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(ge(e,t,a),sn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=ve(),a=sl(l);a.tag=2,e!=null&&(a.callback=e),e=fl(t,a,l),e!==null&&(ge(e,t,l),sn(e,t,l))}};function Fs(t,e,l,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):e.prototype&&e.prototype.isPureReactComponent?!Ia(l,a)||!Ia(n,u):!0}function Ps(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Mc.enqueueReplaceState(e,e.state,null)}function Vl(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=N({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Mu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Is(t){Mu(t)}function tf(t){console.error(t)}function ef(t){Mu(t)}function Ru(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function lf(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Rc(t,e,l){return l=sl(l),l.tag=3,l.payload={element:null},l.callback=function(){Ru(t,e)},l}function af(t){return t=sl(t),t.tag=3,t}function nf(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){lf(e,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){lf(e,l,a),typeof n!="function"&&(Sl===null?Sl=new Set([this]):Sl.add(this));var r=a.stack;this.componentDidCatch(a.value,{componentStack:r!==null?r:""})})}function cv(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&nn(e,l,n,!0),l=Te.current,l!==null){switch(l.tag){case 13:return Ue===null?Ic():l.alternate===null&&Bt===0&&(Bt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===nc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),er(t,a,n)),!1;case 22:return l.flags|=65536,a===nc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),er(t,a,n)),!1}throw Error(o(435,l.tag))}return er(t,a,n),Ic(),!1}if(yt)return e=Te.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Fi&&(t=Error(o(422),{cause:a}),an(pe(t,l)))):(a!==Fi&&(e=Error(o(423),{cause:a}),an(pe(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=pe(a,l),n=Rc(t.stateNode,a,n),cc(t,n),Bt!==4&&(Bt=2)),!1;var u=Error(o(520),{cause:a});if(u=pe(u,l),zn===null?zn=[u]:zn.push(u),Bt!==4&&(Bt=2),e===null)return!0;a=pe(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Rc(l.stateNode,a,t),cc(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Sl===null||!Sl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=af(n),nf(n,t,l,a),cc(l,n),!1}l=l.return}while(l!==null);return!1}var uf=Error(o(461)),kt=!1;function Wt(t,e,l,a){e.child=t===null?Ws(e,null,l,a):Ta(e,t.child,l,a)}function cf(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var i={};for(var r in a)r!=="ref"&&(i[r]=a[r])}else i=a;return Xl(e),a=dc(t,e,l,i,u,n),r=mc(),t!==null&&!kt?(hc(t,e,n),Je(t,e,n)):(yt&&r&&Wi(e),e.flags|=1,Wt(t,e,a,n),e.child)}function rf(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!ki(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,of(t,e,u,a,n)):(t=ru(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Bc(t,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:Ia,l(i,a)&&t.ref===e.ref)return Je(t,e,n)}return e.flags|=1,t=Xe(u,a),t.ref=e.ref,t.return=e,e.child=t}function of(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(Ia(u,a)&&t.ref===e.ref)if(kt=!1,e.pendingProps=a=u,Bc(t,n))(t.flags&131072)!==0&&(kt=!0);else return e.lanes=t.lanes,Je(t,e,n)}return Oc(t,e,l,a,n)}function sf(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return ff(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&mu(e,u!==null?u.cachePool:null),u!==null?rs(e,u):oc(),$s(e);else return e.lanes=e.childLanes=536870912,ff(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(mu(e,u.cachePool),rs(e,u),hl(),e.memoizedState=null):(t!==null&&mu(e,null),oc(),hl());return Wt(t,e,n,l),e.child}function ff(t,e,l,a){var n=ac();return n=n===null?null:{parent:Lt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&mu(e,null),oc(),$s(e),t!==null&&nn(t,e,a,!0),null}function Ou(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(o(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function Oc(t,e,l,a,n){return Xl(e),l=dc(t,e,l,a,void 0,n),a=mc(),t!==null&&!kt?(hc(t,e,n),Je(t,e,n)):(yt&&a&&Wi(e),e.flags|=1,Wt(t,e,l,n),e.child)}function df(t,e,l,a,n,u){return Xl(e),e.updateQueue=null,l=ss(e,a,l,n),os(t),a=mc(),t!==null&&!kt?(hc(t,e,u),Je(t,e,u)):(yt&&a&&Wi(e),e.flags|=1,Wt(t,e,l,u),e.child)}function mf(t,e,l,a,n){if(Xl(e),e.stateNode===null){var u=ma,i=l.contextType;typeof i=="object"&&i!==null&&(u=It(i)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Mc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},uc(e),i=l.contextType,u.context=typeof i=="object"&&i!==null?It(i):ma,u.state=e.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(Nc(e,l,i,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&Mc.enqueueReplaceState(u,u.state,null),dn(e,a,u,n),fn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var r=e.memoizedProps,d=Vl(l,r);u.props=d;var x=u.context,R=l.contextType;i=ma,typeof R=="object"&&R!==null&&(i=It(R));var D=l.getDerivedStateFromProps;R=typeof D=="function"||typeof u.getSnapshotBeforeUpdate=="function",r=e.pendingProps!==r,R||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r||x!==i)&&Ps(e,u,a,i),ol=!1;var A=e.memoizedState;u.state=A,dn(e,a,u,n),fn(),x=e.memoizedState,r||A!==x||ol?(typeof D=="function"&&(Nc(e,l,D,a),x=e.memoizedState),(d=ol||Fs(e,l,d,a,A,x,i))?(R||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=x),u.props=a,u.state=x,u.context=i,a=d):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,ic(t,e),i=e.memoizedProps,R=Vl(l,i),u.props=R,D=e.pendingProps,A=u.context,x=l.contextType,d=ma,typeof x=="object"&&x!==null&&(d=It(x)),r=l.getDerivedStateFromProps,(x=typeof r=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==D||A!==d)&&Ps(e,u,a,d),ol=!1,A=e.memoizedState,u.state=A,dn(e,a,u,n),fn();var E=e.memoizedState;i!==D||A!==E||ol||t!==null&&t.dependencies!==null&&fu(t.dependencies)?(typeof r=="function"&&(Nc(e,l,r,a),E=e.memoizedState),(R=ol||Fs(e,l,R,a,A,E,d)||t!==null&&t.dependencies!==null&&fu(t.dependencies))?(x||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,E,d),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,E,d)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=E),u.props=a,u.state=E,u.context=d,a=R):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Ou(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Ta(e,t.child,null,n),e.child=Ta(e,null,l,n)):Wt(t,e,l,n),e.memoizedState=u.state,t=e.child):t=Je(t,e,n),t}function hf(t,e,l,a){return ln(),e.flags|=256,Wt(t,e,l,a),e.child}var Dc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function wc(t){return{baseLanes:t,cachePool:ts()}}function Uc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Ee),t}function vf(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(Qt.current&2)!==0),i&&(n=!0,e.flags&=-129),i=(e.flags&32)!==0,e.flags&=-33,t===null){if(yt){if(n?ml(e):hl(),yt){var r=Ht,d;if(d=r){t:{for(d=r,r=we;d.nodeType!==8;){if(!r){r=null;break t}if(d=Re(d.nextSibling),d===null){r=null;break t}}r=d}r!==null?(e.memoizedState={dehydrated:r,treeContext:Hl!==null?{id:Le,overflow:Qe}:null,retryLane:536870912,hydrationErrors:null},d=fe(18,null,null,0),d.stateNode=r,d.return=e,e.child=d,ee=e,Ht=null,d=!0):d=!1}d||Yl(e)}if(r=e.memoizedState,r!==null&&(r=r.dehydrated,r!==null))return gr(r)?e.lanes=32:e.lanes=536870912,null;Ke(e)}return r=a.children,a=a.fallback,n?(hl(),n=e.mode,r=Du({mode:"hidden",children:r},n),a=jl(a,n,l,null),r.return=e,a.return=e,r.sibling=a,e.child=r,n=e.child,n.memoizedState=wc(l),n.childLanes=Uc(t,i,l),e.memoizedState=Dc,a):(ml(e),Cc(e,r))}if(d=t.memoizedState,d!==null&&(r=d.dehydrated,r!==null)){if(u)e.flags&256?(ml(e),e.flags&=-257,e=jc(t,e,l)):e.memoizedState!==null?(hl(),e.child=t.child,e.flags|=128,e=null):(hl(),n=a.fallback,r=e.mode,a=Du({mode:"visible",children:a.children},r),n=jl(n,r,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ta(e,t.child,null,l),a=e.child,a.memoizedState=wc(l),a.childLanes=Uc(t,i,l),e.memoizedState=Dc,e=n);else if(ml(e),gr(r)){if(i=r.nextSibling&&r.nextSibling.dataset,i)var x=i.dgst;i=x,a=Error(o(419)),a.stack="",a.digest=i,an({value:a,source:null,stack:null}),e=jc(t,e,l)}else if(kt||nn(t,e,l,!1),i=(l&t.childLanes)!==0,kt||i){if(i=Dt,i!==null&&(a=l&-l,a=(a&42)!==0?1:yi(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==d.retryLane))throw d.retryLane=a,da(t,a),ge(i,t,a),uf;r.data==="$?"||Ic(),e=jc(t,e,l)}else r.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=d.treeContext,Ht=Re(r.nextSibling),ee=e,yt=!0,ql=null,we=!1,t!==null&&(xe[Ae++]=Le,xe[Ae++]=Qe,xe[Ae++]=Hl,Le=t.id,Qe=t.overflow,Hl=e),e=Cc(e,a.children),e.flags|=4096);return e}return n?(hl(),n=a.fallback,r=e.mode,d=t.child,x=d.sibling,a=Xe(d,{mode:"hidden",children:a.children}),a.subtreeFlags=d.subtreeFlags&65011712,x!==null?n=Xe(x,n):(n=jl(n,r,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,r=t.child.memoizedState,r===null?r=wc(l):(d=r.cachePool,d!==null?(x=Lt._currentValue,d=d.parent!==x?{parent:x,pool:x}:d):d=ts(),r={baseLanes:r.baseLanes|l,cachePool:d}),n.memoizedState=r,n.childLanes=Uc(t,i,l),e.memoizedState=Dc,a):(ml(e),l=t.child,t=l.sibling,l=Xe(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(i=e.deletions,i===null?(e.deletions=[t],e.flags|=16):i.push(t)),e.child=l,e.memoizedState=null,l)}function Cc(t,e){return e=Du({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Du(t,e){return t=fe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function jc(t,e,l){return Ta(e,t.child,null,l),t=Cc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function gf(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Ii(t.return,e,l)}function Hc(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function yf(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(Wt(t,e,a.children,l),a=Qt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&gf(t,l,e);else if(t.tag===19)gf(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(B(Qt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Nu(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Hc(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Nu(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Hc(e,!0,l,null,u);break;case"together":Hc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Je(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),pl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(nn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,l=Xe(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Xe(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Bc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&fu(t)))}function rv(t,e,l){switch(e.tag){case 3:Rt(e,e.stateNode.containerInfo),rl(e,Lt,t.memoizedState.cache),ln();break;case 27:case 5:ll(e);break;case 4:Rt(e,e.stateNode.containerInfo);break;case 10:rl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(ml(e),e.flags|=128,null):(l&e.child.childLanes)!==0?vf(t,e,l):(ml(e),t=Je(t,e,l),t!==null?t.sibling:null);ml(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(nn(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return yf(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),B(Qt,Qt.current),a)break;return null;case 22:case 23:return e.lanes=0,sf(t,e,l);case 24:rl(e,Lt,t.memoizedState.cache)}return Je(t,e,l)}function bf(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)kt=!0;else{if(!Bc(t,l)&&(e.flags&128)===0)return kt=!1,rv(t,e,l);kt=(t.flags&131072)!==0}else kt=!1,yt&&(e.flags&1048576)!==0&&Ko(e,su,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")ki(a)?(t=Vl(a,t),e.tag=1,e=mf(null,e,a,t,l)):(e.tag=0,e=Oc(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===et){e.tag=11,e=cf(null,e,a,t,l);break t}else if(n===Nt){e.tag=14,e=rf(null,e,a,t,l);break t}}throw e=Mt(a)||a,Error(o(306,e,""))}}return e;case 0:return Oc(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Vl(a,e.pendingProps),mf(t,e,a,n,l);case 3:t:{if(Rt(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,ic(t,e),dn(e,a,null,l);var i=e.memoizedState;if(a=i.cache,rl(e,Lt,a),a!==u.cache&&tc(e,[Lt],l,!0),fn(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=hf(t,e,a,l);break t}else if(a!==n){n=pe(Error(o(424)),e),an(n),e=hf(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ht=Re(t.firstChild),ee=e,yt=!0,ql=null,we=!0,l=Ws(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(ln(),a===n){e=Je(t,e,l);break t}Wt(t,e,a,l)}e=e.child}return e;case 26:return Ou(t,e),t===null?(l=Ad(e.type,null,e.pendingProps,null))?e.memoizedState=l:yt||(l=e.type,t=e.pendingProps,a=Zu(I.current).createElement(l),a[Pt]=e,a[le]=t,Ft(a,l,t),Zt(a),e.stateNode=a):e.memoizedState=Ad(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return ll(e),t===null&&yt&&(a=e.stateNode=pd(e.type,e.pendingProps,I.current),ee=e,we=!0,n=Ht,Tl(e.type)?(yr=n,Ht=Re(a.firstChild)):Ht=n),Wt(t,e,e.pendingProps.children,l),Ou(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&yt&&((n=a=Ht)&&(a=Hv(a,e.type,e.pendingProps,we),a!==null?(e.stateNode=a,ee=e,Ht=Re(a.firstChild),we=!1,n=!0):n=!1),n||Yl(e)),ll(e),n=e.type,u=e.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,mr(n,u)?a=null:i!==null&&mr(n,i)&&(e.flags|=32),e.memoizedState!==null&&(n=dc(t,e,tv,null,null,l),Cn._currentValue=n),Ou(t,e),Wt(t,e,a,l),e.child;case 6:return t===null&&yt&&((t=l=Ht)&&(l=Bv(l,e.pendingProps,we),l!==null?(e.stateNode=l,ee=e,Ht=null,t=!0):t=!1),t||Yl(e)),null;case 13:return vf(t,e,l);case 4:return Rt(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ta(e,null,a,l):Wt(t,e,a,l),e.child;case 11:return cf(t,e,e.type,e.pendingProps,l);case 7:return Wt(t,e,e.pendingProps,l),e.child;case 8:return Wt(t,e,e.pendingProps.children,l),e.child;case 12:return Wt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,rl(e,e.type,a.value),Wt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Xl(e),n=It(n),a=a(n),e.flags|=1,Wt(t,e,a,l),e.child;case 14:return rf(t,e,e.type,e.pendingProps,l);case 15:return of(t,e,e.type,e.pendingProps,l);case 19:return yf(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Du(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Xe(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return sf(t,e,l);case 24:return Xl(e),a=It(Lt),t===null?(n=ac(),n===null&&(n=Dt,u=ec(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},uc(e),rl(e,Lt,n)):((t.lanes&l)!==0&&(ic(t,e),dn(e,null,null,l),fn()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),rl(e,Lt,a)):(a=u.cache,rl(e,Lt,a),a!==n.cache&&tc(e,[Lt],l,!0))),Wt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function We(t){t.flags|=4}function pf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Nd(e)){if(e=Te.current,e!==null&&((ht&4194048)===ht?Ue!==null:(ht&62914560)!==ht&&(ht&536870912)===0||e!==Ue))throw on=nc,es;t.flags|=8192}}function wu(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Wr():536870912,t.lanes|=e,Na|=e)}function pn(t,e){if(!yt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Ct(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function ov(t,e,l){var a=e.pendingProps;switch($i(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ct(e),null;case 1:return Ct(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Ze(Lt),ce(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(en(e)?We(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,$o())),Ct(e),null;case 26:return l=e.memoizedState,t===null?(We(e),l!==null?(Ct(e),pf(e,l)):(Ct(e),e.flags&=-16777217)):l?l!==t.memoizedState?(We(e),Ct(e),pf(e,l)):(Ct(e),e.flags&=-16777217):(t.memoizedProps!==a&&We(e),Ct(e),e.flags&=-16777217),null;case 27:al(e),l=I.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Ct(e),null}t=X.current,en(e)?Jo(e):(t=pd(n,a,l),e.stateNode=t,We(e))}return Ct(e),null;case 5:if(al(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Ct(e),null}if(t=X.current,en(e))Jo(e);else{switch(n=Zu(I.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[Pt]=e,t[le]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(Ft(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&We(e)}}return Ct(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=I.current,en(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=ee,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[Pt]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||dd(t.nodeValue,l)),t||Yl(e)}else t=Zu(t).createTextNode(a),t[Pt]=e,e.stateNode=t}return Ct(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=en(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(o(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(o(317));n[Pt]=e}else ln(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ct(e),n=!1}else n=$o(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ke(e),e):(Ke(e),null)}if(Ke(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),wu(e,e.updateQueue),Ct(e),null;case 4:return ce(),t===null&&rr(e.stateNode.containerInfo),Ct(e),null;case 10:return Ze(e.type),Ct(e),null;case 19:if(Y(Qt),n=e.memoizedState,n===null)return Ct(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)pn(n,!1);else{if(Bt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Nu(t),u!==null){for(e.flags|=128,pn(n,!1),t=u.updateQueue,e.updateQueue=t,wu(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)ko(l,t),l=l.sibling;return B(Qt,Qt.current&1|2),e.child}t=t.sibling}n.tail!==null&&De()>ju&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Nu(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,wu(e,t),pn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!yt)return Ct(e),null}else 2*De()-n.renderingStartTime>ju&&l!==536870912&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=De(),e.sibling=null,t=Qt.current,B(Qt,a?t&1|2:t&1),e):(Ct(e),null);case 22:case 23:return Ke(e),sc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Ct(e),e.subtreeFlags&6&&(e.flags|=8192)):Ct(e),l=e.updateQueue,l!==null&&wu(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Y(Ll),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ze(Lt),Ct(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function sv(t,e){switch($i(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ze(Lt),ce(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return al(e),null;case 13:if(Ke(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));ln()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(Qt),null;case 4:return ce(),null;case 10:return Ze(e.type),null;case 22:case 23:return Ke(e),sc(),t!==null&&Y(Ll),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ze(Lt),null;case 25:return null;default:return null}}function Sf(t,e){switch($i(e),e.tag){case 3:Ze(Lt),ce();break;case 26:case 27:case 5:al(e);break;case 4:ce();break;case 13:Ke(e);break;case 19:Y(Qt);break;case 10:Ze(e.type);break;case 22:case 23:Ke(e),sc(),t!==null&&Y(Ll);break;case 24:Ze(Lt)}}function Sn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(r){Ot(e,e.return,r)}}function vl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,r=i.destroy;if(r!==void 0){i.destroy=void 0,n=e;var d=l,x=r;try{x()}catch(R){Ot(n,d,R)}}}a=a.next}while(a!==u)}}catch(R){Ot(e,e.return,R)}}function xf(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{cs(e,l)}catch(a){Ot(t,t.return,a)}}}function Af(t,e,l){l.props=Vl(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Ot(t,e,a)}}function xn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Ot(t,e,n)}}function Ce(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Ot(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Ot(t,e,n)}else l.current=null}function Tf(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Ot(t,t.return,n)}}function qc(t,e,l){try{var a=t.stateNode;Dv(a,t.type,l,e),a[le]=e}catch(n){Ot(t,t.return,n)}}function Ef(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Tl(t.type)||t.tag===4}function Yc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Ef(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Tl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Gc(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Vu));else if(a!==4&&(a===27&&Tl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Gc(t,e,l),t=t.sibling;t!==null;)Gc(t,e,l),t=t.sibling}function Uu(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&Tl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Uu(t,e,l),t=t.sibling;t!==null;)Uu(t,e,l),t=t.sibling}function zf(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Ft(e,a,l),e[Pt]=t,e[le]=l}catch(u){Ot(t,t.return,u)}}var $e=!1,Yt=!1,Xc=!1,_f=typeof WeakSet=="function"?WeakSet:Set,Kt=null;function fv(t,e){if(t=t.containerInfo,fr=Fu,t=Ho(t),Yi(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var i=0,r=-1,d=-1,x=0,R=0,D=t,A=null;e:for(;;){for(var E;D!==l||n!==0&&D.nodeType!==3||(r=i+n),D!==u||a!==0&&D.nodeType!==3||(d=i+a),D.nodeType===3&&(i+=D.nodeValue.length),(E=D.firstChild)!==null;)A=D,D=E;for(;;){if(D===t)break e;if(A===l&&++x===n&&(r=i),A===u&&++R===a&&(d=i),(E=D.nextSibling)!==null)break;D=A,A=D.parentNode}D=E}l=r===-1||d===-1?null:{start:r,end:d}}else l=null}l=l||{start:0,end:0}}else l=null;for(dr={focusedElem:t,selectionRange:l},Fu=!1,Kt=e;Kt!==null;)if(e=Kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Kt=t;else for(;Kt!==null;){switch(e=Kt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var F=Vl(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(F,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(W){Ot(l,l.return,W)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)vr(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":vr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,Kt=t;break}Kt=e.return}}function Nf(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:gl(t,l),a&4&&Sn(5,l);break;case 1:if(gl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(i){Ot(l,l.return,i)}else{var n=Vl(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(i){Ot(l,l.return,i)}}a&64&&xf(l),a&512&&xn(l,l.return);break;case 3:if(gl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{cs(t,e)}catch(i){Ot(l,l.return,i)}}break;case 27:e===null&&a&4&&zf(l);case 26:case 5:gl(t,l),e===null&&a&4&&Tf(l),a&512&&xn(l,l.return);break;case 12:gl(t,l);break;case 13:gl(t,l),a&4&&Of(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Sv.bind(null,l),qv(t,l))));break;case 22:if(a=l.memoizedState!==null||$e,!a){e=e!==null&&e.memoizedState!==null||Yt,n=$e;var u=Yt;$e=a,(Yt=e)&&!u?yl(t,l,(l.subtreeFlags&8772)!==0):gl(t,l),$e=n,Yt=u}break;case 30:break;default:gl(t,l)}}function Mf(t){var e=t.alternate;e!==null&&(t.alternate=null,Mf(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Si(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var wt=null,ue=!1;function Fe(t,e,l){for(l=l.child;l!==null;)Rf(t,e,l),l=l.sibling}function Rf(t,e,l){if(re&&typeof re.onCommitFiberUnmount=="function")try{re.onCommitFiberUnmount(Xa,l)}catch{}switch(l.tag){case 26:Yt||Ce(l,e),Fe(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Yt||Ce(l,e);var a=wt,n=ue;Tl(l.type)&&(wt=l.stateNode,ue=!1),Fe(t,e,l),On(l.stateNode),wt=a,ue=n;break;case 5:Yt||Ce(l,e);case 6:if(a=wt,n=ue,wt=null,Fe(t,e,l),wt=a,ue=n,wt!==null)if(ue)try{(wt.nodeType===9?wt.body:wt.nodeName==="HTML"?wt.ownerDocument.body:wt).removeChild(l.stateNode)}catch(u){Ot(l,e,u)}else try{wt.removeChild(l.stateNode)}catch(u){Ot(l,e,u)}break;case 18:wt!==null&&(ue?(t=wt,yd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),qn(t)):yd(wt,l.stateNode));break;case 4:a=wt,n=ue,wt=l.stateNode.containerInfo,ue=!0,Fe(t,e,l),wt=a,ue=n;break;case 0:case 11:case 14:case 15:Yt||vl(2,l,e),Yt||vl(4,l,e),Fe(t,e,l);break;case 1:Yt||(Ce(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Af(l,e,a)),Fe(t,e,l);break;case 21:Fe(t,e,l);break;case 22:Yt=(a=Yt)||l.memoizedState!==null,Fe(t,e,l),Yt=a;break;default:Fe(t,e,l)}}function Of(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{qn(t)}catch(l){Ot(e,e.return,l)}}function dv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new _f),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new _f),e;default:throw Error(o(435,t.tag))}}function Lc(t,e){var l=dv(t);e.forEach(function(a){var n=xv.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function de(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,i=e,r=i;t:for(;r!==null;){switch(r.tag){case 27:if(Tl(r.type)){wt=r.stateNode,ue=!1;break t}break;case 5:wt=r.stateNode,ue=!1;break t;case 3:case 4:wt=r.stateNode.containerInfo,ue=!0;break t}r=r.return}if(wt===null)throw Error(o(160));Rf(u,i,n),wt=null,ue=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Df(e,t),e=e.sibling}var Me=null;function Df(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:de(e,t),me(t),a&4&&(vl(3,t,t.return),Sn(3,t),vl(5,t,t.return));break;case 1:de(e,t),me(t),a&512&&(Yt||l===null||Ce(l,l.return)),a&64&&$e&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Me;if(de(e,t),me(t),a&512&&(Yt||l===null||Ce(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Va]||u[Pt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Ft(u,a,l),u[Pt]=t,Zt(u),a=u;break t;case"link":var i=zd("link","href",n).get(a+(l.href||""));if(i){for(var r=0;r<i.length;r++)if(u=i[r],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(r,1);break e}}u=n.createElement(a),Ft(u,a,l),n.head.appendChild(u);break;case"meta":if(i=zd("meta","content",n).get(a+(l.content||""))){for(r=0;r<i.length;r++)if(u=i[r],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(r,1);break e}}u=n.createElement(a),Ft(u,a,l),n.head.appendChild(u);break;default:throw Error(o(468,a))}u[Pt]=t,Zt(u),a=u}t.stateNode=a}else _d(n,t.type,t.stateNode);else t.stateNode=Ed(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?_d(n,t.type,t.stateNode):Ed(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&qc(t,t.memoizedProps,l.memoizedProps)}break;case 27:de(e,t),me(t),a&512&&(Yt||l===null||Ce(l,l.return)),l!==null&&a&4&&qc(t,t.memoizedProps,l.memoizedProps);break;case 5:if(de(e,t),me(t),a&512&&(Yt||l===null||Ce(l,l.return)),t.flags&32){n=t.stateNode;try{ua(n,"")}catch(E){Ot(t,t.return,E)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,qc(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Xc=!0);break;case 6:if(de(e,t),me(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(E){Ot(t,t.return,E)}}break;case 3:if(Ju=null,n=Me,Me=ku(e.containerInfo),de(e,t),Me=n,me(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{qn(e.containerInfo)}catch(E){Ot(t,t.return,E)}Xc&&(Xc=!1,wf(t));break;case 4:a=Me,Me=ku(t.stateNode.containerInfo),de(e,t),me(t),Me=a;break;case 12:de(e,t),me(t);break;case 13:de(e,t),me(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Jc=De()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Lc(t,a)));break;case 22:n=t.memoizedState!==null;var d=l!==null&&l.memoizedState!==null,x=$e,R=Yt;if($e=x||n,Yt=R||d,de(e,t),Yt=R,$e=x,me(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||d||$e||Yt||Zl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){d=l=e;try{if(u=d.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{r=d.stateNode;var D=d.memoizedProps.style,A=D!=null&&D.hasOwnProperty("display")?D.display:null;r.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(E){Ot(d,d.return,E)}}}else if(e.tag===6){if(l===null){d=e;try{d.stateNode.nodeValue=n?"":d.memoizedProps}catch(E){Ot(d,d.return,E)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Lc(t,l))));break;case 19:de(e,t),me(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Lc(t,a)));break;case 30:break;case 21:break;default:de(e,t),me(t)}}function me(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Ef(a)){l=a;break}a=a.return}if(l==null)throw Error(o(160));switch(l.tag){case 27:var n=l.stateNode,u=Yc(t);Uu(t,u,n);break;case 5:var i=l.stateNode;l.flags&32&&(ua(i,""),l.flags&=-33);var r=Yc(t);Uu(t,r,i);break;case 3:case 4:var d=l.stateNode.containerInfo,x=Yc(t);Gc(t,x,d);break;default:throw Error(o(161))}}catch(R){Ot(t,t.return,R)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function wf(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;wf(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function gl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Nf(t,e.alternate,e),e=e.sibling}function Zl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:vl(4,e,e.return),Zl(e);break;case 1:Ce(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Af(e,e.return,l),Zl(e);break;case 27:On(e.stateNode);case 26:case 5:Ce(e,e.return),Zl(e);break;case 22:e.memoizedState===null&&Zl(e);break;case 30:Zl(e);break;default:Zl(e)}t=t.sibling}}function yl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,i=u.flags;switch(u.tag){case 0:case 11:case 15:yl(n,u,l),Sn(4,u);break;case 1:if(yl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(x){Ot(a,a.return,x)}if(a=u,n=a.updateQueue,n!==null){var r=a.stateNode;try{var d=n.shared.hiddenCallbacks;if(d!==null)for(n.shared.hiddenCallbacks=null,n=0;n<d.length;n++)is(d[n],r)}catch(x){Ot(a,a.return,x)}}l&&i&64&&xf(u),xn(u,u.return);break;case 27:zf(u);case 26:case 5:yl(n,u,l),l&&a===null&&i&4&&Tf(u),xn(u,u.return);break;case 12:yl(n,u,l);break;case 13:yl(n,u,l),l&&i&4&&Of(n,u);break;case 22:u.memoizedState===null&&yl(n,u,l),xn(u,u.return);break;case 30:break;default:yl(n,u,l)}e=e.sibling}}function Qc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&un(l))}function Vc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t))}function je(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Uf(t,e,l,a),e=e.sibling}function Uf(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:je(t,e,l,a),n&2048&&Sn(9,e);break;case 1:je(t,e,l,a);break;case 3:je(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t)));break;case 12:if(n&2048){je(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,i=u.id,r=u.onPostCommit;typeof r=="function"&&r(i,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(d){Ot(e,e.return,d)}}else je(t,e,l,a);break;case 13:je(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,i=e.alternate,e.memoizedState!==null?u._visibility&2?je(t,e,l,a):An(t,e):u._visibility&2?je(t,e,l,a):(u._visibility|=2,Ea(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Qc(i,e);break;case 24:je(t,e,l,a),n&2048&&Vc(e.alternate,e);break;default:je(t,e,l,a)}}function Ea(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,i=e,r=l,d=a,x=i.flags;switch(i.tag){case 0:case 11:case 15:Ea(u,i,r,d,n),Sn(8,i);break;case 23:break;case 22:var R=i.stateNode;i.memoizedState!==null?R._visibility&2?Ea(u,i,r,d,n):An(u,i):(R._visibility|=2,Ea(u,i,r,d,n)),n&&x&2048&&Qc(i.alternate,i);break;case 24:Ea(u,i,r,d,n),n&&x&2048&&Vc(i.alternate,i);break;default:Ea(u,i,r,d,n)}e=e.sibling}}function An(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:An(l,a),n&2048&&Qc(a.alternate,a);break;case 24:An(l,a),n&2048&&Vc(a.alternate,a);break;default:An(l,a)}e=e.sibling}}var Tn=8192;function za(t){if(t.subtreeFlags&Tn)for(t=t.child;t!==null;)Cf(t),t=t.sibling}function Cf(t){switch(t.tag){case 26:za(t),t.flags&Tn&&t.memoizedState!==null&&Fv(Me,t.memoizedState,t.memoizedProps);break;case 5:za(t);break;case 3:case 4:var e=Me;Me=ku(t.stateNode.containerInfo),za(t),Me=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Tn,Tn=16777216,za(t),Tn=e):za(t));break;default:za(t)}}function jf(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function En(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Kt=a,Bf(a,t)}jf(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hf(t),t=t.sibling}function Hf(t){switch(t.tag){case 0:case 11:case 15:En(t),t.flags&2048&&vl(9,t,t.return);break;case 3:En(t);break;case 12:En(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Cu(t)):En(t);break;default:En(t)}}function Cu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Kt=a,Bf(a,t)}jf(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:vl(8,e,e.return),Cu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Cu(e));break;default:Cu(e)}t=t.sibling}}function Bf(t,e){for(;Kt!==null;){var l=Kt;switch(l.tag){case 0:case 11:case 15:vl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:un(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Kt=a;else t:for(l=t;Kt!==null;){a=Kt;var n=a.sibling,u=a.return;if(Mf(a),a===l){Kt=null;break t}if(n!==null){n.return=u,Kt=n;break t}Kt=u}}}var mv={getCacheForType:function(t){var e=It(Lt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},hv=typeof WeakMap=="function"?WeakMap:Map,xt=0,Dt=null,dt=null,ht=0,At=0,he=null,bl=!1,_a=!1,Zc=!1,Pe=0,Bt=0,pl=0,kl=0,kc=0,Ee=0,Na=0,zn=null,ie=null,Kc=!1,Jc=0,ju=1/0,Hu=null,Sl=null,$t=0,xl=null,Ma=null,Ra=0,Wc=0,$c=null,qf=null,_n=0,Fc=null;function ve(){if((xt&2)!==0&&ht!==0)return ht&-ht;if(_.T!==null){var t=ga;return t!==0?t:nr()}return Pr()}function Yf(){Ee===0&&(Ee=(ht&536870912)===0||yt?Jr():536870912);var t=Te.current;return t!==null&&(t.flags|=32),Ee}function ge(t,e,l){(t===Dt&&(At===2||At===9)||t.cancelPendingCommit!==null)&&(Oa(t,0),Al(t,ht,Ee,!1)),Qa(t,l),((xt&2)===0||t!==Dt)&&(t===Dt&&((xt&2)===0&&(kl|=l),Bt===4&&Al(t,ht,Ee,!1)),He(t))}function Gf(t,e,l){if((xt&6)!==0)throw Error(o(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||La(t,e),n=a?yv(t,e):tr(t,e,!0),u=a;do{if(n===0){_a&&!a&&Al(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!vv(l)){n=tr(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){e=i;t:{var r=t;n=zn;var d=r.current.memoizedState.isDehydrated;if(d&&(Oa(r,i).flags|=256),i=tr(r,i,!1),i!==2){if(Zc&&!d){r.errorRecoveryDisabledLanes|=u,kl|=u,n=4;break t}u=ie,ie=n,u!==null&&(ie===null?ie=u:ie.push.apply(ie,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){Oa(t,0),Al(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Al(a,e,Ee,!bl);break t;case 2:ie=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(n=Jc+300-De(),10<n)){if(Al(a,e,Ee,!bl),Kn(a,0,!0)!==0)break t;a.timeoutHandle=vd(Xf.bind(null,a,l,ie,Hu,Kc,e,Ee,kl,Na,bl,u,2,-0,0),n);break t}Xf(a,l,ie,Hu,Kc,e,Ee,kl,Na,bl,u,0,-0,0)}}break}while(!0);He(t)}function Xf(t,e,l,a,n,u,i,r,d,x,R,D,A,E){if(t.timeoutHandle=-1,D=e.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(Un={stylesheets:null,count:0,unsuspend:$v},Cf(e),D=Pv(),D!==null)){t.cancelPendingCommit=D(Jf.bind(null,t,e,u,l,a,n,i,r,d,R,1,A,E)),Al(t,u,i,!x);return}Jf(t,e,u,l,a,n,i,r,d)}function vv(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!se(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Al(t,e,l,a){e&=~kc,e&=~kl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-oe(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&$r(t,l,e)}function Bu(){return(xt&6)===0?(Nn(0),!1):!0}function Pc(){if(dt!==null){if(At===0)var t=dt.return;else t=dt,Ve=Gl=null,vc(t),Aa=null,yn=0,t=dt;for(;t!==null;)Sf(t.alternate,t),t=t.return;dt=null}}function Oa(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Uv(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Pc(),Dt=t,dt=l=Xe(t.current,null),ht=e,At=0,he=null,bl=!1,_a=La(t,e),Zc=!1,Na=Ee=kc=kl=pl=Bt=0,ie=zn=null,Kc=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-oe(a),u=1<<n;e|=t[n],a&=~u}return Pe=e,uu(),l}function Lf(t,e){ut=null,_.H=Eu,e===rn||e===hu?(e=ns(),At=3):e===es?(e=ns(),At=4):At=e===uf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,he=e,dt===null&&(Bt=1,Ru(t,pe(e,t.current)))}function Qf(){var t=_.H;return _.H=Eu,t===null?Eu:t}function Vf(){var t=_.A;return _.A=mv,t}function Ic(){Bt=4,bl||(ht&4194048)!==ht&&Te.current!==null||(_a=!0),(pl&134217727)===0&&(kl&134217727)===0||Dt===null||Al(Dt,ht,Ee,!1)}function tr(t,e,l){var a=xt;xt|=2;var n=Qf(),u=Vf();(Dt!==t||ht!==e)&&(Hu=null,Oa(t,e)),e=!1;var i=Bt;t:do try{if(At!==0&&dt!==null){var r=dt,d=he;switch(At){case 8:Pc(),i=6;break t;case 3:case 2:case 9:case 6:Te.current===null&&(e=!0);var x=At;if(At=0,he=null,Da(t,r,d,x),l&&_a){i=0;break t}break;default:x=At,At=0,he=null,Da(t,r,d,x)}}gv(),i=Bt;break}catch(R){Lf(t,R)}while(!0);return e&&t.shellSuspendCounter++,Ve=Gl=null,xt=a,_.H=n,_.A=u,dt===null&&(Dt=null,ht=0,uu()),i}function gv(){for(;dt!==null;)Zf(dt)}function yv(t,e){var l=xt;xt|=2;var a=Qf(),n=Vf();Dt!==t||ht!==e?(Hu=null,ju=De()+500,Oa(t,e)):_a=La(t,e);t:do try{if(At!==0&&dt!==null){e=dt;var u=he;e:switch(At){case 1:At=0,he=null,Da(t,e,u,1);break;case 2:case 9:if(ls(u)){At=0,he=null,kf(e);break}e=function(){At!==2&&At!==9||Dt!==t||(At=7),He(t)},u.then(e,e);break t;case 3:At=7;break t;case 4:At=5;break t;case 7:ls(u)?(At=0,he=null,kf(e)):(At=0,he=null,Da(t,e,u,7));break;case 5:var i=null;switch(dt.tag){case 26:i=dt.memoizedState;case 5:case 27:var r=dt;if(!i||Nd(i)){At=0,he=null;var d=r.sibling;if(d!==null)dt=d;else{var x=r.return;x!==null?(dt=x,qu(x)):dt=null}break e}}At=0,he=null,Da(t,e,u,5);break;case 6:At=0,he=null,Da(t,e,u,6);break;case 8:Pc(),Bt=6;break t;default:throw Error(o(462))}}bv();break}catch(R){Lf(t,R)}while(!0);return Ve=Gl=null,_.H=a,_.A=n,xt=l,dt!==null?0:(Dt=null,ht=0,uu(),Bt)}function bv(){for(;dt!==null&&!Gm();)Zf(dt)}function Zf(t){var e=bf(t.alternate,t,Pe);t.memoizedProps=t.pendingProps,e===null?qu(t):dt=e}function kf(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=df(l,e,e.pendingProps,e.type,void 0,ht);break;case 11:e=df(l,e,e.pendingProps,e.type.render,e.ref,ht);break;case 5:vc(e);default:Sf(l,e),e=dt=ko(e,Pe),e=bf(l,e,Pe)}t.memoizedProps=t.pendingProps,e===null?qu(t):dt=e}function Da(t,e,l,a){Ve=Gl=null,vc(e),Aa=null,yn=0;var n=e.return;try{if(cv(t,n,e,l,ht)){Bt=1,Ru(t,pe(l,t.current)),dt=null;return}}catch(u){if(n!==null)throw dt=n,u;Bt=1,Ru(t,pe(l,t.current)),dt=null;return}e.flags&32768?(yt||a===1?t=!0:_a||(ht&536870912)!==0?t=!1:(bl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Te.current,a!==null&&a.tag===13&&(a.flags|=16384))),Kf(e,t)):qu(e)}function qu(t){var e=t;do{if((e.flags&32768)!==0){Kf(e,bl);return}t=e.return;var l=ov(e.alternate,e,Pe);if(l!==null){dt=l;return}if(e=e.sibling,e!==null){dt=e;return}dt=e=t}while(e!==null);Bt===0&&(Bt=5)}function Kf(t,e){do{var l=sv(t.alternate,t);if(l!==null){l.flags&=32767,dt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){dt=t;return}dt=t=l}while(t!==null);Bt=6,dt=null}function Jf(t,e,l,a,n,u,i,r,d){t.cancelPendingCommit=null;do Yu();while($t!==0);if((xt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(u=e.lanes|e.childLanes,u|=Vi,$m(t,l,u,i,r,d),t===Dt&&(dt=Dt=null,ht=0),Ma=e,xl=t,Ra=l,Wc=u,$c=n,qf=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Av(Vn,function(){return If(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=_.T,_.T=null,n=q.p,q.p=2,i=xt,xt|=4;try{fv(t,e,l)}finally{xt=i,q.p=n,_.T=a}}$t=1,Wf(),$f(),Ff()}}function Wf(){if($t===1){$t=0;var t=xl,e=Ma,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=_.T,_.T=null;var a=q.p;q.p=2;var n=xt;xt|=4;try{Df(e,t);var u=dr,i=Ho(t.containerInfo),r=u.focusedElem,d=u.selectionRange;if(i!==r&&r&&r.ownerDocument&&jo(r.ownerDocument.documentElement,r)){if(d!==null&&Yi(r)){var x=d.start,R=d.end;if(R===void 0&&(R=x),"selectionStart"in r)r.selectionStart=x,r.selectionEnd=Math.min(R,r.value.length);else{var D=r.ownerDocument||document,A=D&&D.defaultView||window;if(A.getSelection){var E=A.getSelection(),F=r.textContent.length,W=Math.min(d.start,F),_t=d.end===void 0?W:Math.min(d.end,F);!E.extend&&W>_t&&(i=_t,_t=W,W=i);var y=Co(r,W),v=Co(r,_t);if(y&&v&&(E.rangeCount!==1||E.anchorNode!==y.node||E.anchorOffset!==y.offset||E.focusNode!==v.node||E.focusOffset!==v.offset)){var S=D.createRange();S.setStart(y.node,y.offset),E.removeAllRanges(),W>_t?(E.addRange(S),E.extend(v.node,v.offset)):(S.setEnd(v.node,v.offset),E.addRange(S))}}}}for(D=[],E=r;E=E.parentNode;)E.nodeType===1&&D.push({element:E,left:E.scrollLeft,top:E.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<D.length;r++){var O=D[r];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}Fu=!!fr,dr=fr=null}finally{xt=n,q.p=a,_.T=l}}t.current=e,$t=2}}function $f(){if($t===2){$t=0;var t=xl,e=Ma,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=_.T,_.T=null;var a=q.p;q.p=2;var n=xt;xt|=4;try{Nf(t,e.alternate,e)}finally{xt=n,q.p=a,_.T=l}}$t=3}}function Ff(){if($t===4||$t===3){$t=0,Xm();var t=xl,e=Ma,l=Ra,a=qf;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?$t=5:($t=0,Ma=xl=null,Pf(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Sl=null),bi(l),e=e.stateNode,re&&typeof re.onCommitFiberRoot=="function")try{re.onCommitFiberRoot(Xa,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=_.T,n=q.p,q.p=2,_.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var r=a[i];u(r.value,{componentStack:r.stack})}}finally{_.T=e,q.p=n}}(Ra&3)!==0&&Yu(),He(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Fc?_n++:(_n=0,Fc=t):_n=0,Nn(0)}}function Pf(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,un(e)))}function Yu(t){return Wf(),$f(),Ff(),If()}function If(){if($t!==5)return!1;var t=xl,e=Wc;Wc=0;var l=bi(Ra),a=_.T,n=q.p;try{q.p=32>l?32:l,_.T=null,l=$c,$c=null;var u=xl,i=Ra;if($t=0,Ma=xl=null,Ra=0,(xt&6)!==0)throw Error(o(331));var r=xt;if(xt|=4,Hf(u.current),Uf(u,u.current,i,l),xt=r,Nn(0,!1),re&&typeof re.onPostCommitFiberRoot=="function")try{re.onPostCommitFiberRoot(Xa,u)}catch{}return!0}finally{q.p=n,_.T=a,Pf(t,e)}}function td(t,e,l){e=pe(l,e),e=Rc(t.stateNode,e,2),t=fl(t,e,2),t!==null&&(Qa(t,2),He(t))}function Ot(t,e,l){if(t.tag===3)td(t,t,l);else for(;e!==null;){if(e.tag===3){td(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Sl===null||!Sl.has(a))){t=pe(l,t),l=af(2),a=fl(e,l,2),a!==null&&(nf(l,a,e,t),Qa(a,2),He(a));break}}e=e.return}}function er(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new hv;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Zc=!0,n.add(l),t=pv.bind(null,t,e,l),e.then(t,t))}function pv(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Dt===t&&(ht&l)===l&&(Bt===4||Bt===3&&(ht&62914560)===ht&&300>De()-Jc?(xt&2)===0&&Oa(t,0):kc|=l,Na===ht&&(Na=0)),He(t)}function ed(t,e){e===0&&(e=Wr()),t=da(t,e),t!==null&&(Qa(t,e),He(t))}function Sv(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),ed(t,l)}function xv(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),ed(t,l)}function Av(t,e){return hi(t,e)}var Gu=null,wa=null,lr=!1,Xu=!1,ar=!1,Kl=0;function He(t){t!==wa&&t.next===null&&(wa===null?Gu=wa=t:wa=wa.next=t),Xu=!0,lr||(lr=!0,Ev())}function Nn(t,e){if(!ar&&Xu){ar=!0;do for(var l=!1,a=Gu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,r=a.pingedLanes;u=(1<<31-oe(42|t)+1)-1,u&=n&~(i&~r),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,ud(a,u))}else u=ht,u=Kn(a,a===Dt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||La(a,u)||(l=!0,ud(a,u));a=a.next}while(l);ar=!1}}function Tv(){ld()}function ld(){Xu=lr=!1;var t=0;Kl!==0&&(wv()&&(t=Kl),Kl=0);for(var e=De(),l=null,a=Gu;a!==null;){var n=a.next,u=ad(a,e);u===0?(a.next=null,l===null?Gu=n:l.next=n,n===null&&(wa=l)):(l=a,(t!==0||(u&3)!==0)&&(Xu=!0)),a=n}Nn(t)}function ad(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-oe(u),r=1<<i,d=n[i];d===-1?((r&l)===0||(r&a)!==0)&&(n[i]=Wm(r,e)):d<=e&&(t.expiredLanes|=r),u&=~r}if(e=Dt,l=ht,l=Kn(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(At===2||At===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&vi(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||La(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&vi(a),bi(l)){case 2:case 8:l=kr;break;case 32:l=Vn;break;case 268435456:l=Kr;break;default:l=Vn}return a=nd.bind(null,t),l=hi(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&vi(a),t.callbackPriority=2,t.callbackNode=null,2}function nd(t,e){if($t!==0&&$t!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Yu()&&t.callbackNode!==l)return null;var a=ht;return a=Kn(t,t===Dt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Gf(t,a,e),ad(t,De()),t.callbackNode!=null&&t.callbackNode===l?nd.bind(null,t):null)}function ud(t,e){if(Yu())return null;Gf(t,e,!0)}function Ev(){Cv(function(){(xt&6)!==0?hi(Zr,Tv):ld()})}function nr(){return Kl===0&&(Kl=Jr()),Kl}function id(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Pn(""+t)}function cd(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function zv(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=id((n[le]||null).action),i=a.submitter;i&&(e=(e=i[le]||null)?id(e.formAction):i.getAttribute("formAction"),e!==null&&(u=e,i=null));var r=new lu("action","action",null,a,n);t.push({event:r,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Kl!==0){var d=i?cd(n,i):new FormData(n);Ec(l,{pending:!0,data:d,method:n.method,action:u},null,d)}}else typeof u=="function"&&(r.preventDefault(),d=i?cd(n,i):new FormData(n),Ec(l,{pending:!0,data:d,method:n.method,action:u},u,d))},currentTarget:n}]})}}for(var ur=0;ur<Qi.length;ur++){var ir=Qi[ur],_v=ir.toLowerCase(),Nv=ir[0].toUpperCase()+ir.slice(1);Ne(_v,"on"+Nv)}Ne(Yo,"onAnimationEnd"),Ne(Go,"onAnimationIteration"),Ne(Xo,"onAnimationStart"),Ne("dblclick","onDoubleClick"),Ne("focusin","onFocus"),Ne("focusout","onBlur"),Ne(Vh,"onTransitionRun"),Ne(Zh,"onTransitionStart"),Ne(kh,"onTransitionCancel"),Ne(Lo,"onTransitionEnd"),la("onMouseEnter",["mouseout","mouseover"]),la("onMouseLeave",["mouseout","mouseover"]),la("onPointerEnter",["pointerout","pointerover"]),la("onPointerLeave",["pointerout","pointerover"]),Dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Mv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Mn));function rd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var i=a.length-1;0<=i;i--){var r=a[i],d=r.instance,x=r.currentTarget;if(r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=x;try{u(n)}catch(R){Mu(R)}n.currentTarget=null,u=d}else for(i=0;i<a.length;i++){if(r=a[i],d=r.instance,x=r.currentTarget,r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=x;try{u(n)}catch(R){Mu(R)}n.currentTarget=null,u=d}}}}function mt(t,e){var l=e[pi];l===void 0&&(l=e[pi]=new Set);var a=t+"__bubble";l.has(a)||(od(e,t,2,!1),l.add(a))}function cr(t,e,l){var a=0;e&&(a|=4),od(l,t,a,e)}var Lu="_reactListening"+Math.random().toString(36).slice(2);function rr(t){if(!t[Lu]){t[Lu]=!0,to.forEach(function(l){l!=="selectionchange"&&(Mv.has(l)||cr(l,!1,t),cr(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Lu]||(e[Lu]=!0,cr("selectionchange",!1,e))}}function od(t,e,l,a){switch(Ud(e)){case 2:var n=e0;break;case 8:n=l0;break;default:n=Ar}l=n.bind(null,e,l,t),n=void 0,!Oi||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function or(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var r=a.stateNode.containerInfo;if(r===n)break;if(i===4)for(i=a.return;i!==null;){var d=i.tag;if((d===3||d===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;r!==null;){if(i=Il(r),i===null)return;if(d=i.tag,d===5||d===6||d===26||d===27){a=u=i;continue t}r=r.parentNode}}a=a.return}vo(function(){var x=u,R=Mi(l),D=[];t:{var A=Qo.get(t);if(A!==void 0){var E=lu,F=t;switch(t){case"keypress":if(tu(l)===0)break t;case"keydown":case"keyup":E=Ah;break;case"focusin":F="focus",E=Ci;break;case"focusout":F="blur",E=Ci;break;case"beforeblur":case"afterblur":E=Ci;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":E=bo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":E=sh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":E=zh;break;case Yo:case Go:case Xo:E=mh;break;case Lo:E=Nh;break;case"scroll":case"scrollend":E=rh;break;case"wheel":E=Rh;break;case"copy":case"cut":case"paste":E=vh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":E=So;break;case"toggle":case"beforetoggle":E=Dh}var W=(e&4)!==0,_t=!W&&(t==="scroll"||t==="scrollend"),y=W?A!==null?A+"Capture":null:A;W=[];for(var v=x,S;v!==null;){var O=v;if(S=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||S===null||y===null||(O=ka(v,y),O!=null&&W.push(Rn(v,O,S))),_t)break;v=v.return}0<W.length&&(A=new E(A,F,null,l,R),D.push({event:A,listeners:W}))}}if((e&7)===0){t:{if(A=t==="mouseover"||t==="pointerover",E=t==="mouseout"||t==="pointerout",A&&l!==Ni&&(F=l.relatedTarget||l.fromElement)&&(Il(F)||F[Pl]))break t;if((E||A)&&(A=R.window===R?R:(A=R.ownerDocument)?A.defaultView||A.parentWindow:window,E?(F=l.relatedTarget||l.toElement,E=x,F=F?Il(F):null,F!==null&&(_t=b(F),W=F.tag,F!==_t||W!==5&&W!==27&&W!==6)&&(F=null)):(E=null,F=x),E!==F)){if(W=bo,O="onMouseLeave",y="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(W=So,O="onPointerLeave",y="onPointerEnter",v="pointer"),_t=E==null?A:Za(E),S=F==null?A:Za(F),A=new W(O,v+"leave",E,l,R),A.target=_t,A.relatedTarget=S,O=null,Il(R)===x&&(W=new W(y,v+"enter",F,l,R),W.target=S,W.relatedTarget=_t,O=W),_t=O,E&&F)e:{for(W=E,y=F,v=0,S=W;S;S=Ua(S))v++;for(S=0,O=y;O;O=Ua(O))S++;for(;0<v-S;)W=Ua(W),v--;for(;0<S-v;)y=Ua(y),S--;for(;v--;){if(W===y||y!==null&&W===y.alternate)break e;W=Ua(W),y=Ua(y)}W=null}else W=null;E!==null&&sd(D,A,E,W,!1),F!==null&&_t!==null&&sd(D,_t,F,W,!0)}}t:{if(A=x?Za(x):window,E=A.nodeName&&A.nodeName.toLowerCase(),E==="select"||E==="input"&&A.type==="file")var L=Mo;else if(_o(A))if(Ro)L=Xh;else{L=Yh;var rt=qh}else E=A.nodeName,!E||E.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?x&&_i(x.elementType)&&(L=Mo):L=Gh;if(L&&(L=L(t,x))){No(D,L,l,R);break t}rt&&rt(t,A,x),t==="focusout"&&x&&A.type==="number"&&x.memoizedProps.value!=null&&zi(A,"number",A.value)}switch(rt=x?Za(x):window,t){case"focusin":(_o(rt)||rt.contentEditable==="true")&&(oa=rt,Gi=x,tn=null);break;case"focusout":tn=Gi=oa=null;break;case"mousedown":Xi=!0;break;case"contextmenu":case"mouseup":case"dragend":Xi=!1,Bo(D,l,R);break;case"selectionchange":if(Qh)break;case"keydown":case"keyup":Bo(D,l,R)}var K;if(Hi)t:{switch(t){case"compositionstart":var $="onCompositionStart";break t;case"compositionend":$="onCompositionEnd";break t;case"compositionupdate":$="onCompositionUpdate";break t}$=void 0}else ra?Eo(t,l)&&($="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&($="onCompositionStart");$&&(xo&&l.locale!=="ko"&&(ra||$!=="onCompositionStart"?$==="onCompositionEnd"&&ra&&(K=go()):(cl=R,Di="value"in cl?cl.value:cl.textContent,ra=!0)),rt=Qu(x,$),0<rt.length&&($=new po($,t,null,l,R),D.push({event:$,listeners:rt}),K?$.data=K:(K=zo(l),K!==null&&($.data=K)))),(K=Uh?Ch(t,l):jh(t,l))&&($=Qu(x,"onBeforeInput"),0<$.length&&(rt=new po("onBeforeInput","beforeinput",null,l,R),D.push({event:rt,listeners:$}),rt.data=K)),zv(D,t,x,l,R)}rd(D,e)})}function Rn(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Qu(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=ka(t,l),n!=null&&a.unshift(Rn(t,n,u)),n=ka(t,e),n!=null&&a.push(Rn(t,n,u))),t.tag===3)return a;t=t.return}return[]}function Ua(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function sd(t,e,l,a,n){for(var u=e._reactName,i=[];l!==null&&l!==a;){var r=l,d=r.alternate,x=r.stateNode;if(r=r.tag,d!==null&&d===a)break;r!==5&&r!==26&&r!==27||x===null||(d=x,n?(x=ka(l,u),x!=null&&i.unshift(Rn(l,x,d))):n||(x=ka(l,u),x!=null&&i.push(Rn(l,x,d)))),l=l.return}i.length!==0&&t.push({event:e,listeners:i})}var Rv=/\r\n?/g,Ov=/\u0000|\uFFFD/g;function fd(t){return(typeof t=="string"?t:""+t).replace(Rv,`
`).replace(Ov,"")}function dd(t,e){return e=fd(e),fd(t)===e}function Vu(){}function zt(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ua(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ua(t,""+a);break;case"className":Wn(t,"class",a);break;case"tabIndex":Wn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Wn(t,l,a);break;case"style":mo(t,a,u);break;case"data":if(e!=="object"){Wn(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&zt(t,e,"name",n.name,n,null),zt(t,e,"formEncType",n.formEncType,n,null),zt(t,e,"formMethod",n.formMethod,n,null),zt(t,e,"formTarget",n.formTarget,n,null)):(zt(t,e,"encType",n.encType,n,null),zt(t,e,"method",n.method,n,null),zt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"onScroll":a!=null&&mt("scroll",t);break;case"onScrollEnd":a!=null&&mt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Pn(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":mt("beforetoggle",t),mt("toggle",t),Jn(t,"popover",a);break;case"xlinkActuate":Ye(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Ye(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Ye(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Ye(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Ye(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Ye(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Jn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=ih.get(l)||l,Jn(t,l,a))}}function sr(t,e,l,a,n,u){switch(l){case"style":mo(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"children":typeof a=="string"?ua(t,a):(typeof a=="number"||typeof a=="bigint")&&ua(t,""+a);break;case"onScroll":a!=null&&mt("scroll",t);break;case"onScrollEnd":a!=null&&mt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!eo.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[le]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Jn(t,l,a)}}}function Ft(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":mt("error",t),mt("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:zt(t,e,u,i,l,null)}}n&&zt(t,e,"srcSet",l.srcSet,l,null),a&&zt(t,e,"src",l.src,l,null);return;case"input":mt("invalid",t);var r=u=i=n=null,d=null,x=null;for(a in l)if(l.hasOwnProperty(a)){var R=l[a];if(R!=null)switch(a){case"name":n=R;break;case"type":i=R;break;case"checked":d=R;break;case"defaultChecked":x=R;break;case"value":u=R;break;case"defaultValue":r=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(o(137,e));break;default:zt(t,e,a,R,l,null)}}ro(t,u,r,d,x,i,n,!1),$n(t);return;case"select":mt("invalid",t),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(r=l[n],r!=null))switch(n){case"value":u=r;break;case"defaultValue":i=r;break;case"multiple":a=r;default:zt(t,e,n,r,l,null)}e=u,l=i,t.multiple=!!a,e!=null?na(t,!!a,e,!1):l!=null&&na(t,!!a,l,!0);return;case"textarea":mt("invalid",t),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(r=l[i],r!=null))switch(i){case"value":a=r;break;case"defaultValue":n=r;break;case"children":u=r;break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(o(91));break;default:zt(t,e,i,r,l,null)}so(t,a,n,u),$n(t);return;case"option":for(d in l)if(l.hasOwnProperty(d)&&(a=l[d],a!=null))switch(d){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:zt(t,e,d,a,l,null)}return;case"dialog":mt("beforetoggle",t),mt("toggle",t),mt("cancel",t),mt("close",t);break;case"iframe":case"object":mt("load",t);break;case"video":case"audio":for(a=0;a<Mn.length;a++)mt(Mn[a],t);break;case"image":mt("error",t),mt("load",t);break;case"details":mt("toggle",t);break;case"embed":case"source":case"link":mt("error",t),mt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(x in l)if(l.hasOwnProperty(x)&&(a=l[x],a!=null))switch(x){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:zt(t,e,x,a,l,null)}return;default:if(_i(e)){for(R in l)l.hasOwnProperty(R)&&(a=l[R],a!==void 0&&sr(t,e,R,a,l,void 0));return}}for(r in l)l.hasOwnProperty(r)&&(a=l[r],a!=null&&zt(t,e,r,a,l,null))}function Dv(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,r=null,d=null,x=null,R=null;for(E in l){var D=l[E];if(l.hasOwnProperty(E)&&D!=null)switch(E){case"checked":break;case"value":break;case"defaultValue":d=D;default:a.hasOwnProperty(E)||zt(t,e,E,null,a,D)}}for(var A in a){var E=a[A];if(D=l[A],a.hasOwnProperty(A)&&(E!=null||D!=null))switch(A){case"type":u=E;break;case"name":n=E;break;case"checked":x=E;break;case"defaultChecked":R=E;break;case"value":i=E;break;case"defaultValue":r=E;break;case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(o(137,e));break;default:E!==D&&zt(t,e,A,E,a,D)}}Ei(t,i,r,d,x,R,u,n);return;case"select":E=i=r=A=null;for(u in l)if(d=l[u],l.hasOwnProperty(u)&&d!=null)switch(u){case"value":break;case"multiple":E=d;default:a.hasOwnProperty(u)||zt(t,e,u,null,a,d)}for(n in a)if(u=a[n],d=l[n],a.hasOwnProperty(n)&&(u!=null||d!=null))switch(n){case"value":A=u;break;case"defaultValue":r=u;break;case"multiple":i=u;default:u!==d&&zt(t,e,n,u,a,d)}e=r,l=i,a=E,A!=null?na(t,!!l,A,!1):!!a!=!!l&&(e!=null?na(t,!!l,e,!0):na(t,!!l,l?[]:"",!1));return;case"textarea":E=A=null;for(r in l)if(n=l[r],l.hasOwnProperty(r)&&n!=null&&!a.hasOwnProperty(r))switch(r){case"value":break;case"children":break;default:zt(t,e,r,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":A=n;break;case"defaultValue":E=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(o(91));break;default:n!==u&&zt(t,e,i,n,a,u)}oo(t,A,E);return;case"option":for(var F in l)if(A=l[F],l.hasOwnProperty(F)&&A!=null&&!a.hasOwnProperty(F))switch(F){case"selected":t.selected=!1;break;default:zt(t,e,F,null,a,A)}for(d in a)if(A=a[d],E=l[d],a.hasOwnProperty(d)&&A!==E&&(A!=null||E!=null))switch(d){case"selected":t.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:zt(t,e,d,A,a,E)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in l)A=l[W],l.hasOwnProperty(W)&&A!=null&&!a.hasOwnProperty(W)&&zt(t,e,W,null,a,A);for(x in a)if(A=a[x],E=l[x],a.hasOwnProperty(x)&&A!==E&&(A!=null||E!=null))switch(x){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(o(137,e));break;default:zt(t,e,x,A,a,E)}return;default:if(_i(e)){for(var _t in l)A=l[_t],l.hasOwnProperty(_t)&&A!==void 0&&!a.hasOwnProperty(_t)&&sr(t,e,_t,void 0,a,A);for(R in a)A=a[R],E=l[R],!a.hasOwnProperty(R)||A===E||A===void 0&&E===void 0||sr(t,e,R,A,a,E);return}}for(var y in l)A=l[y],l.hasOwnProperty(y)&&A!=null&&!a.hasOwnProperty(y)&&zt(t,e,y,null,a,A);for(D in a)A=a[D],E=l[D],!a.hasOwnProperty(D)||A===E||A==null&&E==null||zt(t,e,D,A,a,E)}var fr=null,dr=null;function Zu(t){return t.nodeType===9?t:t.ownerDocument}function md(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function hd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function mr(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var hr=null;function wv(){var t=window.event;return t&&t.type==="popstate"?t===hr?!1:(hr=t,!0):(hr=null,!1)}var vd=typeof setTimeout=="function"?setTimeout:void 0,Uv=typeof clearTimeout=="function"?clearTimeout:void 0,gd=typeof Promise=="function"?Promise:void 0,Cv=typeof queueMicrotask=="function"?queueMicrotask:typeof gd<"u"?function(t){return gd.resolve(null).then(t).catch(jv)}:vd;function jv(t){setTimeout(function(){throw t})}function Tl(t){return t==="head"}function yd(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var i=t.ownerDocument;if(l&1&&On(i.documentElement),l&2&&On(i.body),l&4)for(l=i.head,On(l),i=l.firstChild;i;){var r=i.nextSibling,d=i.nodeName;i[Va]||d==="SCRIPT"||d==="STYLE"||d==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=r}}if(n===0){t.removeChild(u),qn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);qn(e)}function vr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":vr(l),Si(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Hv(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Va])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Re(t.nextSibling),t===null)break}return null}function Bv(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Re(t.nextSibling),t===null))return null;return t}function gr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function qv(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Re(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var yr=null;function bd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function pd(t,e,l){switch(e=Zu(l),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function On(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Si(t)}var ze=new Map,Sd=new Set;function ku(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ie=q.d;q.d={f:Yv,r:Gv,D:Xv,C:Lv,L:Qv,m:Vv,X:kv,S:Zv,M:Kv};function Yv(){var t=Ie.f(),e=Bu();return t||e}function Gv(t){var e=ta(t);e!==null&&e.tag===5&&e.type==="form"?Ys(e):Ie.r(t)}var Ca=typeof document>"u"?null:document;function xd(t,e,l){var a=Ca;if(a&&typeof e=="string"&&e){var n=be(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),Sd.has(n)||(Sd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),Ft(e,"link",t),Zt(e),a.head.appendChild(e)))}}function Xv(t){Ie.D(t),xd("dns-prefetch",t,null)}function Lv(t,e){Ie.C(t,e),xd("preconnect",t,e)}function Qv(t,e,l){Ie.L(t,e,l);var a=Ca;if(a&&t&&e){var n='link[rel="preload"][as="'+be(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+be(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+be(l.imageSizes)+'"]')):n+='[href="'+be(t)+'"]';var u=n;switch(e){case"style":u=ja(t);break;case"script":u=Ha(t)}ze.has(u)||(t=N({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),ze.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Dn(u))||e==="script"&&a.querySelector(wn(u))||(e=a.createElement("link"),Ft(e,"link",t),Zt(e),a.head.appendChild(e)))}}function Vv(t,e){Ie.m(t,e);var l=Ca;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+be(a)+'"][href="'+be(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ha(t)}if(!ze.has(u)&&(t=N({rel:"modulepreload",href:t},e),ze.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(wn(u)))return}a=l.createElement("link"),Ft(a,"link",t),Zt(a),l.head.appendChild(a)}}}function Zv(t,e,l){Ie.S(t,e,l);var a=Ca;if(a&&t){var n=ea(a).hoistableStyles,u=ja(t);e=e||"default";var i=n.get(u);if(!i){var r={loading:0,preload:null};if(i=a.querySelector(Dn(u)))r.loading=5;else{t=N({rel:"stylesheet",href:t,"data-precedence":e},l),(l=ze.get(u))&&br(t,l);var d=i=a.createElement("link");Zt(d),Ft(d,"link",t),d._p=new Promise(function(x,R){d.onload=x,d.onerror=R}),d.addEventListener("load",function(){r.loading|=1}),d.addEventListener("error",function(){r.loading|=2}),r.loading|=4,Ku(i,e,a)}i={type:"stylesheet",instance:i,count:1,state:r},n.set(u,i)}}}function kv(t,e){Ie.X(t,e);var l=Ca;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(wn(n)),u||(t=N({src:t,async:!0},e),(e=ze.get(n))&&pr(t,e),u=l.createElement("script"),Zt(u),Ft(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Kv(t,e){Ie.M(t,e);var l=Ca;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(wn(n)),u||(t=N({src:t,async:!0,type:"module"},e),(e=ze.get(n))&&pr(t,e),u=l.createElement("script"),Zt(u),Ft(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Ad(t,e,l,a){var n=(n=I.current)?ku(n):null;if(!n)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=ja(l.href),l=ea(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=ja(l.href);var u=ea(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(Dn(t)))&&!u._p&&(i.instance=u,i.state.loading=5),ze.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ze.set(t,l),u||Jv(n,t,l,i.state))),e&&a===null)throw Error(o(528,""));return i}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ha(l),l=ea(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function ja(t){return'href="'+be(t)+'"'}function Dn(t){return'link[rel="stylesheet"]['+t+"]"}function Td(t){return N({},t,{"data-precedence":t.precedence,precedence:null})}function Jv(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Ft(e,"link",l),Zt(e),t.head.appendChild(e))}function Ha(t){return'[src="'+be(t)+'"]'}function wn(t){return"script[async]"+t}function Ed(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+be(l.href)+'"]');if(a)return e.instance=a,Zt(a),a;var n=N({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Zt(a),Ft(a,"style",n),Ku(a,l.precedence,t),e.instance=a;case"stylesheet":n=ja(l.href);var u=t.querySelector(Dn(n));if(u)return e.state.loading|=4,e.instance=u,Zt(u),u;a=Td(l),(n=ze.get(n))&&br(a,n),u=(t.ownerDocument||t).createElement("link"),Zt(u);var i=u;return i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),Ft(u,"link",a),e.state.loading|=4,Ku(u,l.precedence,t),e.instance=u;case"script":return u=Ha(l.src),(n=t.querySelector(wn(u)))?(e.instance=n,Zt(n),n):(a=l,(n=ze.get(u))&&(a=N({},l),pr(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Zt(n),Ft(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ku(a,l.precedence,t));return e.instance}function Ku(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var r=a[i];if(r.dataset.precedence===e)u=r;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function br(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function pr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ju=null;function zd(t,e,l){if(Ju===null){var a=new Map,n=Ju=new Map;n.set(l,a)}else n=Ju,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[Va]||u[Pt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(e)||"";i=t+i;var r=a.get(i);r?r.push(u):a.set(i,[u])}}return a}function _d(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function Wv(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Nd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Un=null;function $v(){}function Fv(t,e,l){if(Un===null)throw Error(o(475));var a=Un;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=ja(l.href),u=t.querySelector(Dn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Wu.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,Zt(u);return}u=t.ownerDocument||t,l=Td(l),(n=ze.get(n))&&br(l,n),u=u.createElement("link"),Zt(u);var i=u;i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),Ft(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Wu.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function Pv(){if(Un===null)throw Error(o(475));var t=Un;return t.stylesheets&&t.count===0&&Sr(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&Sr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Wu(){if(this.count--,this.count===0){if(this.stylesheets)Sr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var $u=null;function Sr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,$u=new Map,e.forEach(Iv,t),$u=null,Wu.call(t))}function Iv(t,e){if(!(e.state.loading&4)){var l=$u.get(t);if(l)var a=l.get(null);else{l=new Map,$u.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=e.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=Wu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Cn={$$typeof:pt,Provider:null,Consumer:null,_currentValue:j,_currentValue2:j,_threadCount:0};function t0(t,e,l,a,n,u,i,r){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=gi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gi(0),this.hiddenUpdates=gi(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=r,this.incompleteTransitions=new Map}function Md(t,e,l,a,n,u,i,r,d,x,R,D){return t=new t0(t,e,l,i,r,d,x,D),e=1,u===!0&&(e|=24),u=fe(3,null,null,e),t.current=u,u.stateNode=t,e=ec(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},uc(u),t}function Rd(t){return t?(t=ma,t):ma}function Od(t,e,l,a,n,u){n=Rd(n),a.context===null?a.context=n:a.pendingContext=n,a=sl(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=fl(t,a,e),l!==null&&(ge(l,t,e),sn(l,t,e))}function Dd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function xr(t,e){Dd(t,e),(t=t.alternate)&&Dd(t,e)}function wd(t){if(t.tag===13){var e=da(t,67108864);e!==null&&ge(e,t,67108864),xr(t,67108864)}}var Fu=!0;function e0(t,e,l,a){var n=_.T;_.T=null;var u=q.p;try{q.p=2,Ar(t,e,l,a)}finally{q.p=u,_.T=n}}function l0(t,e,l,a){var n=_.T;_.T=null;var u=q.p;try{q.p=8,Ar(t,e,l,a)}finally{q.p=u,_.T=n}}function Ar(t,e,l,a){if(Fu){var n=Tr(a);if(n===null)or(t,e,a,Pu,l),Cd(t,a);else if(n0(n,t,e,l,a))a.stopPropagation();else if(Cd(t,a),e&4&&-1<a0.indexOf(t)){for(;n!==null;){var u=ta(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=Ol(u.pendingLanes);if(i!==0){var r=u;for(r.pendingLanes|=2,r.entangledLanes|=2;i;){var d=1<<31-oe(i);r.entanglements[1]|=d,i&=~d}He(u),(xt&6)===0&&(ju=De()+500,Nn(0))}}break;case 13:r=da(u,2),r!==null&&ge(r,u,2),Bu(),xr(u,2)}if(u=Tr(a),u===null&&or(t,e,a,Pu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else or(t,e,a,null,l)}}function Tr(t){return t=Mi(t),Er(t)}var Pu=null;function Er(t){if(Pu=null,t=Il(t),t!==null){var e=b(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=z(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Pu=t,null}function Ud(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Lm()){case Zr:return 2;case kr:return 8;case Vn:case Qm:return 32;case Kr:return 268435456;default:return 32}default:return 32}}var zr=!1,El=null,zl=null,_l=null,jn=new Map,Hn=new Map,Nl=[],a0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Cd(t,e){switch(t){case"focusin":case"focusout":El=null;break;case"dragenter":case"dragleave":zl=null;break;case"mouseover":case"mouseout":_l=null;break;case"pointerover":case"pointerout":jn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hn.delete(e.pointerId)}}function Bn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=ta(e),e!==null&&wd(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function n0(t,e,l,a,n){switch(e){case"focusin":return El=Bn(El,t,e,l,a,n),!0;case"dragenter":return zl=Bn(zl,t,e,l,a,n),!0;case"mouseover":return _l=Bn(_l,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return jn.set(u,Bn(jn.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Hn.set(u,Bn(Hn.get(u)||null,t,e,l,a,n)),!0}return!1}function jd(t){var e=Il(t.target);if(e!==null){var l=b(e);if(l!==null){if(e=l.tag,e===13){if(e=z(l),e!==null){t.blockedOn=e,Fm(t.priority,function(){if(l.tag===13){var a=ve();a=yi(a);var n=da(l,a);n!==null&&ge(n,l,a),xr(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Iu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Tr(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Ni=a,l.target.dispatchEvent(a),Ni=null}else return e=ta(l),e!==null&&wd(e),t.blockedOn=l,!1;e.shift()}return!0}function Hd(t,e,l){Iu(t)&&l.delete(e)}function u0(){zr=!1,El!==null&&Iu(El)&&(El=null),zl!==null&&Iu(zl)&&(zl=null),_l!==null&&Iu(_l)&&(_l=null),jn.forEach(Hd),Hn.forEach(Hd)}function ti(t,e){t.blockedOn===e&&(t.blockedOn=null,zr||(zr=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,u0)))}var ei=null;function Bd(t){ei!==t&&(ei=t,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){ei===t&&(ei=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(Er(a||l)===null)continue;break}var u=ta(l);u!==null&&(t.splice(e,3),e-=3,Ec(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function qn(t){function e(d){return ti(d,t)}El!==null&&ti(El,t),zl!==null&&ti(zl,t),_l!==null&&ti(_l,t),jn.forEach(e),Hn.forEach(e);for(var l=0;l<Nl.length;l++){var a=Nl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Nl.length&&(l=Nl[0],l.blockedOn===null);)jd(l),l.blockedOn===null&&Nl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[le]||null;if(typeof u=="function")i||Bd(l);else if(i){var r=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[le]||null)r=i.formAction;else if(Er(n)!==null)continue}else r=i.action;typeof r=="function"?l[a+1]=r:(l.splice(a,3),a-=3),Bd(l)}}}function _r(t){this._internalRoot=t}li.prototype.render=_r.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var l=e.current,a=ve();Od(l,a,t,e,null,null)},li.prototype.unmount=_r.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Od(t.current,2,null,t,null,null),Bu(),e[Pl]=null}};function li(t){this._internalRoot=t}li.prototype.unstable_scheduleHydration=function(t){if(t){var e=Pr();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Nl.length&&e!==0&&e<Nl[l].priority;l++);Nl.splice(l,0,t),l===0&&jd(t)}};var qd=s.version;if(qd!=="19.1.0")throw Error(o(527,qd,"19.1.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=p(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var i0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ai=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ai.isDisabled&&ai.supportsFiber)try{Xa=ai.inject(i0),re=ai}catch{}}return Gn.createRoot=function(t,e){if(!m(t))throw Error(o(299));var l=!1,a="",n=Is,u=tf,i=ef,r=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(i=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(r=e.unstable_transitionCallbacks)),e=Md(t,1,!1,null,null,l,a,n,u,i,r,null),t[Pl]=e.current,rr(t),new _r(e)},Gn.hydrateRoot=function(t,e,l){if(!m(t))throw Error(o(299));var a=!1,n="",u=Is,i=tf,r=ef,d=null,x=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(r=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(d=l.unstable_transitionCallbacks),l.formState!==void 0&&(x=l.formState)),e=Md(t,1,!0,e,l??null,a,n,u,i,r,d,x),e.context=Rd(null),l=e.current,a=ve(),a=yi(a),n=sl(a),n.callback=null,fl(l,n,a),l=a,e.current.lanes=l,Qa(e,l),He(e),t[Pl]=e.current,rr(t),new li(e)},Gn.version="19.1.0",Gn}var Jd;function v0(){if(Jd)return Rr.exports;Jd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Rr.exports=h0(),Rr.exports}var g0=v0();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),b0=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(s,f,o)=>o?o.toUpperCase():f.toLowerCase()),Wd=c=>{const s=b0(c);return s.charAt(0).toUpperCase()+s.slice(1)},sm=(...c)=>c.filter((s,f,o)=>!!s&&s.trim()!==""&&o.indexOf(s)===f).join(" ").trim(),p0=c=>{for(const s in c)if(s.startsWith("aria-")||s==="role"||s==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var S0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=w.forwardRef(({color:c="currentColor",size:s=24,strokeWidth:f=2,absoluteStrokeWidth:o,className:m="",children:b,iconNode:z,...M},p)=>w.createElement("svg",{ref:p,...S0,width:s,height:s,stroke:c,strokeWidth:o?Number(f)*24/Number(s):f,className:sm("lucide",m),...!b&&!p0(M)&&{"aria-hidden":"true"},...M},[...z.map(([g,N])=>w.createElement(g,N)),...Array.isArray(b)?b:[b]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=(c,s)=>{const f=w.forwardRef(({className:o,...m},b)=>w.createElement(x0,{ref:b,iconNode:s,className:sm(`lucide-${y0(Wd(c))}`,`lucide-${c}`,o),...m}));return f.displayName=Wd(c),f};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A0=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],$d=Oe("book",A0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T0=[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]],E0=Oe("corner-down-left",T0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z0=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],ni=Oe("download",z0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],N0=Oe("globe",_0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M0=[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]],R0=Oe("maximize-2",M0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],Ur=Oe("message-square",O0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D0=[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]],w0=Oe("minimize-2",D0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],C0=Oe("search",U0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j0=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],H0=Oe("tag",j0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B0=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],q0=Oe("user",B0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y0=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],G0=Oe("x",Y0);function Fd(c,s){if(typeof c=="function")return c(s);c!=null&&(c.current=s)}function fm(...c){return s=>{let f=!1;const o=c.map(m=>{const b=Fd(m,s);return!f&&typeof b=="function"&&(f=!0),b});if(f)return()=>{for(let m=0;m<o.length;m++){const b=o[m];typeof b=="function"?b():Fd(c[m],null)}}}}function Fl(...c){return w.useCallback(fm(...c),c)}function dm(c){const s=X0(c),f=w.forwardRef((o,m)=>{const{children:b,...z}=o,M=w.Children.toArray(b),p=M.find(Q0);if(p){const g=p.props.children,N=M.map(C=>C===p?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:C);return T.jsx(s,{...z,ref:m,children:w.isValidElement(g)?w.cloneElement(g,void 0,N):null})}return T.jsx(s,{...z,ref:m,children:b})});return f.displayName=`${c}.Slot`,f}var mm=dm("Slot");function X0(c){const s=w.forwardRef((f,o)=>{const{children:m,...b}=f;if(w.isValidElement(m)){const z=Z0(m),M=V0(b,m.props);return m.type!==w.Fragment&&(M.ref=o?fm(o,z):z),w.cloneElement(m,M)}return w.Children.count(m)>1?w.Children.only(null):null});return s.displayName=`${c}.SlotClone`,s}var L0=Symbol("radix.slottable");function Q0(c){return w.isValidElement(c)&&typeof c.type=="function"&&"__radixId"in c.type&&c.type.__radixId===L0}function V0(c,s){const f={...s};for(const o in s){const m=c[o],b=s[o];/^on[A-Z]/.test(o)?m&&b?f[o]=(...M)=>{const p=b(...M);return m(...M),p}:m&&(f[o]=m):o==="style"?f[o]={...m,...b}:o==="className"&&(f[o]=[m,b].filter(Boolean).join(" "))}return{...c,...f}}function Z0(c){var o,m;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,f=s&&"isReactWarning"in s&&s.isReactWarning;return f?c.ref:(s=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,f=s&&"isReactWarning"in s&&s.isReactWarning,f?c.props.ref:c.props.ref||c.ref)}function hm(c){var s,f,o="";if(typeof c=="string"||typeof c=="number")o+=c;else if(typeof c=="object")if(Array.isArray(c)){var m=c.length;for(s=0;s<m;s++)c[s]&&(f=hm(c[s]))&&(o&&(o+=" "),o+=f)}else for(f in c)c[f]&&(o&&(o+=" "),o+=f);return o}function vm(){for(var c,s,f=0,o="",m=arguments.length;f<m;f++)(c=arguments[f])&&(s=hm(c))&&(o&&(o+=" "),o+=s);return o}const Pd=c=>typeof c=="boolean"?`${c}`:c===0?"0":c,Id=vm,gm=(c,s)=>f=>{var o;if((s==null?void 0:s.variants)==null)return Id(c,f==null?void 0:f.class,f==null?void 0:f.className);const{variants:m,defaultVariants:b}=s,z=Object.keys(m).map(g=>{const N=f==null?void 0:f[g],C=b==null?void 0:b[g];if(N===null)return null;const H=Pd(N)||Pd(C);return m[g][H]}),M=f&&Object.entries(f).reduce((g,N)=>{let[C,H]=N;return H===void 0||(g[C]=H),g},{}),p=s==null||(o=s.compoundVariants)===null||o===void 0?void 0:o.reduce((g,N)=>{let{class:C,className:H,...k}=N;return Object.entries(k).every(J=>{let[tt,ot]=J;return Array.isArray(ot)?ot.includes({...b,...M}[tt]):{...b,...M}[tt]===ot})?[...g,C,H]:g},[]);return Id(c,z,p,f==null?void 0:f.class,f==null?void 0:f.className)},Xr="-",k0=c=>{const s=J0(c),{conflictingClassGroups:f,conflictingClassGroupModifiers:o}=c;return{getClassGroupId:z=>{const M=z.split(Xr);return M[0]===""&&M.length!==1&&M.shift(),ym(M,s)||K0(z)},getConflictingClassGroupIds:(z,M)=>{const p=f[z]||[];return M&&o[z]?[...p,...o[z]]:p}}},ym=(c,s)=>{var z;if(c.length===0)return s.classGroupId;const f=c[0],o=s.nextPart.get(f),m=o?ym(c.slice(1),o):void 0;if(m)return m;if(s.validators.length===0)return;const b=c.join(Xr);return(z=s.validators.find(({validator:M})=>M(b)))==null?void 0:z.classGroupId},tm=/^\[(.+)\]$/,K0=c=>{if(tm.test(c)){const s=tm.exec(c)[1],f=s==null?void 0:s.substring(0,s.indexOf(":"));if(f)return"arbitrary.."+f}},J0=c=>{const{theme:s,classGroups:f}=c,o={nextPart:new Map,validators:[]};for(const m in f)Hr(f[m],o,m,s);return o},Hr=(c,s,f,o)=>{c.forEach(m=>{if(typeof m=="string"){const b=m===""?s:em(s,m);b.classGroupId=f;return}if(typeof m=="function"){if(W0(m)){Hr(m(o),s,f,o);return}s.validators.push({validator:m,classGroupId:f});return}Object.entries(m).forEach(([b,z])=>{Hr(z,em(s,b),f,o)})})},em=(c,s)=>{let f=c;return s.split(Xr).forEach(o=>{f.nextPart.has(o)||f.nextPart.set(o,{nextPart:new Map,validators:[]}),f=f.nextPart.get(o)}),f},W0=c=>c.isThemeGetter,$0=c=>{if(c<1)return{get:()=>{},set:()=>{}};let s=0,f=new Map,o=new Map;const m=(b,z)=>{f.set(b,z),s++,s>c&&(s=0,o=f,f=new Map)};return{get(b){let z=f.get(b);if(z!==void 0)return z;if((z=o.get(b))!==void 0)return m(b,z),z},set(b,z){f.has(b)?f.set(b,z):m(b,z)}}},Br="!",qr=":",F0=qr.length,P0=c=>{const{prefix:s,experimentalParseClassName:f}=c;let o=m=>{const b=[];let z=0,M=0,p=0,g;for(let J=0;J<m.length;J++){let tt=m[J];if(z===0&&M===0){if(tt===qr){b.push(m.slice(p,J)),p=J+F0;continue}if(tt==="/"){g=J;continue}}tt==="["?z++:tt==="]"?z--:tt==="("?M++:tt===")"&&M--}const N=b.length===0?m:m.substring(p),C=I0(N),H=C!==N,k=g&&g>p?g-p:void 0;return{modifiers:b,hasImportantModifier:H,baseClassName:C,maybePostfixModifierPosition:k}};if(s){const m=s+qr,b=o;o=z=>z.startsWith(m)?b(z.substring(m.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:z,maybePostfixModifierPosition:void 0}}if(f){const m=o;o=b=>f({className:b,parseClassName:m})}return o},I0=c=>c.endsWith(Br)?c.substring(0,c.length-1):c.startsWith(Br)?c.substring(1):c,tg=c=>{const s=Object.fromEntries(c.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const m=[];let b=[];return o.forEach(z=>{z[0]==="["||s[z]?(m.push(...b.sort(),z),b=[]):b.push(z)}),m.push(...b.sort()),m}},eg=c=>({cache:$0(c.cacheSize),parseClassName:P0(c),sortModifiers:tg(c),...k0(c)}),lg=/\s+/,ag=(c,s)=>{const{parseClassName:f,getClassGroupId:o,getConflictingClassGroupIds:m,sortModifiers:b}=s,z=[],M=c.trim().split(lg);let p="";for(let g=M.length-1;g>=0;g-=1){const N=M[g],{isExternal:C,modifiers:H,hasImportantModifier:k,baseClassName:J,maybePostfixModifierPosition:tt}=f(N);if(C){p=N+(p.length>0?" "+p:p);continue}let ot=!!tt,Tt=o(ot?J.substring(0,tt):J);if(!Tt){if(!ot){p=N+(p.length>0?" "+p:p);continue}if(Tt=o(J),!Tt){p=N+(p.length>0?" "+p:p);continue}ot=!1}const jt=b(H).join(":"),pt=k?jt+Br:jt,et=pt+Tt;if(z.includes(et))continue;z.push(et);const P=m(Tt,ot);for(let St=0;St<P.length;++St){const Nt=P[St];z.push(pt+Nt)}p=N+(p.length>0?" "+p:p)}return p};function ng(){let c=0,s,f,o="";for(;c<arguments.length;)(s=arguments[c++])&&(f=bm(s))&&(o&&(o+=" "),o+=f);return o}const bm=c=>{if(typeof c=="string")return c;let s,f="";for(let o=0;o<c.length;o++)c[o]&&(s=bm(c[o]))&&(f&&(f+=" "),f+=s);return f};function ug(c,...s){let f,o,m,b=z;function z(p){const g=s.reduce((N,C)=>C(N),c());return f=eg(g),o=f.cache.get,m=f.cache.set,b=M,M(p)}function M(p){const g=o(p);if(g)return g;const N=ag(p,f);return m(p,N),N}return function(){return b(ng.apply(null,arguments))}}const Vt=c=>{const s=f=>f[c]||[];return s.isThemeGetter=!0,s},pm=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Sm=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ig=/^\d+\/\d+$/,cg=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,rg=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,og=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,sg=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fg=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ba=c=>ig.test(c),it=c=>!!c&&!Number.isNaN(Number(c)),Rl=c=>!!c&&Number.isInteger(Number(c)),Cr=c=>c.endsWith("%")&&it(c.slice(0,-1)),tl=c=>cg.test(c),dg=()=>!0,mg=c=>rg.test(c)&&!og.test(c),xm=()=>!1,hg=c=>sg.test(c),vg=c=>fg.test(c),gg=c=>!Q(c)&&!V(c),yg=c=>Ya(c,Em,xm),Q=c=>pm.test(c),Jl=c=>Ya(c,zm,mg),jr=c=>Ya(c,Ag,it),lm=c=>Ya(c,Am,xm),bg=c=>Ya(c,Tm,vg),ui=c=>Ya(c,_m,hg),V=c=>Sm.test(c),Xn=c=>Ga(c,zm),pg=c=>Ga(c,Tg),am=c=>Ga(c,Am),Sg=c=>Ga(c,Em),xg=c=>Ga(c,Tm),ii=c=>Ga(c,_m,!0),Ya=(c,s,f)=>{const o=pm.exec(c);return o?o[1]?s(o[1]):f(o[2]):!1},Ga=(c,s,f=!1)=>{const o=Sm.exec(c);return o?o[1]?s(o[1]):f:!1},Am=c=>c==="position"||c==="percentage",Tm=c=>c==="image"||c==="url",Em=c=>c==="length"||c==="size"||c==="bg-size",zm=c=>c==="length",Ag=c=>c==="number",Tg=c=>c==="family-name",_m=c=>c==="shadow",Eg=()=>{const c=Vt("color"),s=Vt("font"),f=Vt("text"),o=Vt("font-weight"),m=Vt("tracking"),b=Vt("leading"),z=Vt("breakpoint"),M=Vt("container"),p=Vt("spacing"),g=Vt("radius"),N=Vt("shadow"),C=Vt("inset-shadow"),H=Vt("text-shadow"),k=Vt("drop-shadow"),J=Vt("blur"),tt=Vt("perspective"),ot=Vt("aspect"),Tt=Vt("ease"),jt=Vt("animate"),pt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],et=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...et(),V,Q],St=()=>["auto","hidden","clip","visible","scroll"],Nt=()=>["auto","contain","none"],Z=()=>[V,Q,p],at=()=>[Ba,"full","auto",...Z()],Ut=()=>[Rl,"none","subgrid",V,Q],G=()=>["auto",{span:["full",Rl,V,Q]},Rl,V,Q],lt=()=>[Rl,"auto",V,Q],ct=()=>["auto","min","max","fr",V,Q],Mt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],st=()=>["start","end","center","stretch","center-safe","end-safe"],_=()=>["auto",...Z()],q=()=>[Ba,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Z()],j=()=>[c,V,Q],bt=()=>[...et(),am,lm,{position:[V,Q]}],h=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",Sg,yg,{size:[V,Q]}],Y=()=>[Cr,Xn,Jl],B=()=>["","none","full",g,V,Q],X=()=>["",it,Xn,Jl],ft=()=>["solid","dashed","dotted","double"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],vt=()=>[it,Cr,am,lm],Rt=()=>["","none",J,V,Q],ce=()=>["none",it,V,Q],ll=()=>["none",it,V,Q],al=()=>[it,V,Q],nl=()=>[Ba,"full",...Z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[tl],breakpoint:[tl],color:[dg],container:[tl],"drop-shadow":[tl],ease:["in","out","in-out"],font:[gg],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[tl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[tl],shadow:[tl],spacing:["px",it],text:[tl],"text-shadow":[tl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ba,Q,V,ot]}],container:["container"],columns:[{columns:[it,Q,V,M]}],"break-after":[{"break-after":pt()}],"break-before":[{"break-before":pt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:St()}],"overflow-x":[{"overflow-x":St()}],"overflow-y":[{"overflow-y":St()}],overscroll:[{overscroll:Nt()}],"overscroll-x":[{"overscroll-x":Nt()}],"overscroll-y":[{"overscroll-y":Nt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:at()}],"inset-x":[{"inset-x":at()}],"inset-y":[{"inset-y":at()}],start:[{start:at()}],end:[{end:at()}],top:[{top:at()}],right:[{right:at()}],bottom:[{bottom:at()}],left:[{left:at()}],visibility:["visible","invisible","collapse"],z:[{z:[Rl,"auto",V,Q]}],basis:[{basis:[Ba,"full","auto",M,...Z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[it,Ba,"auto","initial","none",Q]}],grow:[{grow:["",it,V,Q]}],shrink:[{shrink:["",it,V,Q]}],order:[{order:[Rl,"first","last","none",V,Q]}],"grid-cols":[{"grid-cols":Ut()}],"col-start-end":[{col:G()}],"col-start":[{"col-start":lt()}],"col-end":[{"col-end":lt()}],"grid-rows":[{"grid-rows":Ut()}],"row-start-end":[{row:G()}],"row-start":[{"row-start":lt()}],"row-end":[{"row-end":lt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ct()}],"auto-rows":[{"auto-rows":ct()}],gap:[{gap:Z()}],"gap-x":[{"gap-x":Z()}],"gap-y":[{"gap-y":Z()}],"justify-content":[{justify:[...Mt(),"normal"]}],"justify-items":[{"justify-items":[...st(),"normal"]}],"justify-self":[{"justify-self":["auto",...st()]}],"align-content":[{content:["normal",...Mt()]}],"align-items":[{items:[...st(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...st(),{baseline:["","last"]}]}],"place-content":[{"place-content":Mt()}],"place-items":[{"place-items":[...st(),"baseline"]}],"place-self":[{"place-self":["auto",...st()]}],p:[{p:Z()}],px:[{px:Z()}],py:[{py:Z()}],ps:[{ps:Z()}],pe:[{pe:Z()}],pt:[{pt:Z()}],pr:[{pr:Z()}],pb:[{pb:Z()}],pl:[{pl:Z()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":Z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Z()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[M,"screen",...q()]}],"min-w":[{"min-w":[M,"screen","none",...q()]}],"max-w":[{"max-w":[M,"screen","none","prose",{screen:[z]},...q()]}],h:[{h:["screen","lh",...q()]}],"min-h":[{"min-h":["screen","lh","none",...q()]}],"max-h":[{"max-h":["screen","lh",...q()]}],"font-size":[{text:["base",f,Xn,Jl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,V,jr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Cr,Q]}],"font-family":[{font:[pg,Q,s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[m,V,Q]}],"line-clamp":[{"line-clamp":[it,"none",V,jr]}],leading:[{leading:[b,...Z()]}],"list-image":[{"list-image":["none",V,Q]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",V,Q]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:j()}],"text-color":[{text:j()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ft(),"wavy"]}],"text-decoration-thickness":[{decoration:[it,"from-font","auto",V,Jl]}],"text-decoration-color":[{decoration:j()}],"underline-offset":[{"underline-offset":[it,"auto",V,Q]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V,Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V,Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:bt()}],"bg-repeat":[{bg:h()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Rl,V,Q],radial:["",V,Q],conic:[Rl,V,Q]},xg,bg]}],"bg-color":[{bg:j()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:j()}],"gradient-via":[{via:j()}],"gradient-to":[{to:j()}],rounded:[{rounded:B()}],"rounded-s":[{"rounded-s":B()}],"rounded-e":[{"rounded-e":B()}],"rounded-t":[{"rounded-t":B()}],"rounded-r":[{"rounded-r":B()}],"rounded-b":[{"rounded-b":B()}],"rounded-l":[{"rounded-l":B()}],"rounded-ss":[{"rounded-ss":B()}],"rounded-se":[{"rounded-se":B()}],"rounded-ee":[{"rounded-ee":B()}],"rounded-es":[{"rounded-es":B()}],"rounded-tl":[{"rounded-tl":B()}],"rounded-tr":[{"rounded-tr":B()}],"rounded-br":[{"rounded-br":B()}],"rounded-bl":[{"rounded-bl":B()}],"border-w":[{border:X()}],"border-w-x":[{"border-x":X()}],"border-w-y":[{"border-y":X()}],"border-w-s":[{"border-s":X()}],"border-w-e":[{"border-e":X()}],"border-w-t":[{"border-t":X()}],"border-w-r":[{"border-r":X()}],"border-w-b":[{"border-b":X()}],"border-w-l":[{"border-l":X()}],"divide-x":[{"divide-x":X()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":X()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ft(),"hidden","none"]}],"divide-style":[{divide:[...ft(),"hidden","none"]}],"border-color":[{border:j()}],"border-color-x":[{"border-x":j()}],"border-color-y":[{"border-y":j()}],"border-color-s":[{"border-s":j()}],"border-color-e":[{"border-e":j()}],"border-color-t":[{"border-t":j()}],"border-color-r":[{"border-r":j()}],"border-color-b":[{"border-b":j()}],"border-color-l":[{"border-l":j()}],"divide-color":[{divide:j()}],"outline-style":[{outline:[...ft(),"none","hidden"]}],"outline-offset":[{"outline-offset":[it,V,Q]}],"outline-w":[{outline:["",it,Xn,Jl]}],"outline-color":[{outline:j()}],shadow:[{shadow:["","none",N,ii,ui]}],"shadow-color":[{shadow:j()}],"inset-shadow":[{"inset-shadow":["none",C,ii,ui]}],"inset-shadow-color":[{"inset-shadow":j()}],"ring-w":[{ring:X()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:j()}],"ring-offset-w":[{"ring-offset":[it,Jl]}],"ring-offset-color":[{"ring-offset":j()}],"inset-ring-w":[{"inset-ring":X()}],"inset-ring-color":[{"inset-ring":j()}],"text-shadow":[{"text-shadow":["none",H,ii,ui]}],"text-shadow-color":[{"text-shadow":j()}],opacity:[{opacity:[it,V,Q]}],"mix-blend":[{"mix-blend":[...I(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":I()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[it]}],"mask-image-linear-from-pos":[{"mask-linear-from":vt()}],"mask-image-linear-to-pos":[{"mask-linear-to":vt()}],"mask-image-linear-from-color":[{"mask-linear-from":j()}],"mask-image-linear-to-color":[{"mask-linear-to":j()}],"mask-image-t-from-pos":[{"mask-t-from":vt()}],"mask-image-t-to-pos":[{"mask-t-to":vt()}],"mask-image-t-from-color":[{"mask-t-from":j()}],"mask-image-t-to-color":[{"mask-t-to":j()}],"mask-image-r-from-pos":[{"mask-r-from":vt()}],"mask-image-r-to-pos":[{"mask-r-to":vt()}],"mask-image-r-from-color":[{"mask-r-from":j()}],"mask-image-r-to-color":[{"mask-r-to":j()}],"mask-image-b-from-pos":[{"mask-b-from":vt()}],"mask-image-b-to-pos":[{"mask-b-to":vt()}],"mask-image-b-from-color":[{"mask-b-from":j()}],"mask-image-b-to-color":[{"mask-b-to":j()}],"mask-image-l-from-pos":[{"mask-l-from":vt()}],"mask-image-l-to-pos":[{"mask-l-to":vt()}],"mask-image-l-from-color":[{"mask-l-from":j()}],"mask-image-l-to-color":[{"mask-l-to":j()}],"mask-image-x-from-pos":[{"mask-x-from":vt()}],"mask-image-x-to-pos":[{"mask-x-to":vt()}],"mask-image-x-from-color":[{"mask-x-from":j()}],"mask-image-x-to-color":[{"mask-x-to":j()}],"mask-image-y-from-pos":[{"mask-y-from":vt()}],"mask-image-y-to-pos":[{"mask-y-to":vt()}],"mask-image-y-from-color":[{"mask-y-from":j()}],"mask-image-y-to-color":[{"mask-y-to":j()}],"mask-image-radial":[{"mask-radial":[V,Q]}],"mask-image-radial-from-pos":[{"mask-radial-from":vt()}],"mask-image-radial-to-pos":[{"mask-radial-to":vt()}],"mask-image-radial-from-color":[{"mask-radial-from":j()}],"mask-image-radial-to-color":[{"mask-radial-to":j()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":et()}],"mask-image-conic-pos":[{"mask-conic":[it]}],"mask-image-conic-from-pos":[{"mask-conic-from":vt()}],"mask-image-conic-to-pos":[{"mask-conic-to":vt()}],"mask-image-conic-from-color":[{"mask-conic-from":j()}],"mask-image-conic-to-color":[{"mask-conic-to":j()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:bt()}],"mask-repeat":[{mask:h()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",V,Q]}],filter:[{filter:["","none",V,Q]}],blur:[{blur:Rt()}],brightness:[{brightness:[it,V,Q]}],contrast:[{contrast:[it,V,Q]}],"drop-shadow":[{"drop-shadow":["","none",k,ii,ui]}],"drop-shadow-color":[{"drop-shadow":j()}],grayscale:[{grayscale:["",it,V,Q]}],"hue-rotate":[{"hue-rotate":[it,V,Q]}],invert:[{invert:["",it,V,Q]}],saturate:[{saturate:[it,V,Q]}],sepia:[{sepia:["",it,V,Q]}],"backdrop-filter":[{"backdrop-filter":["","none",V,Q]}],"backdrop-blur":[{"backdrop-blur":Rt()}],"backdrop-brightness":[{"backdrop-brightness":[it,V,Q]}],"backdrop-contrast":[{"backdrop-contrast":[it,V,Q]}],"backdrop-grayscale":[{"backdrop-grayscale":["",it,V,Q]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[it,V,Q]}],"backdrop-invert":[{"backdrop-invert":["",it,V,Q]}],"backdrop-opacity":[{"backdrop-opacity":[it,V,Q]}],"backdrop-saturate":[{"backdrop-saturate":[it,V,Q]}],"backdrop-sepia":[{"backdrop-sepia":["",it,V,Q]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Z()}],"border-spacing-x":[{"border-spacing-x":Z()}],"border-spacing-y":[{"border-spacing-y":Z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",V,Q]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[it,"initial",V,Q]}],ease:[{ease:["linear","initial",Tt,V,Q]}],delay:[{delay:[it,V,Q]}],animate:[{animate:["none",jt,V,Q]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[tt,V,Q]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:ce()}],"rotate-x":[{"rotate-x":ce()}],"rotate-y":[{"rotate-y":ce()}],"rotate-z":[{"rotate-z":ce()}],scale:[{scale:ll()}],"scale-x":[{"scale-x":ll()}],"scale-y":[{"scale-y":ll()}],"scale-z":[{"scale-z":ll()}],"scale-3d":["scale-3d"],skew:[{skew:al()}],"skew-x":[{"skew-x":al()}],"skew-y":[{"skew-y":al()}],transform:[{transform:[V,Q,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:nl()}],"translate-x":[{"translate-x":nl()}],"translate-y":[{"translate-y":nl()}],"translate-z":[{"translate-z":nl()}],"translate-none":["translate-none"],accent:[{accent:j()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:j()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V,Q]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Z()}],"scroll-mx":[{"scroll-mx":Z()}],"scroll-my":[{"scroll-my":Z()}],"scroll-ms":[{"scroll-ms":Z()}],"scroll-me":[{"scroll-me":Z()}],"scroll-mt":[{"scroll-mt":Z()}],"scroll-mr":[{"scroll-mr":Z()}],"scroll-mb":[{"scroll-mb":Z()}],"scroll-ml":[{"scroll-ml":Z()}],"scroll-p":[{"scroll-p":Z()}],"scroll-px":[{"scroll-px":Z()}],"scroll-py":[{"scroll-py":Z()}],"scroll-ps":[{"scroll-ps":Z()}],"scroll-pe":[{"scroll-pe":Z()}],"scroll-pt":[{"scroll-pt":Z()}],"scroll-pr":[{"scroll-pr":Z()}],"scroll-pb":[{"scroll-pb":Z()}],"scroll-pl":[{"scroll-pl":Z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V,Q]}],fill:[{fill:["none",...j()]}],"stroke-w":[{stroke:[it,Xn,Jl,jr]}],stroke:[{stroke:["none",...j()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},zg=ug(Eg);function el(...c){return zg(vm(c))}const _g=gm("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Be({className:c,variant:s,size:f,asChild:o=!1,...m}){const b=o?mm:"button";return T.jsx(b,{"data-slot":"button",className:el(_g({variant:s,size:f,className:c})),...m})}function nm({className:c,type:s,...f}){return T.jsx("input",{type:s,"data-slot":"input",className:el("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",c),...f})}function ci({className:c,...s}){return T.jsx("div",{"data-slot":"card",className:el("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",c),...s})}function um({className:c,...s}){return T.jsx("div",{"data-slot":"card-header",className:el("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",c),...s})}function im({className:c,...s}){return T.jsx("div",{"data-slot":"card-title",className:el("leading-none font-semibold",c),...s})}function ri({className:c,...s}){return T.jsx("div",{"data-slot":"card-content",className:el("px-6",c),...s})}const Ng=gm("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function cm({className:c,variant:s,asChild:f=!1,...o}){const m=f?mm:"span";return T.jsx(m,{"data-slot":"badge",className:el(Ng({variant:s}),c),...o})}om();var Mg=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ln=Mg.reduce((c,s)=>{const f=dm(`Primitive.${s}`),o=w.forwardRef((m,b)=>{const{asChild:z,...M}=m,p=z?f:s;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),T.jsx(p,{...M,ref:b})});return o.displayName=`Primitive.${s}`,{...c,[s]:o}},{}),Yr=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{};function Rg(c,s){return w.useReducer((f,o)=>s[f][o]??f,c)}var Qn=c=>{const{present:s,children:f}=c,o=Og(s),m=typeof f=="function"?f({present:o.isPresent}):w.Children.only(f),b=Fl(o.ref,Dg(m));return typeof f=="function"||o.isPresent?w.cloneElement(m,{ref:b}):null};Qn.displayName="Presence";function Og(c){const[s,f]=w.useState(),o=w.useRef(null),m=w.useRef(c),b=w.useRef("none"),z=c?"mounted":"unmounted",[M,p]=Rg(z,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const g=oi(o.current);b.current=M==="mounted"?g:"none"},[M]),Yr(()=>{const g=o.current,N=m.current;if(N!==c){const H=b.current,k=oi(g);c?p("MOUNT"):k==="none"||(g==null?void 0:g.display)==="none"?p("UNMOUNT"):p(N&&H!==k?"ANIMATION_OUT":"UNMOUNT"),m.current=c}},[c,p]),Yr(()=>{if(s){let g;const N=s.ownerDocument.defaultView??window,C=k=>{const tt=oi(o.current).includes(k.animationName);if(k.target===s&&tt&&(p("ANIMATION_END"),!m.current)){const ot=s.style.animationFillMode;s.style.animationFillMode="forwards",g=N.setTimeout(()=>{s.style.animationFillMode==="forwards"&&(s.style.animationFillMode=ot)})}},H=k=>{k.target===s&&(b.current=oi(o.current))};return s.addEventListener("animationstart",H),s.addEventListener("animationcancel",C),s.addEventListener("animationend",C),()=>{N.clearTimeout(g),s.removeEventListener("animationstart",H),s.removeEventListener("animationcancel",C),s.removeEventListener("animationend",C)}}else p("ANIMATION_END")},[s,p]),{isPresent:["mounted","unmountSuspended"].includes(M),ref:w.useCallback(g=>{o.current=g?getComputedStyle(g):null,f(g)},[])}}function oi(c){return(c==null?void 0:c.animationName)||"none"}function Dg(c){var o,m;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,f=s&&"isReactWarning"in s&&s.isReactWarning;return f?c.ref:(s=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,f=s&&"isReactWarning"in s&&s.isReactWarning,f?c.props.ref:c.props.ref||c.ref)}function wg(c,s=[]){let f=[];function o(b,z){const M=w.createContext(z),p=f.length;f=[...f,z];const g=C=>{var Tt;const{scope:H,children:k,...J}=C,tt=((Tt=H==null?void 0:H[c])==null?void 0:Tt[p])||M,ot=w.useMemo(()=>J,Object.values(J));return T.jsx(tt.Provider,{value:ot,children:k})};g.displayName=b+"Provider";function N(C,H){var tt;const k=((tt=H==null?void 0:H[c])==null?void 0:tt[p])||M,J=w.useContext(k);if(J)return J;if(z!==void 0)return z;throw new Error(`\`${C}\` must be used within \`${b}\``)}return[g,N]}const m=()=>{const b=f.map(z=>w.createContext(z));return function(M){const p=(M==null?void 0:M[c])||b;return w.useMemo(()=>({[`__scope${c}`]:{...M,[c]:p}}),[M,p])}};return m.scopeName=c,[o,Ug(m,...s)]}function Ug(...c){const s=c[0];if(c.length===1)return s;const f=()=>{const o=c.map(m=>({useScope:m(),scopeName:m.scopeName}));return function(b){const z=o.reduce((M,{useScope:p,scopeName:g})=>{const C=p(b)[`__scope${g}`];return{...M,...C}},{});return w.useMemo(()=>({[`__scope${s.scopeName}`]:z}),[z])}};return f.scopeName=s.scopeName,f}function Wl(c){const s=w.useRef(c);return w.useEffect(()=>{s.current=c}),w.useMemo(()=>(...f)=>{var o;return(o=s.current)==null?void 0:o.call(s,...f)},[])}var Cg=w.createContext(void 0);function jg(c){const s=w.useContext(Cg);return c||s||"ltr"}function Hg(c,[s,f]){return Math.min(f,Math.max(s,c))}function $l(c,s,{checkForDefaultPrevented:f=!0}={}){return function(m){if(c==null||c(m),f===!1||!m.defaultPrevented)return s==null?void 0:s(m)}}function Bg(c,s){return w.useReducer((f,o)=>s[f][o]??f,c)}var Lr="ScrollArea",[Nm,ty]=wg(Lr),[qg,_e]=Nm(Lr),Mm=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,type:o="hover",dir:m,scrollHideDelay:b=600,...z}=c,[M,p]=w.useState(null),[g,N]=w.useState(null),[C,H]=w.useState(null),[k,J]=w.useState(null),[tt,ot]=w.useState(null),[Tt,jt]=w.useState(0),[pt,et]=w.useState(0),[P,St]=w.useState(!1),[Nt,Z]=w.useState(!1),at=Fl(s,G=>p(G)),Ut=jg(m);return T.jsx(qg,{scope:f,type:o,dir:Ut,scrollHideDelay:b,scrollArea:M,viewport:g,onViewportChange:N,content:C,onContentChange:H,scrollbarX:k,onScrollbarXChange:J,scrollbarXEnabled:P,onScrollbarXEnabledChange:St,scrollbarY:tt,onScrollbarYChange:ot,scrollbarYEnabled:Nt,onScrollbarYEnabledChange:Z,onCornerWidthChange:jt,onCornerHeightChange:et,children:T.jsx(Ln.div,{dir:Ut,...z,ref:at,style:{position:"relative","--radix-scroll-area-corner-width":Tt+"px","--radix-scroll-area-corner-height":pt+"px",...c.style}})})});Mm.displayName=Lr;var Rm="ScrollAreaViewport",Om=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,children:o,nonce:m,...b}=c,z=_e(Rm,f),M=w.useRef(null),p=Fl(s,M,z.onViewportChange);return T.jsxs(T.Fragment,{children:[T.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:m}),T.jsx(Ln.div,{"data-radix-scroll-area-viewport":"",...b,ref:p,style:{overflowX:z.scrollbarXEnabled?"scroll":"hidden",overflowY:z.scrollbarYEnabled?"scroll":"hidden",...c.style},children:T.jsx("div",{ref:z.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});Om.displayName=Rm;var qe="ScrollAreaScrollbar",Dm=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=_e(qe,c.__scopeScrollArea),{onScrollbarXEnabledChange:b,onScrollbarYEnabledChange:z}=m,M=c.orientation==="horizontal";return w.useEffect(()=>(M?b(!0):z(!0),()=>{M?b(!1):z(!1)}),[M,b,z]),m.type==="hover"?T.jsx(Yg,{...o,ref:s,forceMount:f}):m.type==="scroll"?T.jsx(Gg,{...o,ref:s,forceMount:f}):m.type==="auto"?T.jsx(wm,{...o,ref:s,forceMount:f}):m.type==="always"?T.jsx(Qr,{...o,ref:s}):null});Dm.displayName=qe;var Yg=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=_e(qe,c.__scopeScrollArea),[b,z]=w.useState(!1);return w.useEffect(()=>{const M=m.scrollArea;let p=0;if(M){const g=()=>{window.clearTimeout(p),z(!0)},N=()=>{p=window.setTimeout(()=>z(!1),m.scrollHideDelay)};return M.addEventListener("pointerenter",g),M.addEventListener("pointerleave",N),()=>{window.clearTimeout(p),M.removeEventListener("pointerenter",g),M.removeEventListener("pointerleave",N)}}},[m.scrollArea,m.scrollHideDelay]),T.jsx(Qn,{present:f||b,children:T.jsx(wm,{"data-state":b?"visible":"hidden",...o,ref:s})})}),Gg=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=_e(qe,c.__scopeScrollArea),b=c.orientation==="horizontal",z=mi(()=>p("SCROLL_END"),100),[M,p]=Bg("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return w.useEffect(()=>{if(M==="idle"){const g=window.setTimeout(()=>p("HIDE"),m.scrollHideDelay);return()=>window.clearTimeout(g)}},[M,m.scrollHideDelay,p]),w.useEffect(()=>{const g=m.viewport,N=b?"scrollLeft":"scrollTop";if(g){let C=g[N];const H=()=>{const k=g[N];C!==k&&(p("SCROLL"),z()),C=k};return g.addEventListener("scroll",H),()=>g.removeEventListener("scroll",H)}},[m.viewport,b,p,z]),T.jsx(Qn,{present:f||M!=="hidden",children:T.jsx(Qr,{"data-state":M==="hidden"?"hidden":"visible",...o,ref:s,onPointerEnter:$l(c.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:$l(c.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),wm=w.forwardRef((c,s)=>{const f=_e(qe,c.__scopeScrollArea),{forceMount:o,...m}=c,[b,z]=w.useState(!1),M=c.orientation==="horizontal",p=mi(()=>{if(f.viewport){const g=f.viewport.offsetWidth<f.viewport.scrollWidth,N=f.viewport.offsetHeight<f.viewport.scrollHeight;z(M?g:N)}},10);return qa(f.viewport,p),qa(f.content,p),T.jsx(Qn,{present:o||b,children:T.jsx(Qr,{"data-state":b?"visible":"hidden",...m,ref:s})})}),Qr=w.forwardRef((c,s)=>{const{orientation:f="vertical",...o}=c,m=_e(qe,c.__scopeScrollArea),b=w.useRef(null),z=w.useRef(0),[M,p]=w.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),g=Bm(M.viewport,M.content),N={...o,sizes:M,onSizesChange:p,hasThumb:g>0&&g<1,onThumbChange:H=>b.current=H,onThumbPointerUp:()=>z.current=0,onThumbPointerDown:H=>z.current=H};function C(H,k){return kg(H,z.current,M,k)}return f==="horizontal"?T.jsx(Xg,{...N,ref:s,onThumbPositionChange:()=>{if(m.viewport&&b.current){const H=m.viewport.scrollLeft,k=rm(H,M,m.dir);b.current.style.transform=`translate3d(${k}px, 0, 0)`}},onWheelScroll:H=>{m.viewport&&(m.viewport.scrollLeft=H)},onDragScroll:H=>{m.viewport&&(m.viewport.scrollLeft=C(H,m.dir))}}):f==="vertical"?T.jsx(Lg,{...N,ref:s,onThumbPositionChange:()=>{if(m.viewport&&b.current){const H=m.viewport.scrollTop,k=rm(H,M);b.current.style.transform=`translate3d(0, ${k}px, 0)`}},onWheelScroll:H=>{m.viewport&&(m.viewport.scrollTop=H)},onDragScroll:H=>{m.viewport&&(m.viewport.scrollTop=C(H))}}):null}),Xg=w.forwardRef((c,s)=>{const{sizes:f,onSizesChange:o,...m}=c,b=_e(qe,c.__scopeScrollArea),[z,M]=w.useState(),p=w.useRef(null),g=Fl(s,p,b.onScrollbarXChange);return w.useEffect(()=>{p.current&&M(getComputedStyle(p.current))},[p]),T.jsx(Cm,{"data-orientation":"horizontal",...m,ref:g,sizes:f,style:{bottom:0,left:b.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:b.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":di(f)+"px",...c.style},onThumbPointerDown:N=>c.onThumbPointerDown(N.x),onDragScroll:N=>c.onDragScroll(N.x),onWheelScroll:(N,C)=>{if(b.viewport){const H=b.viewport.scrollLeft+N.deltaX;c.onWheelScroll(H),Ym(H,C)&&N.preventDefault()}},onResize:()=>{p.current&&b.viewport&&z&&o({content:b.viewport.scrollWidth,viewport:b.viewport.offsetWidth,scrollbar:{size:p.current.clientWidth,paddingStart:fi(z.paddingLeft),paddingEnd:fi(z.paddingRight)}})}})}),Lg=w.forwardRef((c,s)=>{const{sizes:f,onSizesChange:o,...m}=c,b=_e(qe,c.__scopeScrollArea),[z,M]=w.useState(),p=w.useRef(null),g=Fl(s,p,b.onScrollbarYChange);return w.useEffect(()=>{p.current&&M(getComputedStyle(p.current))},[p]),T.jsx(Cm,{"data-orientation":"vertical",...m,ref:g,sizes:f,style:{top:0,right:b.dir==="ltr"?0:void 0,left:b.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":di(f)+"px",...c.style},onThumbPointerDown:N=>c.onThumbPointerDown(N.y),onDragScroll:N=>c.onDragScroll(N.y),onWheelScroll:(N,C)=>{if(b.viewport){const H=b.viewport.scrollTop+N.deltaY;c.onWheelScroll(H),Ym(H,C)&&N.preventDefault()}},onResize:()=>{p.current&&b.viewport&&z&&o({content:b.viewport.scrollHeight,viewport:b.viewport.offsetHeight,scrollbar:{size:p.current.clientHeight,paddingStart:fi(z.paddingTop),paddingEnd:fi(z.paddingBottom)}})}})}),[Qg,Um]=Nm(qe),Cm=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,sizes:o,hasThumb:m,onThumbChange:b,onThumbPointerUp:z,onThumbPointerDown:M,onThumbPositionChange:p,onDragScroll:g,onWheelScroll:N,onResize:C,...H}=c,k=_e(qe,f),[J,tt]=w.useState(null),ot=Fl(s,at=>tt(at)),Tt=w.useRef(null),jt=w.useRef(""),pt=k.viewport,et=o.content-o.viewport,P=Wl(N),St=Wl(p),Nt=mi(C,10);function Z(at){if(Tt.current){const Ut=at.clientX-Tt.current.left,G=at.clientY-Tt.current.top;g({x:Ut,y:G})}}return w.useEffect(()=>{const at=Ut=>{const G=Ut.target;(J==null?void 0:J.contains(G))&&P(Ut,et)};return document.addEventListener("wheel",at,{passive:!1}),()=>document.removeEventListener("wheel",at,{passive:!1})},[pt,J,et,P]),w.useEffect(St,[o,St]),qa(J,Nt),qa(k.content,Nt),T.jsx(Qg,{scope:f,scrollbar:J,hasThumb:m,onThumbChange:Wl(b),onThumbPointerUp:Wl(z),onThumbPositionChange:St,onThumbPointerDown:Wl(M),children:T.jsx(Ln.div,{...H,ref:ot,style:{position:"absolute",...H.style},onPointerDown:$l(c.onPointerDown,at=>{at.button===0&&(at.target.setPointerCapture(at.pointerId),Tt.current=J.getBoundingClientRect(),jt.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",k.viewport&&(k.viewport.style.scrollBehavior="auto"),Z(at))}),onPointerMove:$l(c.onPointerMove,Z),onPointerUp:$l(c.onPointerUp,at=>{const Ut=at.target;Ut.hasPointerCapture(at.pointerId)&&Ut.releasePointerCapture(at.pointerId),document.body.style.webkitUserSelect=jt.current,k.viewport&&(k.viewport.style.scrollBehavior=""),Tt.current=null})})})}),si="ScrollAreaThumb",jm=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=Um(si,c.__scopeScrollArea);return T.jsx(Qn,{present:f||m.hasThumb,children:T.jsx(Vg,{ref:s,...o})})}),Vg=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,style:o,...m}=c,b=_e(si,f),z=Um(si,f),{onThumbPositionChange:M}=z,p=Fl(s,C=>z.onThumbChange(C)),g=w.useRef(void 0),N=mi(()=>{g.current&&(g.current(),g.current=void 0)},100);return w.useEffect(()=>{const C=b.viewport;if(C){const H=()=>{if(N(),!g.current){const k=Kg(C,M);g.current=k,M()}};return M(),C.addEventListener("scroll",H),()=>C.removeEventListener("scroll",H)}},[b.viewport,N,M]),T.jsx(Ln.div,{"data-state":z.hasThumb?"visible":"hidden",...m,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:$l(c.onPointerDownCapture,C=>{const k=C.target.getBoundingClientRect(),J=C.clientX-k.left,tt=C.clientY-k.top;z.onThumbPointerDown({x:J,y:tt})}),onPointerUp:$l(c.onPointerUp,z.onThumbPointerUp)})});jm.displayName=si;var Vr="ScrollAreaCorner",Hm=w.forwardRef((c,s)=>{const f=_e(Vr,c.__scopeScrollArea),o=!!(f.scrollbarX&&f.scrollbarY);return f.type!=="scroll"&&o?T.jsx(Zg,{...c,ref:s}):null});Hm.displayName=Vr;var Zg=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,...o}=c,m=_e(Vr,f),[b,z]=w.useState(0),[M,p]=w.useState(0),g=!!(b&&M);return qa(m.scrollbarX,()=>{var C;const N=((C=m.scrollbarX)==null?void 0:C.offsetHeight)||0;m.onCornerHeightChange(N),p(N)}),qa(m.scrollbarY,()=>{var C;const N=((C=m.scrollbarY)==null?void 0:C.offsetWidth)||0;m.onCornerWidthChange(N),z(N)}),g?T.jsx(Ln.div,{...o,ref:s,style:{width:b,height:M,position:"absolute",right:m.dir==="ltr"?0:void 0,left:m.dir==="rtl"?0:void 0,bottom:0,...c.style}}):null});function fi(c){return c?parseInt(c,10):0}function Bm(c,s){const f=c/s;return isNaN(f)?0:f}function di(c){const s=Bm(c.viewport,c.content),f=c.scrollbar.paddingStart+c.scrollbar.paddingEnd,o=(c.scrollbar.size-f)*s;return Math.max(o,18)}function kg(c,s,f,o="ltr"){const m=di(f),b=m/2,z=s||b,M=m-z,p=f.scrollbar.paddingStart+z,g=f.scrollbar.size-f.scrollbar.paddingEnd-M,N=f.content-f.viewport,C=o==="ltr"?[0,N]:[N*-1,0];return qm([p,g],C)(c)}function rm(c,s,f="ltr"){const o=di(s),m=s.scrollbar.paddingStart+s.scrollbar.paddingEnd,b=s.scrollbar.size-m,z=s.content-s.viewport,M=b-o,p=f==="ltr"?[0,z]:[z*-1,0],g=Hg(c,p);return qm([0,z],[0,M])(g)}function qm(c,s){return f=>{if(c[0]===c[1]||s[0]===s[1])return s[0];const o=(s[1]-s[0])/(c[1]-c[0]);return s[0]+o*(f-c[0])}}function Ym(c,s){return c>0&&c<s}var Kg=(c,s=()=>{})=>{let f={left:c.scrollLeft,top:c.scrollTop},o=0;return function m(){const b={left:c.scrollLeft,top:c.scrollTop},z=f.left!==b.left,M=f.top!==b.top;(z||M)&&s(),f=b,o=window.requestAnimationFrame(m)}(),()=>window.cancelAnimationFrame(o)};function mi(c,s){const f=Wl(c),o=w.useRef(0);return w.useEffect(()=>()=>window.clearTimeout(o.current),[]),w.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(f,s)},[f,s])}function qa(c,s){const f=Wl(s);Yr(()=>{let o=0;if(c){const m=new ResizeObserver(()=>{cancelAnimationFrame(o),o=window.requestAnimationFrame(f)});return m.observe(c),()=>{window.cancelAnimationFrame(o),m.unobserve(c)}}},[c,f])}var Jg=Mm,Wg=Om,$g=Hm;function Fg({className:c,children:s,...f}){return T.jsxs(Jg,{"data-slot":"scroll-area",className:el("relative",c),...f,children:[T.jsx(Wg,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:s}),T.jsx(Pg,{}),T.jsx($g,{})]})}function Pg({className:c,orientation:s="vertical",...f}){return T.jsx(Dm,{"data-slot":"scroll-area-scrollbar",orientation:s,className:el("flex touch-none p-px transition-colors select-none",s==="vertical"&&"h-full w-2.5 border-l border-l-transparent",s==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",c),...f,children:T.jsx(jm,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function Ig(){const[c,s]=w.useState(""),[f,o]=w.useState("en"),[m,b]=w.useState([]),[z,M]=w.useState(!1),[p,g]=w.useState(""),[N,C]=w.useState([]),[H,k]=w.useState(""),[J,tt]=w.useState(!1),[ot,Tt]=w.useState(!1),[jt,pt]=w.useState(!0),et={en:{title:"Book Information Retrieval System",searchPlaceholder:"Enter book name or ask a question...",searchButton:"Search Books",categories:"Categories",downloadPdf:"Download PDF",publicDomain:"Public Domain - Free to Download",noResults:"No books found. Try a different search term.",error:"An error occurred while searching. Please try again.",loading:"Searching for books...",pdfLinks:"PDF Downloads",convert:"Convert",download:"Download",chatTitle:"AI Assistant",chatPlaceholder:"Ask me anything about books...",sendMessage:"Send",llmLoading:"AI is thinking...",relatedBooks:"Related Books",askAboutBook:"Ask AI about this book",askAboutRelated:"Get related books from AI"},ar:{title:"نظام استرجاع معلومات الكتب",searchPlaceholder:"أدخل اسم الكتاب أو اطرح سؤالاً...",searchButton:"البحث عن الكتب",categories:"التصنيفات",downloadPdf:"تحميل PDF",publicDomain:"ملكية عامة - مجاني للتحميل",noResults:"لم يتم العثور على كتب. جرب مصطلح بحث مختلف.",error:"حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.",loading:"البحث عن الكتب...",pdfLinks:"تحميلات PDF",convert:"تحويل",download:"تحميل",chatTitle:"مساعد الذكاء الاصطناعي",chatPlaceholder:"اسألني أي شيء عن الكتب...",sendMessage:"إرسال",llmLoading:"الذكاء الاصطناعي يفكر...",relatedBooks:"كتب ذات صلة",askAboutBook:"اسأل الذكاء الاصطناعي عن هذا الكتاب",askAboutRelated:"احصل على كتب ذات صلة من الذكاء الاصطناعي"}}[f];w.useEffect(()=>{document.documentElement.dir=f==="ar"?"rtl":"ltr"},[f]);const P=async()=>{if(c.trim()){M(!0),g(""),b([]);try{const G=await fetch("/api/books/pdf-priority-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:c,lang:f})});if(!G.ok)throw new Error("Search failed");const lt=await G.json();b(lt.results||[])}catch(G){g("Failed to search books. Please try again."),console.error("Search error:",G)}finally{M(!1)}}},St=async(G,lt)=>{try{const ct=await fetch("/api/books/convert-to-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_url:G,output_filename:`converted_book.${lt}.pdf`})});if(ct.ok){const Mt=await ct.blob(),st=window.URL.createObjectURL(Mt),_=document.createElement("a");_.href=st,_.download=`converted_book.${lt}.pdf`,document.body.appendChild(_),_.click(),window.URL.revokeObjectURL(st),document.body.removeChild(_)}else alert("Conversion failed. Please try again.")}catch(ct){console.error("Conversion error:",ct),alert("Conversion failed. Please try again.")}},Nt=async()=>{if(!H.trim())return;const G=H;C(lt=>[...lt,{sender:"user",text:G}]),k(""),tt(!0);try{const lt=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:G})});if(!lt.ok)throw new Error("LLM chat failed");const ct=await lt.json();C(Mt=>[...Mt,{sender:"ai",text:ct.response}])}catch(lt){console.error("LLM chat error:",lt),C(ct=>[...ct,{sender:"ai",text:et.error}])}finally{tt(!1)}},Z=async(G,lt)=>{tt(!0),C(ct=>[...ct,{sender:"user",text:`${et.askAboutRelated}: ${G} by ${lt}`}]);try{const ct=await fetch("/api/llm/related-books",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:G,author:lt})});if(!ct.ok)throw new Error("Related books failed");const Mt=await ct.json();if(Mt.related_books&&Mt.related_books.length>0){const st=Mt.related_books.map(_=>`${_.title} - ${_.author}`).join(`
`);C(_=>[..._,{sender:"ai",text:`${et.relatedBooks}:
${st}`}])}else C(st=>[...st,{sender:"ai",text:"No related books found."}])}catch(ct){console.error("Related books error:",ct),C(Mt=>[...Mt,{sender:"ai",text:et.error}])}finally{tt(!1)}},at=async(G,lt,ct)=>{const Mt=`Tell me about the book: ${G} by ${lt}. Here is a brief description: ${ct}.`;C(st=>[...st,{sender:"user",text:`${et.askAboutBook}: ${G}`}]),tt(!0);try{const st=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:Mt})});if(!st.ok)throw new Error("LLM chat failed");const _=await st.json();C(q=>[...q,{sender:"ai",text:_.response}])}catch(st){console.error("LLM chat error:",st),C(_=>[..._,{sender:"ai",text:et.error}])}finally{tt(!1)}},Ut=()=>{o(f==="en"?"ar":"en")};return T.jsx("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${f==="ar"?"rtl":"ltr"}`,children:T.jsxs("div",{className:"container mx-auto px-4 py-8",children:[T.jsxs("div",{className:"text-center mb-8",children:[T.jsxs("div",{className:"flex justify-between items-center mb-4",children:[T.jsxs("div",{className:"flex items-center gap-2",children:[T.jsx($d,{className:"h-8 w-8 text-blue-600"}),T.jsx("span",{className:"text-xl font-bold text-blue-600",children:"BookFinder AI"})]}),T.jsxs(Be,{variant:"outline",size:"sm",onClick:Ut,className:"flex items-center gap-2",children:[T.jsx(N0,{className:"h-4 w-4"}),f==="en"?"العربية":"English"]})]}),T.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:et.title}),T.jsx("p",{className:"text-lg text-gray-600",children:et.subtitle})]}),T.jsx(ci,{className:"max-w-2xl mx-auto mb-8",children:T.jsx(ri,{className:"p-6",children:T.jsxs("div",{className:"flex gap-4",children:[T.jsx("div",{className:"flex-1",children:T.jsx(nm,{type:"text",placeholder:et.searchPlaceholder,value:c,onChange:G=>s(G.target.value),onKeyPress:G=>G.key==="Enter"&&P(),className:"text-lg",dir:f==="ar"?"rtl":"ltr"})}),T.jsxs(Be,{onClick:P,disabled:z||!c.trim(),className:"px-6",children:[T.jsx(C0,{className:"h-4 w-4 mr-2"}),et.searchButton]})]})})}),z&&T.jsxs("div",{className:"text-center py-8",children:[T.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),T.jsx("p",{className:"text-gray-600",children:et.loading})]}),p&&T.jsx("div",{className:"max-w-2xl mx-auto mb-8",children:T.jsx(ci,{className:"border-red-200 bg-red-50",children:T.jsx(ri,{className:"p-4",children:T.jsx("p",{className:"text-red-600 text-center",children:p})})})}),m.length>0&&T.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:m.map((G,lt)=>T.jsxs(ci,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[T.jsx(um,{className:"pb-4",children:T.jsxs("div",{className:"flex gap-4",children:[T.jsx("img",{src:G.thumbnail||"/api/placeholder/120/180",alt:G.title,className:"w-20 h-30 object-cover rounded",onError:ct=>{ct.target.src="/api/placeholder/120/180"}}),T.jsxs("div",{className:"flex-1",children:[T.jsx(im,{className:"text-lg mb-2 line-clamp-2",children:G.title}),T.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-2",children:[T.jsx(q0,{className:"h-4 w-4"}),T.jsx("span",{children:G.author})]}),G.description&&T.jsx("p",{className:"text-xs text-gray-500 line-clamp-3",children:G.description})]})]})}),T.jsx(ri,{children:T.jsxs("div",{className:"space-y-3",children:[T.jsxs("div",{children:[T.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[T.jsx(H0,{className:"h-4 w-4 text-gray-500"}),T.jsx("span",{className:"text-sm font-medium",children:et.categories})]}),T.jsx("div",{className:"flex flex-wrap gap-1",children:G.categories.map((ct,Mt)=>T.jsx(cm,{variant:"secondary",className:"text-xs",children:ct},Mt))})]}),G.pdf_links&&G.pdf_links.length>0&&T.jsxs("div",{className:"pt-2",children:[T.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[T.jsx(ni,{className:"h-4 w-4 text-green-600"}),T.jsx("span",{className:"text-sm font-medium text-green-700",children:et.pdfLinks}),T.jsxs(cm,{variant:"secondary",className:"text-xs bg-green-100 text-green-800",children:[G.pdf_links.length," PDF",G.pdf_links.length>1?"s":""]})]}),T.jsx("div",{className:"space-y-2",children:G.pdf_links.map((ct,Mt)=>T.jsxs("div",{className:"flex items-center justify-between bg-green-50 border border-green-200 p-3 rounded-lg",children:[T.jsxs("div",{className:"flex items-center gap-2",children:[T.jsx(ni,{className:"h-4 w-4 text-green-600"}),T.jsxs("div",{children:[T.jsx("div",{className:"text-sm font-medium text-green-800",children:ct.source}),T.jsx("div",{className:"text-xs text-green-600",children:et.publicDomain})]})]}),ct.type==="convertible"?T.jsxs(Be,{onClick:()=>St(ct.url,ct.format),size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",children:[T.jsx(ni,{className:"h-3 w-3 mr-1"}),et.convert]}):T.jsx(Be,{asChild:!0,size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",children:T.jsxs("a",{href:ct.url,target:"_blank",rel:"noopener noreferrer",children:[T.jsx(ni,{className:"h-3 w-3 mr-1"}),et.downloadPdf]})})]},Mt))})]}),T.jsxs("div",{className:"flex flex-col gap-2 pt-2",children:[T.jsx(Be,{variant:"outline",size:"sm",onClick:()=>at(G.title,G.author,G.description),children:et.askAboutBook}),T.jsx(Be,{variant:"outline",size:"sm",onClick:()=>Z(G.title,G.author),children:et.askAboutRelated})]})]})})]},G.id||lt))}),!z&&!p&&m.length===0&&c&&T.jsxs("div",{className:"text-center py-8",children:[T.jsx($d,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),T.jsx("p",{className:"text-gray-600",children:et.noResults})]}),jt&&T.jsxs(ci,{className:`fixed ${ot?"inset-4":"bottom-4 right-4 w-96 h-[500px]"} flex flex-col shadow-xl z-50 transition-all duration-300`,children:[T.jsxs(um,{className:"flex flex-row items-center justify-between space-y-0 p-4 border-b bg-blue-50",children:[T.jsxs("div",{className:"flex items-center gap-2",children:[T.jsx(Ur,{className:"h-5 w-5 text-blue-600"}),T.jsx(im,{className:"text-lg font-semibold text-blue-800",children:et.chatTitle})]}),T.jsxs("div",{className:"flex items-center gap-1",children:[T.jsx(Be,{size:"icon",variant:"ghost",onClick:()=>Tt(!ot),className:"h-8 w-8 hover:bg-blue-100",children:ot?T.jsx(w0,{className:"h-4 w-4"}):T.jsx(R0,{className:"h-4 w-4"})}),T.jsx(Be,{size:"icon",variant:"ghost",onClick:()=>pt(!1),className:"h-8 w-8 hover:bg-red-100",children:T.jsx(G0,{className:"h-4 w-4"})})]})]}),T.jsx(ri,{className:"flex-1 p-4 overflow-hidden bg-white",children:T.jsx(Fg,{className:"h-full pr-4",children:T.jsxs("div",{className:"space-y-4",children:[N.length===0&&T.jsxs("div",{className:"text-center text-gray-500 py-8",children:[T.jsx(Ur,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),T.jsx("p",{className:"text-sm",children:"Ask me anything about books!"}),T.jsx("p",{className:"text-xs mt-2",children:'Try: "Tell me about Rich Dad Poor Dad" or "Recommend finance books"'})]}),N.map((G,lt)=>T.jsx("div",{className:`flex ${G.sender==="user"?"justify-end":"justify-start"}`,children:T.jsx("div",{className:`max-w-[85%] p-3 rounded-lg shadow-sm ${G.sender==="user"?"bg-blue-500 text-white":"bg-gray-100 text-gray-800 border"}`,children:T.jsx("div",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:G.text})})},lt)),J&&T.jsx("div",{className:"flex justify-start",children:T.jsx("div",{className:"max-w-[85%] p-3 rounded-lg bg-gray-100 text-gray-800 border animate-pulse",children:T.jsxs("div",{className:"flex items-center gap-2",children:[T.jsxs("div",{className:"flex space-x-1",children:[T.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),T.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),T.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),T.jsx("span",{className:"text-sm",children:et.llmLoading})]})})})]})})}),T.jsxs("div",{className:"p-4 border-t bg-gray-50 flex items-center gap-2",children:[T.jsx(nm,{placeholder:et.chatPlaceholder,value:H,onChange:G=>k(G.target.value),onKeyPress:G=>G.key==="Enter"&&Nt(),className:"flex-1 bg-white",dir:f==="ar"?"rtl":"ltr"}),T.jsx(Be,{size:"icon",onClick:Nt,disabled:J||!H.trim(),className:"bg-blue-600 hover:bg-blue-700",children:T.jsx(E0,{className:"h-4 w-4"})})]})]}),!jt&&T.jsx(Be,{onClick:()=>pt(!0),className:"fixed bottom-4 right-4 h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg z-50",size:"icon",children:T.jsx(Ur,{className:"h-6 w-6"})})]})})}g0.createRoot(document.getElementById("root")).render(T.jsx(w.StrictMode,{children:T.jsx(Ig,{})}));
