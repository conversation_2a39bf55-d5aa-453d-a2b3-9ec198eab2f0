(function(){const f=document.createElement("link").relList;if(f&&f.supports&&f.supports("modulepreload"))return;for(const m of document.querySelectorAll('link[rel="modulepreload"]'))o(m);new MutationObserver(m=>{for(const b of m)if(b.type==="childList")for(const E of b.addedNodes)E.tagName==="LINK"&&E.rel==="modulepreload"&&o(E)}).observe(document,{childList:!0,subtree:!0});function s(m){const b={};return m.integrity&&(b.integrity=m.integrity),m.referrerPolicy&&(b.referrerPolicy=m.referrerPolicy),m.crossOrigin==="use-credentials"?b.credentials="include":m.crossOrigin==="anonymous"?b.credentials="omit":b.credentials="same-origin",b}function o(m){if(m.ep)return;m.ep=!0;const b=s(m);fetch(m.href,b)}})();var _r={exports:{}},Yn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bd;function u0(){if(Bd)return Yn;Bd=1;var c=Symbol.for("react.transitional.element"),f=Symbol.for("react.fragment");function s(o,m,b){var E=null;if(b!==void 0&&(E=""+b),m.key!==void 0&&(E=""+m.key),"key"in m){b={};for(var M in m)M!=="key"&&(b[M]=m[M])}else b=m;return m=b.ref,{$$typeof:c,type:o,key:E,ref:m!==void 0?m:null,props:b}}return Yn.Fragment=f,Yn.jsx=s,Yn.jsxs=s,Yn}var qd;function i0(){return qd||(qd=1,_r.exports=u0()),_r.exports}var O=i0(),Mr={exports:{}},at={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yd;function c0(){if(Yd)return at;Yd=1;var c=Symbol.for("react.transitional.element"),f=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),m=Symbol.for("react.profiler"),b=Symbol.for("react.consumer"),E=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),C=Symbol.iterator;function H(h){return h===null||typeof h!="object"?null:(h=C&&h[C]||h["@@iterator"],typeof h=="function"?h:null)}var K={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},W=Object.assign,et={};function $(h,U,G){this.props=h,this.context=U,this.refs=et,this.updater=G||K}$.prototype.isReactComponent={},$.prototype.setState=function(h,U){if(typeof h!="object"&&typeof h!="function"&&h!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,h,U,"setState")},$.prototype.forceUpdate=function(h){this.updater.enqueueForceUpdate(this,h,"forceUpdate")};function bt(){}bt.prototype=$.prototype;function jt(h,U,G){this.props=h,this.context=U,this.refs=et,this.updater=G||K}var pt=jt.prototype=new bt;pt.constructor=jt,W(pt,$.prototype),pt.isPureReactComponent=!0;var Ot=Array.isArray,lt={H:null,A:null,T:null,S:null,V:null},St=Object.prototype.hasOwnProperty;function X(h,U,G,B,Q,ot){return G=ot.ref,{$$typeof:c,type:h,key:U,ref:G!==void 0?G:null,props:ot}}function q(h,U){return X(h.type,U,void 0,void 0,void 0,h.props)}function L(h){return typeof h=="object"&&h!==null&&h.$$typeof===c}function ct(h){var U={"=":"=0",":":"=2"};return"$"+h.replace(/[=:]/g,function(G){return U[G]})}var mt=/\/+/g;function rt(h,U){return typeof h=="object"&&h!==null&&h.key!=null?ct(""+h.key):U.toString(36)}function ve(){}function _e(h){switch(h.status){case"fulfilled":return h.value;case"rejected":throw h.reason;default:switch(typeof h.status=="string"?h.then(ve,ve):(h.status="pending",h.then(function(U){h.status==="pending"&&(h.status="fulfilled",h.value=U)},function(U){h.status==="pending"&&(h.status="rejected",h.reason=U)})),h.status){case"fulfilled":return h.value;case"rejected":throw h.reason}}throw h}function Dt(h,U,G,B,Q){var ot=typeof h;(ot==="undefined"||ot==="boolean")&&(h=null);var tt=!1;if(h===null)tt=!0;else switch(ot){case"bigint":case"string":case"number":tt=!0;break;case"object":switch(h.$$typeof){case c:case f:tt=!0;break;case z:return tt=h._init,Dt(tt(h._payload),U,G,B,Q)}}if(tt)return Q=Q(h),tt=B===""?"."+rt(h,0):B,Ot(Q)?(G="",tt!=null&&(G=tt.replace(mt,"$&/")+"/"),Dt(Q,U,G,"",function(ue){return ue})):Q!=null&&(L(Q)&&(Q=q(Q,G+(Q.key==null||h&&h.key===Q.key?"":(""+Q.key).replace(mt,"$&/")+"/")+tt)),U.push(Q)),1;tt=0;var ht=B===""?".":B+":";if(Ot(h))for(var _t=0;_t<h.length;_t++)B=h[_t],ot=ht+rt(B,_t),tt+=Dt(B,U,G,ot,Q);else if(_t=H(h),typeof _t=="function")for(h=_t.call(h),_t=0;!(B=h.next()).done;)B=B.value,ot=ht+rt(B,_t++),tt+=Dt(B,U,G,ot,Q);else if(ot==="object"){if(typeof h.then=="function")return Dt(_e(h),U,G,B,Q);throw U=String(h),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(h).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return tt}function _(h,U,G){if(h==null)return h;var B=[],Q=0;return Dt(h,B,"","",function(ot){return U.call(G,ot,Q++)}),B}function Y(h){if(h._status===-1){var U=h._result;U=U(),U.then(function(G){(h._status===0||h._status===-1)&&(h._status=1,h._result=G)},function(G){(h._status===0||h._status===-1)&&(h._status=2,h._result=G)}),h._status===-1&&(h._status=0,h._result=U)}if(h._status===1)return h._result.default;throw h._result}var j=typeof reportError=="function"?reportError:function(h){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof h=="object"&&h!==null&&typeof h.message=="string"?String(h.message):String(h),error:h});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",h);return}console.error(h)};function gt(){}return at.Children={map:_,forEach:function(h,U,G){_(h,function(){U.apply(this,arguments)},G)},count:function(h){var U=0;return _(h,function(){U++}),U},toArray:function(h){return _(h,function(U){return U})||[]},only:function(h){if(!L(h))throw Error("React.Children.only expected to receive a single React element child.");return h}},at.Component=$,at.Fragment=s,at.Profiler=m,at.PureComponent=jt,at.StrictMode=o,at.Suspense=p,at.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=lt,at.__COMPILER_RUNTIME={__proto__:null,c:function(h){return lt.H.useMemoCache(h)}},at.cache=function(h){return function(){return h.apply(null,arguments)}},at.cloneElement=function(h,U,G){if(h==null)throw Error("The argument must be a React element, but you passed "+h+".");var B=W({},h.props),Q=h.key,ot=void 0;if(U!=null)for(tt in U.ref!==void 0&&(ot=void 0),U.key!==void 0&&(Q=""+U.key),U)!St.call(U,tt)||tt==="key"||tt==="__self"||tt==="__source"||tt==="ref"&&U.ref===void 0||(B[tt]=U[tt]);var tt=arguments.length-2;if(tt===1)B.children=G;else if(1<tt){for(var ht=Array(tt),_t=0;_t<tt;_t++)ht[_t]=arguments[_t+2];B.children=ht}return X(h.type,Q,void 0,void 0,ot,B)},at.createContext=function(h){return h={$$typeof:E,_currentValue:h,_currentValue2:h,_threadCount:0,Provider:null,Consumer:null},h.Provider=h,h.Consumer={$$typeof:b,_context:h},h},at.createElement=function(h,U,G){var B,Q={},ot=null;if(U!=null)for(B in U.key!==void 0&&(ot=""+U.key),U)St.call(U,B)&&B!=="key"&&B!=="__self"&&B!=="__source"&&(Q[B]=U[B]);var tt=arguments.length-2;if(tt===1)Q.children=G;else if(1<tt){for(var ht=Array(tt),_t=0;_t<tt;_t++)ht[_t]=arguments[_t+2];Q.children=ht}if(h&&h.defaultProps)for(B in tt=h.defaultProps,tt)Q[B]===void 0&&(Q[B]=tt[B]);return X(h,ot,void 0,void 0,null,Q)},at.createRef=function(){return{current:null}},at.forwardRef=function(h){return{$$typeof:M,render:h}},at.isValidElement=L,at.lazy=function(h){return{$$typeof:z,_payload:{_status:-1,_result:h},_init:Y}},at.memo=function(h,U){return{$$typeof:y,type:h,compare:U===void 0?null:U}},at.startTransition=function(h){var U=lt.T,G={};lt.T=G;try{var B=h(),Q=lt.S;Q!==null&&Q(G,B),typeof B=="object"&&B!==null&&typeof B.then=="function"&&B.then(gt,j)}catch(ot){j(ot)}finally{lt.T=U}},at.unstable_useCacheRefresh=function(){return lt.H.useCacheRefresh()},at.use=function(h){return lt.H.use(h)},at.useActionState=function(h,U,G){return lt.H.useActionState(h,U,G)},at.useCallback=function(h,U){return lt.H.useCallback(h,U)},at.useContext=function(h){return lt.H.useContext(h)},at.useDebugValue=function(){},at.useDeferredValue=function(h,U){return lt.H.useDeferredValue(h,U)},at.useEffect=function(h,U,G){var B=lt.H;if(typeof G=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return B.useEffect(h,U)},at.useId=function(){return lt.H.useId()},at.useImperativeHandle=function(h,U,G){return lt.H.useImperativeHandle(h,U,G)},at.useInsertionEffect=function(h,U){return lt.H.useInsertionEffect(h,U)},at.useLayoutEffect=function(h,U){return lt.H.useLayoutEffect(h,U)},at.useMemo=function(h,U){return lt.H.useMemo(h,U)},at.useOptimistic=function(h,U){return lt.H.useOptimistic(h,U)},at.useReducer=function(h,U,G){return lt.H.useReducer(h,U,G)},at.useRef=function(h){return lt.H.useRef(h)},at.useState=function(h){return lt.H.useState(h)},at.useSyncExternalStore=function(h,U,G){return lt.H.useSyncExternalStore(h,U,G)},at.useTransition=function(){return lt.H.useTransition()},at.version="19.1.0",at}var Gd;function qr(){return Gd||(Gd=1,Mr.exports=c0()),Mr.exports}var w=qr(),Rr={exports:{}},Gn={},Or={exports:{}},Nr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xd;function r0(){return Xd||(Xd=1,function(c){function f(_,Y){var j=_.length;_.push(Y);t:for(;0<j;){var gt=j-1>>>1,h=_[gt];if(0<m(h,Y))_[gt]=Y,_[j]=h,j=gt;else break t}}function s(_){return _.length===0?null:_[0]}function o(_){if(_.length===0)return null;var Y=_[0],j=_.pop();if(j!==Y){_[0]=j;t:for(var gt=0,h=_.length,U=h>>>1;gt<U;){var G=2*(gt+1)-1,B=_[G],Q=G+1,ot=_[Q];if(0>m(B,j))Q<h&&0>m(ot,B)?(_[gt]=ot,_[Q]=j,gt=Q):(_[gt]=B,_[G]=j,gt=G);else if(Q<h&&0>m(ot,j))_[gt]=ot,_[Q]=j,gt=Q;else break t}}return Y}function m(_,Y){var j=_.sortIndex-Y.sortIndex;return j!==0?j:_.id-Y.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var b=performance;c.unstable_now=function(){return b.now()}}else{var E=Date,M=E.now();c.unstable_now=function(){return E.now()-M}}var p=[],y=[],z=1,C=null,H=3,K=!1,W=!1,et=!1,$=!1,bt=typeof setTimeout=="function"?setTimeout:null,jt=typeof clearTimeout=="function"?clearTimeout:null,pt=typeof setImmediate<"u"?setImmediate:null;function Ot(_){for(var Y=s(y);Y!==null;){if(Y.callback===null)o(y);else if(Y.startTime<=_)o(y),Y.sortIndex=Y.expirationTime,f(p,Y);else break;Y=s(y)}}function lt(_){if(et=!1,Ot(_),!W)if(s(p)!==null)W=!0,St||(St=!0,rt());else{var Y=s(y);Y!==null&&Dt(lt,Y.startTime-_)}}var St=!1,X=-1,q=5,L=-1;function ct(){return $?!0:!(c.unstable_now()-L<q)}function mt(){if($=!1,St){var _=c.unstable_now();L=_;var Y=!0;try{t:{W=!1,et&&(et=!1,jt(X),X=-1),K=!0;var j=H;try{e:{for(Ot(_),C=s(p);C!==null&&!(C.expirationTime>_&&ct());){var gt=C.callback;if(typeof gt=="function"){C.callback=null,H=C.priorityLevel;var h=gt(C.expirationTime<=_);if(_=c.unstable_now(),typeof h=="function"){C.callback=h,Ot(_),Y=!0;break e}C===s(p)&&o(p),Ot(_)}else o(p);C=s(p)}if(C!==null)Y=!0;else{var U=s(y);U!==null&&Dt(lt,U.startTime-_),Y=!1}}break t}finally{C=null,H=j,K=!1}Y=void 0}}finally{Y?rt():St=!1}}}var rt;if(typeof pt=="function")rt=function(){pt(mt)};else if(typeof MessageChannel<"u"){var ve=new MessageChannel,_e=ve.port2;ve.port1.onmessage=mt,rt=function(){_e.postMessage(null)}}else rt=function(){bt(mt,0)};function Dt(_,Y){X=bt(function(){_(c.unstable_now())},Y)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(_){_.callback=null},c.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<_?Math.floor(1e3/_):5},c.unstable_getCurrentPriorityLevel=function(){return H},c.unstable_next=function(_){switch(H){case 1:case 2:case 3:var Y=3;break;default:Y=H}var j=H;H=Y;try{return _()}finally{H=j}},c.unstable_requestPaint=function(){$=!0},c.unstable_runWithPriority=function(_,Y){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var j=H;H=_;try{return Y()}finally{H=j}},c.unstable_scheduleCallback=function(_,Y,j){var gt=c.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?gt+j:gt):j=gt,_){case 1:var h=-1;break;case 2:h=250;break;case 5:h=1073741823;break;case 4:h=1e4;break;default:h=5e3}return h=j+h,_={id:z++,callback:Y,priorityLevel:_,startTime:j,expirationTime:h,sortIndex:-1},j>gt?(_.sortIndex=j,f(y,_),s(p)===null&&_===s(y)&&(et?(jt(X),X=-1):et=!0,Dt(lt,j-gt))):(_.sortIndex=h,f(p,_),W||K||(W=!0,St||(St=!0,rt()))),_},c.unstable_shouldYield=ct,c.unstable_wrapCallback=function(_){var Y=H;return function(){var j=H;H=Y;try{return _.apply(this,arguments)}finally{H=j}}}}(Nr)),Nr}var Ld;function o0(){return Ld||(Ld=1,Or.exports=r0()),Or.exports}var Dr={exports:{}},Pt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function f0(){if(Qd)return Pt;Qd=1;var c=qr();function f(p){var y="https://react.dev/errors/"+p;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)y+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+p+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var o={d:{f:s,r:function(){throw Error(f(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},m=Symbol.for("react.portal");function b(p,y,z){var C=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:m,key:C==null?null:""+C,children:p,containerInfo:y,implementation:z}}var E=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function M(p,y){if(p==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return Pt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,Pt.createPortal=function(p,y){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(f(299));return b(p,y,null,z)},Pt.flushSync=function(p){var y=E.T,z=o.p;try{if(E.T=null,o.p=2,p)return p()}finally{E.T=y,o.p=z,o.d.f()}},Pt.preconnect=function(p,y){typeof p=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,o.d.C(p,y))},Pt.prefetchDNS=function(p){typeof p=="string"&&o.d.D(p)},Pt.preinit=function(p,y){if(typeof p=="string"&&y&&typeof y.as=="string"){var z=y.as,C=M(z,y.crossOrigin),H=typeof y.integrity=="string"?y.integrity:void 0,K=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;z==="style"?o.d.S(p,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:C,integrity:H,fetchPriority:K}):z==="script"&&o.d.X(p,{crossOrigin:C,integrity:H,fetchPriority:K,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},Pt.preinitModule=function(p,y){if(typeof p=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var z=M(y.as,y.crossOrigin);o.d.M(p,{crossOrigin:z,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&o.d.M(p)},Pt.preload=function(p,y){if(typeof p=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var z=y.as,C=M(z,y.crossOrigin);o.d.L(p,z,{crossOrigin:C,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},Pt.preloadModule=function(p,y){if(typeof p=="string")if(y){var z=M(y.as,y.crossOrigin);o.d.m(p,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:z,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else o.d.m(p)},Pt.requestFormReset=function(p){o.d.r(p)},Pt.unstable_batchedUpdates=function(p,y){return p(y)},Pt.useFormState=function(p,y,z){return E.H.useFormState(p,y,z)},Pt.useFormStatus=function(){return E.H.useHostTransitionStatus()},Pt.version="19.1.0",Pt}var Vd;function im(){if(Vd)return Dr.exports;Vd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(f){console.error(f)}}return c(),Dr.exports=f0(),Dr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zd;function s0(){if(Zd)return Gn;Zd=1;var c=o0(),f=qr(),s=im();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function m(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function b(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function E(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function M(t){if(b(t)!==t)throw Error(o(188))}function p(t){var e=t.alternate;if(!e){if(e=b(t),e===null)throw Error(o(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return M(n),t;if(u===a)return M(n),e;u=u.sibling}throw Error(o(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,r=n.child;r;){if(r===l){i=!0,l=n,a=u;break}if(r===a){i=!0,a=n,l=u;break}r=r.sibling}if(!i){for(r=u.child;r;){if(r===l){i=!0,l=u,a=n;break}if(r===a){i=!0,a=u,l=n;break}r=r.sibling}if(!i)throw Error(o(189))}}if(l.alternate!==a)throw Error(o(190))}if(l.tag!==3)throw Error(o(188));return l.stateNode.current===l?t:e}function y(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=y(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,C=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),K=Symbol.for("react.portal"),W=Symbol.for("react.fragment"),et=Symbol.for("react.strict_mode"),$=Symbol.for("react.profiler"),bt=Symbol.for("react.provider"),jt=Symbol.for("react.consumer"),pt=Symbol.for("react.context"),Ot=Symbol.for("react.forward_ref"),lt=Symbol.for("react.suspense"),St=Symbol.for("react.suspense_list"),X=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),L=Symbol.for("react.activity"),ct=Symbol.for("react.memo_cache_sentinel"),mt=Symbol.iterator;function rt(t){return t===null||typeof t!="object"?null:(t=mt&&t[mt]||t["@@iterator"],typeof t=="function"?t:null)}var ve=Symbol.for("react.client.reference");function _e(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ve?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case W:return"Fragment";case $:return"Profiler";case et:return"StrictMode";case lt:return"Suspense";case St:return"SuspenseList";case L:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case K:return"Portal";case pt:return(t.displayName||"Context")+".Provider";case jt:return(t._context.displayName||"Context")+".Consumer";case Ot:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case X:return e=t.displayName||null,e!==null?e:_e(t.type)||"Memo";case q:e=t._payload,t=t._init;try{return _e(t(e))}catch{}}return null}var Dt=Array.isArray,_=f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j={pending:!1,data:null,method:null,action:null},gt=[],h=-1;function U(t){return{current:t}}function G(t){0>h||(t.current=gt[h],gt[h]=null,h--)}function B(t,e){h++,gt[h]=t.current,t.current=e}var Q=U(null),ot=U(null),tt=U(null),ht=U(null);function _t(t,e){switch(B(tt,e),B(ot,t),B(Q,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?sd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=sd(e),t=dd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}G(Q),B(Q,t)}function ue(){G(Q),G(ot),G(tt)}function tl(t){t.memoizedState!==null&&B(ht,t);var e=Q.current,l=dd(e,t.type);e!==l&&(B(ot,t),B(Q,l))}function el(t){ot.current===t&&(G(Q),G(ot)),ht.current===t&&(G(ht),Cn._currentValue=j)}var ll=Object.prototype.hasOwnProperty,mi=c.unstable_scheduleCallback,hi=c.unstable_cancelCallback,Bm=c.unstable_shouldYield,qm=c.unstable_requestPaint,Ne=c.unstable_now,Ym=c.unstable_getCurrentPriorityLevel,Qr=c.unstable_ImmediatePriority,Vr=c.unstable_UserBlockingPriority,Vn=c.unstable_NormalPriority,Gm=c.unstable_LowPriority,Zr=c.unstable_IdlePriority,Xm=c.log,Lm=c.unstable_setDisableYieldValue,Xa=null,ie=null;function al(t){if(typeof Xm=="function"&&Lm(t),ie&&typeof ie.setStrictMode=="function")try{ie.setStrictMode(Xa,t)}catch{}}var ce=Math.clz32?Math.clz32:Zm,Qm=Math.log,Vm=Math.LN2;function Zm(t){return t>>>=0,t===0?32:31-(Qm(t)/Vm|0)|0}var Zn=256,kn=4194304;function Rl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Kn(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var r=a&134217727;return r!==0?(a=r&~u,a!==0?n=Rl(a):(i&=r,i!==0?n=Rl(i):l||(l=r&~t,l!==0&&(n=Rl(l))))):(r=a&~u,r!==0?n=Rl(r):i!==0?n=Rl(i):l||(l=a&~t,l!==0&&(n=Rl(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function La(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function km(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function kr(){var t=Zn;return Zn<<=1,(Zn&4194048)===0&&(Zn=256),t}function Kr(){var t=kn;return kn<<=1,(kn&62914560)===0&&(kn=4194304),t}function vi(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Qa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Km(t,e,l,a,n,u){var i=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var r=t.entanglements,d=t.expirationTimes,x=t.hiddenUpdates;for(l=i&~l;0<l;){var R=31-ce(l),D=1<<R;r[R]=0,d[R]=-1;var A=x[R];if(A!==null)for(x[R]=null,R=0;R<A.length;R++){var T=A[R];T!==null&&(T.lane&=-536870913)}l&=~D}a!==0&&Jr(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~e))}function Jr(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-ce(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Wr(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-ce(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function yi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function gi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function $r(){var t=Y.p;return t!==0?t:(t=window.event,t===void 0?32:Dd(t.type))}function Jm(t,e){var l=Y.p;try{return Y.p=t,e()}finally{Y.p=l}}var nl=Math.random().toString(36).slice(2),$t="__reactFiber$"+nl,te="__reactProps$"+nl,Pl="__reactContainer$"+nl,bi="__reactEvents$"+nl,Wm="__reactListeners$"+nl,$m="__reactHandles$"+nl,Fr="__reactResources$"+nl,Va="__reactMarker$"+nl;function pi(t){delete t[$t],delete t[te],delete t[bi],delete t[Wm],delete t[$m]}function Il(t){var e=t[$t];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Pl]||l[$t]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=yd(t);t!==null;){if(l=t[$t])return l;t=yd(t)}return e}t=l,l=t.parentNode}return null}function ta(t){if(t=t[$t]||t[Pl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Za(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function ea(t){var e=t[Fr];return e||(e=t[Fr]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Qt(t){t[Va]=!0}var Pr=new Set,Ir={};function Ol(t,e){la(t,e),la(t+"Capture",e)}function la(t,e){for(Ir[t]=e,t=0;t<e.length;t++)Pr.add(e[t])}var Fm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),to={},eo={};function Pm(t){return ll.call(eo,t)?!0:ll.call(to,t)?!1:Fm.test(t)?eo[t]=!0:(to[t]=!0,!1)}function Jn(t,e,l){if(Pm(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Wn(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Be(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var Si,lo;function aa(t){if(Si===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);Si=e&&e[1]||"",lo=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Si+t+lo}var xi=!1;function Ai(t,e){if(!t||xi)return"";xi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var D=function(){throw Error()};if(Object.defineProperty(D.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(D,[])}catch(T){var A=T}Reflect.construct(t,[],D)}else{try{D.call()}catch(T){A=T}t.call(D.prototype)}}else{try{throw Error()}catch(T){A=T}(D=t())&&typeof D.catch=="function"&&D.catch(function(){})}}catch(T){if(T&&A&&typeof T.stack=="string")return[T.stack,A.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],r=u[1];if(i&&r){var d=i.split(`
`),x=r.split(`
`);for(n=a=0;a<d.length&&!d[a].includes("DetermineComponentFrameRoot");)a++;for(;n<x.length&&!x[n].includes("DetermineComponentFrameRoot");)n++;if(a===d.length||n===x.length)for(a=d.length-1,n=x.length-1;1<=a&&0<=n&&d[a]!==x[n];)n--;for(;1<=a&&0<=n;a--,n--)if(d[a]!==x[n]){if(a!==1||n!==1)do if(a--,n--,0>n||d[a]!==x[n]){var R=`
`+d[a].replace(" at new "," at ");return t.displayName&&R.includes("<anonymous>")&&(R=R.replace("<anonymous>",t.displayName)),R}while(1<=a&&0<=n);break}}}finally{xi=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?aa(l):""}function Im(t){switch(t.tag){case 26:case 27:case 5:return aa(t.type);case 16:return aa("Lazy");case 13:return aa("Suspense");case 19:return aa("SuspenseList");case 0:case 15:return Ai(t.type,!1);case 11:return Ai(t.type.render,!1);case 1:return Ai(t.type,!0);case 31:return aa("Activity");default:return""}}function ao(t){try{var e="";do e+=Im(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function ye(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function no(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function th(t){var e=no(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function $n(t){t._valueTracker||(t._valueTracker=th(t))}function uo(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=no(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Fn(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var eh=/[\n"\\]/g;function ge(t){return t.replace(eh,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ti(t,e,l,a,n,u,i,r){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),e!=null?i==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ye(e)):t.value!==""+ye(e)&&(t.value=""+ye(e)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),e!=null?Ei(t,i,ye(e)):l!=null?Ei(t,i,ye(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?t.name=""+ye(r):t.removeAttribute("name")}function io(t,e,l,a,n,u,i,r){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+ye(l):"",e=e!=null?""+ye(e):l,r||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=r?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function Ei(t,e,l){e==="number"&&Fn(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function na(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+ye(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function co(t,e,l){if(e!=null&&(e=""+ye(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+ye(l):""}function ro(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(o(92));if(Dt(a)){if(1<a.length)throw Error(o(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=ye(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function ua(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var lh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function oo(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||lh.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function fo(t,e,l){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&oo(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&oo(t,u,e[u])}function zi(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ah=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),nh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pn(t){return nh.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var _i=null;function Mi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ia=null,ca=null;function so(t){var e=ta(t);if(e&&(t=e.stateNode)){var l=t[te]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ti(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+ge(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[te]||null;if(!n)throw Error(o(90));Ti(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&uo(a)}break t;case"textarea":co(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&na(t,!!l.multiple,e,!1)}}}var Ri=!1;function mo(t,e,l){if(Ri)return t(e,l);Ri=!0;try{var a=t(e);return a}finally{if(Ri=!1,(ia!==null||ca!==null)&&(Bu(),ia&&(e=ia,t=ca,ca=ia=null,so(e),t)))for(e=0;e<t.length;e++)so(t[e])}}function ka(t,e){var l=t.stateNode;if(l===null)return null;var a=l[te]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(o(231,e,typeof l));return l}var qe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Oi=!1;if(qe)try{var Ka={};Object.defineProperty(Ka,"passive",{get:function(){Oi=!0}}),window.addEventListener("test",Ka,Ka),window.removeEventListener("test",Ka,Ka)}catch{Oi=!1}var ul=null,Ni=null,In=null;function ho(){if(In)return In;var t,e=Ni,l=e.length,a,n="value"in ul?ul.value:ul.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var i=l-t;for(a=1;a<=i&&e[l-a]===n[u-a];a++);return In=n.slice(t,1<a?1-a:void 0)}function tu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function eu(){return!0}function vo(){return!1}function ee(t){function e(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var r in t)t.hasOwnProperty(r)&&(l=t[r],this[r]=l?l(u):u[r]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?eu:vo,this.isPropagationStopped=vo,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=eu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=eu)},persist:function(){},isPersistent:eu}),e}var Nl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},lu=ee(Nl),Ja=z({},Nl,{view:0,detail:0}),uh=ee(Ja),Di,Ui,Wa,au=z({},Ja,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ci,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Wa&&(Wa&&t.type==="mousemove"?(Di=t.screenX-Wa.screenX,Ui=t.screenY-Wa.screenY):Ui=Di=0,Wa=t),Di)},movementY:function(t){return"movementY"in t?t.movementY:Ui}}),yo=ee(au),ih=z({},au,{dataTransfer:0}),ch=ee(ih),rh=z({},Ja,{relatedTarget:0}),wi=ee(rh),oh=z({},Nl,{animationName:0,elapsedTime:0,pseudoElement:0}),fh=ee(oh),sh=z({},Nl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),dh=ee(sh),mh=z({},Nl,{data:0}),go=ee(mh),hh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},vh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},yh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function gh(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=yh[t])?!!e[t]:!1}function Ci(){return gh}var bh=z({},Ja,{key:function(t){if(t.key){var e=hh[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=tu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?vh[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ci,charCode:function(t){return t.type==="keypress"?tu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?tu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),ph=ee(bh),Sh=z({},au,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bo=ee(Sh),xh=z({},Ja,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ci}),Ah=ee(xh),Th=z({},Nl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Eh=ee(Th),zh=z({},au,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),_h=ee(zh),Mh=z({},Nl,{newState:0,oldState:0}),Rh=ee(Mh),Oh=[9,13,27,32],ji=qe&&"CompositionEvent"in window,$a=null;qe&&"documentMode"in document&&($a=document.documentMode);var Nh=qe&&"TextEvent"in window&&!$a,po=qe&&(!ji||$a&&8<$a&&11>=$a),So=" ",xo=!1;function Ao(t,e){switch(t){case"keyup":return Oh.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function To(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ra=!1;function Dh(t,e){switch(t){case"compositionend":return To(e);case"keypress":return e.which!==32?null:(xo=!0,So);case"textInput":return t=e.data,t===So&&xo?null:t;default:return null}}function Uh(t,e){if(ra)return t==="compositionend"||!ji&&Ao(t,e)?(t=ho(),In=Ni=ul=null,ra=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return po&&e.locale!=="ko"?null:e.data;default:return null}}var wh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Eo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!wh[t.type]:e==="textarea"}function zo(t,e,l,a){ia?ca?ca.push(a):ca=[a]:ia=a,e=Qu(e,"onChange"),0<e.length&&(l=new lu("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Fa=null,Pa=null;function Ch(t){id(t,0)}function nu(t){var e=Za(t);if(uo(e))return t}function _o(t,e){if(t==="change")return e}var Mo=!1;if(qe){var Hi;if(qe){var Bi="oninput"in document;if(!Bi){var Ro=document.createElement("div");Ro.setAttribute("oninput","return;"),Bi=typeof Ro.oninput=="function"}Hi=Bi}else Hi=!1;Mo=Hi&&(!document.documentMode||9<document.documentMode)}function Oo(){Fa&&(Fa.detachEvent("onpropertychange",No),Pa=Fa=null)}function No(t){if(t.propertyName==="value"&&nu(Pa)){var e=[];zo(e,Pa,t,Mi(t)),mo(Ch,e)}}function jh(t,e,l){t==="focusin"?(Oo(),Fa=e,Pa=l,Fa.attachEvent("onpropertychange",No)):t==="focusout"&&Oo()}function Hh(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return nu(Pa)}function Bh(t,e){if(t==="click")return nu(e)}function qh(t,e){if(t==="input"||t==="change")return nu(e)}function Yh(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var re=typeof Object.is=="function"?Object.is:Yh;function Ia(t,e){if(re(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!ll.call(e,n)||!re(t[n],e[n]))return!1}return!0}function Do(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Uo(t,e){var l=Do(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Do(l)}}function wo(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?wo(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Co(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Fn(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Fn(t.document)}return e}function qi(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Gh=qe&&"documentMode"in document&&11>=document.documentMode,oa=null,Yi=null,tn=null,Gi=!1;function jo(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Gi||oa==null||oa!==Fn(a)||(a=oa,"selectionStart"in a&&qi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),tn&&Ia(tn,a)||(tn=a,a=Qu(Yi,"onSelect"),0<a.length&&(e=new lu("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=oa)))}function Dl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var fa={animationend:Dl("Animation","AnimationEnd"),animationiteration:Dl("Animation","AnimationIteration"),animationstart:Dl("Animation","AnimationStart"),transitionrun:Dl("Transition","TransitionRun"),transitionstart:Dl("Transition","TransitionStart"),transitioncancel:Dl("Transition","TransitionCancel"),transitionend:Dl("Transition","TransitionEnd")},Xi={},Ho={};qe&&(Ho=document.createElement("div").style,"AnimationEvent"in window||(delete fa.animationend.animation,delete fa.animationiteration.animation,delete fa.animationstart.animation),"TransitionEvent"in window||delete fa.transitionend.transition);function Ul(t){if(Xi[t])return Xi[t];if(!fa[t])return t;var e=fa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Ho)return Xi[t]=e[l];return t}var Bo=Ul("animationend"),qo=Ul("animationiteration"),Yo=Ul("animationstart"),Xh=Ul("transitionrun"),Lh=Ul("transitionstart"),Qh=Ul("transitioncancel"),Go=Ul("transitionend"),Xo=new Map,Li="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Li.push("scrollEnd");function Me(t,e){Xo.set(t,e),Ol(e,[t])}var Lo=new WeakMap;function be(t,e){if(typeof t=="object"&&t!==null){var l=Lo.get(t);return l!==void 0?l:(e={value:t,source:e,stack:ao(e)},Lo.set(t,e),e)}return{value:t,source:e,stack:ao(e)}}var pe=[],sa=0,Qi=0;function uu(){for(var t=sa,e=Qi=sa=0;e<t;){var l=pe[e];pe[e++]=null;var a=pe[e];pe[e++]=null;var n=pe[e];pe[e++]=null;var u=pe[e];if(pe[e++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&Qo(l,n,u)}}function iu(t,e,l,a){pe[sa++]=t,pe[sa++]=e,pe[sa++]=l,pe[sa++]=a,Qi|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Vi(t,e,l,a){return iu(t,e,l,a),cu(t)}function da(t,e){return iu(t,null,null,e),cu(t)}function Qo(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-ce(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function cu(t){if(50<_n)throw _n=0,$c=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ma={};function Vh(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function oe(t,e,l,a){return new Vh(t,e,l,a)}function Zi(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ye(t,e){var l=t.alternate;return l===null?(l=oe(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Vo(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ru(t,e,l,a,n,u){var i=0;if(a=t,typeof t=="function")Zi(t)&&(i=1);else if(typeof t=="string")i=kv(t,l,Q.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case L:return t=oe(31,l,e,n),t.elementType=L,t.lanes=u,t;case W:return wl(l.children,n,u,e);case et:i=8,n|=24;break;case $:return t=oe(12,l,e,n|2),t.elementType=$,t.lanes=u,t;case lt:return t=oe(13,l,e,n),t.elementType=lt,t.lanes=u,t;case St:return t=oe(19,l,e,n),t.elementType=St,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case bt:case pt:i=10;break t;case jt:i=9;break t;case Ot:i=11;break t;case X:i=14;break t;case q:i=16,a=null;break t}i=29,l=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=oe(i,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function wl(t,e,l,a){return t=oe(7,t,a,e),t.lanes=l,t}function ki(t,e,l){return t=oe(6,t,null,e),t.lanes=l,t}function Ki(t,e,l){return e=oe(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ha=[],va=0,ou=null,fu=0,Se=[],xe=0,Cl=null,Ge=1,Xe="";function jl(t,e){ha[va++]=fu,ha[va++]=ou,ou=t,fu=e}function Zo(t,e,l){Se[xe++]=Ge,Se[xe++]=Xe,Se[xe++]=Cl,Cl=t;var a=Ge;t=Xe;var n=32-ce(a)-1;a&=~(1<<n),l+=1;var u=32-ce(e)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Ge=1<<32-ce(e)+n|l<<n|a,Xe=u+t}else Ge=1<<u|l<<n|a,Xe=t}function Ji(t){t.return!==null&&(jl(t,1),Zo(t,1,0))}function Wi(t){for(;t===ou;)ou=ha[--va],ha[va]=null,fu=ha[--va],ha[va]=null;for(;t===Cl;)Cl=Se[--xe],Se[xe]=null,Xe=Se[--xe],Se[xe]=null,Ge=Se[--xe],Se[xe]=null}var It=null,wt=null,yt=!1,Hl=null,De=!1,$i=Error(o(519));function Bl(t){var e=Error(o(418,""));throw an(be(e,t)),$i}function ko(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[$t]=t,e[te]=a,l){case"dialog":st("cancel",e),st("close",e);break;case"iframe":case"object":case"embed":st("load",e);break;case"video":case"audio":for(l=0;l<Rn.length;l++)st(Rn[l],e);break;case"source":st("error",e);break;case"img":case"image":case"link":st("error",e),st("load",e);break;case"details":st("toggle",e);break;case"input":st("invalid",e),io(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),$n(e);break;case"select":st("invalid",e);break;case"textarea":st("invalid",e),ro(e,a.value,a.defaultValue,a.children),$n(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||fd(e.textContent,l)?(a.popover!=null&&(st("beforetoggle",e),st("toggle",e)),a.onScroll!=null&&st("scroll",e),a.onScrollEnd!=null&&st("scrollend",e),a.onClick!=null&&(e.onclick=Vu),e=!0):e=!1,e||Bl(t)}function Ko(t){for(It=t.return;It;)switch(It.tag){case 5:case 13:De=!1;return;case 27:case 3:De=!0;return;default:It=It.return}}function en(t){if(t!==It)return!1;if(!yt)return Ko(t),yt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||dr(t.type,t.memoizedProps)),l=!l),l&&wt&&Bl(t),Ko(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){wt=Oe(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}wt=null}}else e===27?(e=wt,xl(t.type)?(t=yr,yr=null,wt=t):wt=e):wt=It?Oe(t.stateNode.nextSibling):null;return!0}function ln(){wt=It=null,yt=!1}function Jo(){var t=Hl;return t!==null&&(ne===null?ne=t:ne.push.apply(ne,t),Hl=null),t}function an(t){Hl===null?Hl=[t]:Hl.push(t)}var Fi=U(null),ql=null,Le=null;function il(t,e,l){B(Fi,e._currentValue),e._currentValue=l}function Qe(t){t._currentValue=Fi.current,G(Fi)}function Pi(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Ii(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var r=u;u=n;for(var d=0;d<e.length;d++)if(r.context===e[d]){u.lanes|=l,r=u.alternate,r!==null&&(r.lanes|=l),Pi(u.return,l,t),a||(i=null);break t}u=r.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(o(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),Pi(i,l,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function nn(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(o(387));if(i=i.memoizedProps,i!==null){var r=n.type;re(n.pendingProps.value,i.value)||(t!==null?t.push(r):t=[r])}}else if(n===ht.current){if(i=n.alternate,i===null)throw Error(o(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Cn):t=[Cn])}n=n.return}t!==null&&Ii(e,t,l,a),e.flags|=262144}function su(t){for(t=t.firstContext;t!==null;){if(!re(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Yl(t){ql=t,Le=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Ft(t){return Wo(ql,t)}function du(t,e){return ql===null&&Yl(t),Wo(t,e)}function Wo(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Le===null){if(t===null)throw Error(o(308));Le=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Le=Le.next=e;return l}var Zh=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},kh=c.unstable_scheduleCallback,Kh=c.unstable_NormalPriority,Gt={$$typeof:pt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function tc(){return{controller:new Zh,data:new Map,refCount:0}}function un(t){t.refCount--,t.refCount===0&&kh(Kh,function(){t.controller.abort()})}var cn=null,ec=0,ya=0,ga=null;function Jh(t,e){if(cn===null){var l=cn=[];ec=0,ya=ar(),ga={status:"pending",value:void 0,then:function(a){l.push(a)}}}return ec++,e.then($o,$o),e}function $o(){if(--ec===0&&cn!==null){ga!==null&&(ga.status="fulfilled");var t=cn;cn=null,ya=0,ga=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Wh(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Fo=_.S;_.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Jh(t,e),Fo!==null&&Fo(t,e)};var Gl=U(null);function lc(){var t=Gl.current;return t!==null?t:Rt.pooledCache}function mu(t,e){e===null?B(Gl,Gl.current):B(Gl,e.pool)}function Po(){var t=lc();return t===null?null:{parent:Gt._currentValue,pool:t}}var rn=Error(o(460)),Io=Error(o(474)),hu=Error(o(542)),ac={then:function(){}};function tf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function vu(){}function ef(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(vu,vu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,af(t),t;default:if(typeof e.status=="string")e.then(vu,vu);else{if(t=Rt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,af(t),t}throw on=e,rn}}var on=null;function lf(){if(on===null)throw Error(o(459));var t=on;return on=null,t}function af(t){if(t===rn||t===hu)throw Error(o(483))}var cl=!1;function nc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function uc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function rl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ol(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(xt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=cu(t),Qo(t,null,l),e}return iu(t,a,e,l),cu(t)}function fn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Wr(t,l)}}function ic(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var cc=!1;function sn(){if(cc){var t=ga;if(t!==null)throw t}}function dn(t,e,l,a){cc=!1;var n=t.updateQueue;cl=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,r=n.shared.pending;if(r!==null){n.shared.pending=null;var d=r,x=d.next;d.next=null,i===null?u=x:i.next=x,i=d;var R=t.alternate;R!==null&&(R=R.updateQueue,r=R.lastBaseUpdate,r!==i&&(r===null?R.firstBaseUpdate=x:r.next=x,R.lastBaseUpdate=d))}if(u!==null){var D=n.baseState;i=0,R=x=d=null,r=u;do{var A=r.lane&-536870913,T=A!==r.lane;if(T?(dt&A)===A:(a&A)===A){A!==0&&A===ya&&(cc=!0),R!==null&&(R=R.next={lane:0,tag:r.tag,payload:r.payload,callback:null,next:null});t:{var I=t,F=r;A=e;var zt=l;switch(F.tag){case 1:if(I=F.payload,typeof I=="function"){D=I.call(zt,D,A);break t}D=I;break t;case 3:I.flags=I.flags&-65537|128;case 0:if(I=F.payload,A=typeof I=="function"?I.call(zt,D,A):I,A==null)break t;D=z({},D,A);break t;case 2:cl=!0}}A=r.callback,A!==null&&(t.flags|=64,T&&(t.flags|=8192),T=n.callbacks,T===null?n.callbacks=[A]:T.push(A))}else T={lane:A,tag:r.tag,payload:r.payload,callback:r.callback,next:null},R===null?(x=R=T,d=D):R=R.next=T,i|=A;if(r=r.next,r===null){if(r=n.shared.pending,r===null)break;T=r,r=T.next,T.next=null,n.lastBaseUpdate=T,n.shared.pending=null}}while(!0);R===null&&(d=D),n.baseState=d,n.firstBaseUpdate=x,n.lastBaseUpdate=R,u===null&&(n.shared.lanes=0),gl|=i,t.lanes=i,t.memoizedState=D}}function nf(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function uf(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)nf(l[t],e)}var ba=U(null),yu=U(0);function cf(t,e){t=$e,B(yu,t),B(ba,e),$e=t|e.baseLanes}function rc(){B(yu,$e),B(ba,ba.current)}function oc(){$e=yu.current,G(ba),G(yu)}var fl=0,nt=null,Tt=null,qt=null,gu=!1,pa=!1,Xl=!1,bu=0,mn=0,Sa=null,$h=0;function Ht(){throw Error(o(321))}function fc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!re(t[l],e[l]))return!1;return!0}function sc(t,e,l,a,n,u){return fl=u,nt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,_.H=t===null||t.memoizedState===null?Vf:Zf,Xl=!1,u=l(a,n),Xl=!1,pa&&(u=of(e,l,a,n)),rf(t),u}function rf(t){_.H=Eu;var e=Tt!==null&&Tt.next!==null;if(fl=0,qt=Tt=nt=null,gu=!1,mn=0,Sa=null,e)throw Error(o(300));t===null||Vt||(t=t.dependencies,t!==null&&su(t)&&(Vt=!0))}function of(t,e,l,a){nt=t;var n=0;do{if(pa&&(Sa=null),mn=0,pa=!1,25<=n)throw Error(o(301));if(n+=1,qt=Tt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}_.H=av,u=e(l,a)}while(pa);return u}function Fh(){var t=_.H,e=t.useState()[0];return e=typeof e.then=="function"?hn(e):e,t=t.useState()[0],(Tt!==null?Tt.memoizedState:null)!==t&&(nt.flags|=1024),e}function dc(){var t=bu!==0;return bu=0,t}function mc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function hc(t){if(gu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}gu=!1}fl=0,qt=Tt=nt=null,pa=!1,mn=bu=0,Sa=null}function le(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return qt===null?nt.memoizedState=qt=t:qt=qt.next=t,qt}function Yt(){if(Tt===null){var t=nt.alternate;t=t!==null?t.memoizedState:null}else t=Tt.next;var e=qt===null?nt.memoizedState:qt.next;if(e!==null)qt=e,Tt=t;else{if(t===null)throw nt.alternate===null?Error(o(467)):Error(o(310));Tt=t,t={memoizedState:Tt.memoizedState,baseState:Tt.baseState,baseQueue:Tt.baseQueue,queue:Tt.queue,next:null},qt===null?nt.memoizedState=qt=t:qt=qt.next=t}return qt}function vc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function hn(t){var e=mn;return mn+=1,Sa===null&&(Sa=[]),t=ef(Sa,t,e),e=nt,(qt===null?e.memoizedState:qt.next)===null&&(e=e.alternate,_.H=e===null||e.memoizedState===null?Vf:Zf),t}function pu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return hn(t);if(t.$$typeof===pt)return Ft(t)}throw Error(o(438,String(t)))}function yc(t){var e=null,l=nt.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=nt.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=vc(),nt.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=ct;return e.index++,l}function Ve(t,e){return typeof e=="function"?e(t):e}function Su(t){var e=Yt();return gc(e,Tt,t)}function gc(t,e,l){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var r=i=null,d=null,x=e,R=!1;do{var D=x.lane&-536870913;if(D!==x.lane?(dt&D)===D:(fl&D)===D){var A=x.revertLane;if(A===0)d!==null&&(d=d.next={lane:0,revertLane:0,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null}),D===ya&&(R=!0);else if((fl&A)===A){x=x.next,A===ya&&(R=!0);continue}else D={lane:0,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},d===null?(r=d=D,i=u):d=d.next=D,nt.lanes|=A,gl|=A;D=x.action,Xl&&l(u,D),u=x.hasEagerState?x.eagerState:l(u,D)}else A={lane:D,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},d===null?(r=d=A,i=u):d=d.next=A,nt.lanes|=D,gl|=D;x=x.next}while(x!==null&&x!==e);if(d===null?i=u:d.next=r,!re(u,t.memoizedState)&&(Vt=!0,R&&(l=ga,l!==null)))throw l;t.memoizedState=u,t.baseState=i,t.baseQueue=d,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function bc(t){var e=Yt(),l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);re(u,e.memoizedState)||(Vt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function ff(t,e,l){var a=nt,n=Yt(),u=yt;if(u){if(l===void 0)throw Error(o(407));l=l()}else l=e();var i=!re((Tt||n).memoizedState,l);i&&(n.memoizedState=l,Vt=!0),n=n.queue;var r=mf.bind(null,a,n,t);if(vn(2048,8,r,[t]),n.getSnapshot!==e||i||qt!==null&&qt.memoizedState.tag&1){if(a.flags|=2048,xa(9,xu(),df.bind(null,a,n,l,e),null),Rt===null)throw Error(o(349));u||(fl&124)!==0||sf(a,e,l)}return l}function sf(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=nt.updateQueue,e===null?(e=vc(),nt.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function df(t,e,l,a){e.value=l,e.getSnapshot=a,hf(e)&&vf(t)}function mf(t,e,l){return l(function(){hf(e)&&vf(t)})}function hf(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!re(t,l)}catch{return!0}}function vf(t){var e=da(t,2);e!==null&&he(e,t,2)}function pc(t){var e=le();if(typeof t=="function"){var l=t;if(t=l(),Xl){al(!0);try{l()}finally{al(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ve,lastRenderedState:t},e}function yf(t,e,l,a){return t.baseState=l,gc(t,Tt,typeof a=="function"?a:Ve)}function Ph(t,e,l,a,n){if(Tu(t))throw Error(o(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};_.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,gf(e,u)):(u.next=l.next,e.pending=l.next=u)}}function gf(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=_.T,i={};_.T=i;try{var r=l(n,a),d=_.S;d!==null&&d(i,r),bf(t,e,r)}catch(x){Sc(t,e,x)}finally{_.T=u}}else try{u=l(n,a),bf(t,e,u)}catch(x){Sc(t,e,x)}}function bf(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){pf(t,e,a)},function(a){return Sc(t,e,a)}):pf(t,e,l)}function pf(t,e,l){e.status="fulfilled",e.value=l,Sf(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,gf(t,l)))}function Sc(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,Sf(e),e=e.next;while(e!==a)}t.action=null}function Sf(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function xf(t,e){return e}function Af(t,e){if(yt){var l=Rt.formState;if(l!==null){t:{var a=nt;if(yt){if(wt){e:{for(var n=wt,u=De;n.nodeType!==8;){if(!u){n=null;break e}if(n=Oe(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){wt=Oe(n.nextSibling),a=n.data==="F!";break t}}Bl(a)}a=!1}a&&(e=l[0])}}return l=le(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xf,lastRenderedState:e},l.queue=a,l=Xf.bind(null,nt,a),a.dispatch=l,a=pc(!1),u=zc.bind(null,nt,!1,a.queue),a=le(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=Ph.bind(null,nt,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Tf(t){var e=Yt();return Ef(e,Tt,t)}function Ef(t,e,l){if(e=gc(t,e,xf)[0],t=Su(Ve)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=hn(e)}catch(i){throw i===rn?hu:i}else a=e;e=Yt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(nt.flags|=2048,xa(9,xu(),Ih.bind(null,n,l),null)),[a,u,t]}function Ih(t,e){t.action=e}function zf(t){var e=Yt(),l=Tt;if(l!==null)return Ef(e,l,t);Yt(),e=e.memoizedState,l=Yt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function xa(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=nt.updateQueue,e===null&&(e=vc(),nt.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function xu(){return{destroy:void 0,resource:void 0}}function _f(){return Yt().memoizedState}function Au(t,e,l,a){var n=le();a=a===void 0?null:a,nt.flags|=t,n.memoizedState=xa(1|e,xu(),l,a)}function vn(t,e,l,a){var n=Yt();a=a===void 0?null:a;var u=n.memoizedState.inst;Tt!==null&&a!==null&&fc(a,Tt.memoizedState.deps)?n.memoizedState=xa(e,u,l,a):(nt.flags|=t,n.memoizedState=xa(1|e,u,l,a))}function Mf(t,e){Au(8390656,8,t,e)}function Rf(t,e){vn(2048,8,t,e)}function Of(t,e){return vn(4,2,t,e)}function Nf(t,e){return vn(4,4,t,e)}function Df(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Uf(t,e,l){l=l!=null?l.concat([t]):null,vn(4,4,Df.bind(null,e,t),l)}function xc(){}function wf(t,e){var l=Yt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&fc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Cf(t,e){var l=Yt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&fc(e,a[1]))return a[0];if(a=t(),Xl){al(!0);try{t()}finally{al(!1)}}return l.memoizedState=[a,e],a}function Ac(t,e,l){return l===void 0||(fl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Bs(),nt.lanes|=t,gl|=t,l)}function jf(t,e,l,a){return re(l,e)?l:ba.current!==null?(t=Ac(t,l,a),re(t,e)||(Vt=!0),t):(fl&42)===0?(Vt=!0,t.memoizedState=l):(t=Bs(),nt.lanes|=t,gl|=t,e)}function Hf(t,e,l,a,n){var u=Y.p;Y.p=u!==0&&8>u?u:8;var i=_.T,r={};_.T=r,zc(t,!1,e,l);try{var d=n(),x=_.S;if(x!==null&&x(r,d),d!==null&&typeof d=="object"&&typeof d.then=="function"){var R=Wh(d,a);yn(t,e,R,me(t))}else yn(t,e,a,me(t))}catch(D){yn(t,e,{then:function(){},status:"rejected",reason:D},me())}finally{Y.p=u,_.T=i}}function tv(){}function Tc(t,e,l,a){if(t.tag!==5)throw Error(o(476));var n=Bf(t).queue;Hf(t,n,e,j,l===null?tv:function(){return qf(t),l(a)})}function Bf(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:j,baseState:j,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ve,lastRenderedState:j},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ve,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function qf(t){var e=Bf(t).next.queue;yn(t,e,{},me())}function Ec(){return Ft(Cn)}function Yf(){return Yt().memoizedState}function Gf(){return Yt().memoizedState}function ev(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=me();t=rl(l);var a=ol(e,t,l);a!==null&&(he(a,e,l),fn(a,e,l)),e={cache:tc()},t.payload=e;return}e=e.return}}function lv(t,e,l){var a=me();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Tu(t)?Lf(e,l):(l=Vi(t,e,l,a),l!==null&&(he(l,t,a),Qf(l,e,a)))}function Xf(t,e,l){var a=me();yn(t,e,l,a)}function yn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Tu(t))Lf(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var i=e.lastRenderedState,r=u(i,l);if(n.hasEagerState=!0,n.eagerState=r,re(r,i))return iu(t,e,n,0),Rt===null&&uu(),!1}catch{}finally{}if(l=Vi(t,e,n,a),l!==null)return he(l,t,a),Qf(l,e,a),!0}return!1}function zc(t,e,l,a){if(a={lane:2,revertLane:ar(),action:a,hasEagerState:!1,eagerState:null,next:null},Tu(t)){if(e)throw Error(o(479))}else e=Vi(t,l,a,2),e!==null&&he(e,t,2)}function Tu(t){var e=t.alternate;return t===nt||e!==null&&e===nt}function Lf(t,e){pa=gu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Qf(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Wr(t,l)}}var Eu={readContext:Ft,use:pu,useCallback:Ht,useContext:Ht,useEffect:Ht,useImperativeHandle:Ht,useLayoutEffect:Ht,useInsertionEffect:Ht,useMemo:Ht,useReducer:Ht,useRef:Ht,useState:Ht,useDebugValue:Ht,useDeferredValue:Ht,useTransition:Ht,useSyncExternalStore:Ht,useId:Ht,useHostTransitionStatus:Ht,useFormState:Ht,useActionState:Ht,useOptimistic:Ht,useMemoCache:Ht,useCacheRefresh:Ht},Vf={readContext:Ft,use:pu,useCallback:function(t,e){return le().memoizedState=[t,e===void 0?null:e],t},useContext:Ft,useEffect:Mf,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Au(4194308,4,Df.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Au(4194308,4,t,e)},useInsertionEffect:function(t,e){Au(4,2,t,e)},useMemo:function(t,e){var l=le();e=e===void 0?null:e;var a=t();if(Xl){al(!0);try{t()}finally{al(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=le();if(l!==void 0){var n=l(e);if(Xl){al(!0);try{l(e)}finally{al(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=lv.bind(null,nt,t),[a.memoizedState,t]},useRef:function(t){var e=le();return t={current:t},e.memoizedState=t},useState:function(t){t=pc(t);var e=t.queue,l=Xf.bind(null,nt,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:xc,useDeferredValue:function(t,e){var l=le();return Ac(l,t,e)},useTransition:function(){var t=pc(!1);return t=Hf.bind(null,nt,t.queue,!0,!1),le().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=nt,n=le();if(yt){if(l===void 0)throw Error(o(407));l=l()}else{if(l=e(),Rt===null)throw Error(o(349));(dt&124)!==0||sf(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,Mf(mf.bind(null,a,u,t),[t]),a.flags|=2048,xa(9,xu(),df.bind(null,a,u,l,e),null),l},useId:function(){var t=le(),e=Rt.identifierPrefix;if(yt){var l=Xe,a=Ge;l=(a&~(1<<32-ce(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=bu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=$h++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Ec,useFormState:Af,useActionState:Af,useOptimistic:function(t){var e=le();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=zc.bind(null,nt,!0,l),l.dispatch=e,[t,e]},useMemoCache:yc,useCacheRefresh:function(){return le().memoizedState=ev.bind(null,nt)}},Zf={readContext:Ft,use:pu,useCallback:wf,useContext:Ft,useEffect:Rf,useImperativeHandle:Uf,useInsertionEffect:Of,useLayoutEffect:Nf,useMemo:Cf,useReducer:Su,useRef:_f,useState:function(){return Su(Ve)},useDebugValue:xc,useDeferredValue:function(t,e){var l=Yt();return jf(l,Tt.memoizedState,t,e)},useTransition:function(){var t=Su(Ve)[0],e=Yt().memoizedState;return[typeof t=="boolean"?t:hn(t),e]},useSyncExternalStore:ff,useId:Yf,useHostTransitionStatus:Ec,useFormState:Tf,useActionState:Tf,useOptimistic:function(t,e){var l=Yt();return yf(l,Tt,t,e)},useMemoCache:yc,useCacheRefresh:Gf},av={readContext:Ft,use:pu,useCallback:wf,useContext:Ft,useEffect:Rf,useImperativeHandle:Uf,useInsertionEffect:Of,useLayoutEffect:Nf,useMemo:Cf,useReducer:bc,useRef:_f,useState:function(){return bc(Ve)},useDebugValue:xc,useDeferredValue:function(t,e){var l=Yt();return Tt===null?Ac(l,t,e):jf(l,Tt.memoizedState,t,e)},useTransition:function(){var t=bc(Ve)[0],e=Yt().memoizedState;return[typeof t=="boolean"?t:hn(t),e]},useSyncExternalStore:ff,useId:Yf,useHostTransitionStatus:Ec,useFormState:zf,useActionState:zf,useOptimistic:function(t,e){var l=Yt();return Tt!==null?yf(l,Tt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:yc,useCacheRefresh:Gf},Aa=null,gn=0;function zu(t){var e=gn;return gn+=1,Aa===null&&(Aa=[]),ef(Aa,t,e)}function bn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function _u(t,e){throw e.$$typeof===C?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function kf(t){var e=t._init;return e(t._payload)}function Kf(t){function e(g,v){if(t){var S=g.deletions;S===null?(g.deletions=[v],g.flags|=16):S.push(v)}}function l(g,v){if(!t)return null;for(;v!==null;)e(g,v),v=v.sibling;return null}function a(g){for(var v=new Map;g!==null;)g.key!==null?v.set(g.key,g):v.set(g.index,g),g=g.sibling;return v}function n(g,v){return g=Ye(g,v),g.index=0,g.sibling=null,g}function u(g,v,S){return g.index=S,t?(S=g.alternate,S!==null?(S=S.index,S<v?(g.flags|=67108866,v):S):(g.flags|=67108866,v)):(g.flags|=1048576,v)}function i(g){return t&&g.alternate===null&&(g.flags|=67108866),g}function r(g,v,S,N){return v===null||v.tag!==6?(v=ki(S,g.mode,N),v.return=g,v):(v=n(v,S),v.return=g,v)}function d(g,v,S,N){var V=S.type;return V===W?R(g,v,S.props.children,N,S.key):v!==null&&(v.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===q&&kf(V)===v.type)?(v=n(v,S.props),bn(v,S),v.return=g,v):(v=ru(S.type,S.key,S.props,null,g.mode,N),bn(v,S),v.return=g,v)}function x(g,v,S,N){return v===null||v.tag!==4||v.stateNode.containerInfo!==S.containerInfo||v.stateNode.implementation!==S.implementation?(v=Ki(S,g.mode,N),v.return=g,v):(v=n(v,S.children||[]),v.return=g,v)}function R(g,v,S,N,V){return v===null||v.tag!==7?(v=wl(S,g.mode,N,V),v.return=g,v):(v=n(v,S),v.return=g,v)}function D(g,v,S){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=ki(""+v,g.mode,S),v.return=g,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case H:return S=ru(v.type,v.key,v.props,null,g.mode,S),bn(S,v),S.return=g,S;case K:return v=Ki(v,g.mode,S),v.return=g,v;case q:var N=v._init;return v=N(v._payload),D(g,v,S)}if(Dt(v)||rt(v))return v=wl(v,g.mode,S,null),v.return=g,v;if(typeof v.then=="function")return D(g,zu(v),S);if(v.$$typeof===pt)return D(g,du(g,v),S);_u(g,v)}return null}function A(g,v,S,N){var V=v!==null?v.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return V!==null?null:r(g,v,""+S,N);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case H:return S.key===V?d(g,v,S,N):null;case K:return S.key===V?x(g,v,S,N):null;case q:return V=S._init,S=V(S._payload),A(g,v,S,N)}if(Dt(S)||rt(S))return V!==null?null:R(g,v,S,N,null);if(typeof S.then=="function")return A(g,v,zu(S),N);if(S.$$typeof===pt)return A(g,v,du(g,S),N);_u(g,S)}return null}function T(g,v,S,N,V){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return g=g.get(S)||null,r(v,g,""+N,V);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case H:return g=g.get(N.key===null?S:N.key)||null,d(v,g,N,V);case K:return g=g.get(N.key===null?S:N.key)||null,x(v,g,N,V);case q:var it=N._init;return N=it(N._payload),T(g,v,S,N,V)}if(Dt(N)||rt(N))return g=g.get(S)||null,R(v,g,N,V,null);if(typeof N.then=="function")return T(g,v,S,zu(N),V);if(N.$$typeof===pt)return T(g,v,S,du(v,N),V);_u(v,N)}return null}function I(g,v,S,N){for(var V=null,it=null,J=v,P=v=0,kt=null;J!==null&&P<S.length;P++){J.index>P?(kt=J,J=null):kt=J.sibling;var vt=A(g,J,S[P],N);if(vt===null){J===null&&(J=kt);break}t&&J&&vt.alternate===null&&e(g,J),v=u(vt,v,P),it===null?V=vt:it.sibling=vt,it=vt,J=kt}if(P===S.length)return l(g,J),yt&&jl(g,P),V;if(J===null){for(;P<S.length;P++)J=D(g,S[P],N),J!==null&&(v=u(J,v,P),it===null?V=J:it.sibling=J,it=J);return yt&&jl(g,P),V}for(J=a(J);P<S.length;P++)kt=T(J,g,P,S[P],N),kt!==null&&(t&&kt.alternate!==null&&J.delete(kt.key===null?P:kt.key),v=u(kt,v,P),it===null?V=kt:it.sibling=kt,it=kt);return t&&J.forEach(function(_l){return e(g,_l)}),yt&&jl(g,P),V}function F(g,v,S,N){if(S==null)throw Error(o(151));for(var V=null,it=null,J=v,P=v=0,kt=null,vt=S.next();J!==null&&!vt.done;P++,vt=S.next()){J.index>P?(kt=J,J=null):kt=J.sibling;var _l=A(g,J,vt.value,N);if(_l===null){J===null&&(J=kt);break}t&&J&&_l.alternate===null&&e(g,J),v=u(_l,v,P),it===null?V=_l:it.sibling=_l,it=_l,J=kt}if(vt.done)return l(g,J),yt&&jl(g,P),V;if(J===null){for(;!vt.done;P++,vt=S.next())vt=D(g,vt.value,N),vt!==null&&(v=u(vt,v,P),it===null?V=vt:it.sibling=vt,it=vt);return yt&&jl(g,P),V}for(J=a(J);!vt.done;P++,vt=S.next())vt=T(J,g,P,vt.value,N),vt!==null&&(t&&vt.alternate!==null&&J.delete(vt.key===null?P:vt.key),v=u(vt,v,P),it===null?V=vt:it.sibling=vt,it=vt);return t&&J.forEach(function(n0){return e(g,n0)}),yt&&jl(g,P),V}function zt(g,v,S,N){if(typeof S=="object"&&S!==null&&S.type===W&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case H:t:{for(var V=S.key;v!==null;){if(v.key===V){if(V=S.type,V===W){if(v.tag===7){l(g,v.sibling),N=n(v,S.props.children),N.return=g,g=N;break t}}else if(v.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===q&&kf(V)===v.type){l(g,v.sibling),N=n(v,S.props),bn(N,S),N.return=g,g=N;break t}l(g,v);break}else e(g,v);v=v.sibling}S.type===W?(N=wl(S.props.children,g.mode,N,S.key),N.return=g,g=N):(N=ru(S.type,S.key,S.props,null,g.mode,N),bn(N,S),N.return=g,g=N)}return i(g);case K:t:{for(V=S.key;v!==null;){if(v.key===V)if(v.tag===4&&v.stateNode.containerInfo===S.containerInfo&&v.stateNode.implementation===S.implementation){l(g,v.sibling),N=n(v,S.children||[]),N.return=g,g=N;break t}else{l(g,v);break}else e(g,v);v=v.sibling}N=Ki(S,g.mode,N),N.return=g,g=N}return i(g);case q:return V=S._init,S=V(S._payload),zt(g,v,S,N)}if(Dt(S))return I(g,v,S,N);if(rt(S)){if(V=rt(S),typeof V!="function")throw Error(o(150));return S=V.call(S),F(g,v,S,N)}if(typeof S.then=="function")return zt(g,v,zu(S),N);if(S.$$typeof===pt)return zt(g,v,du(g,S),N);_u(g,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,v!==null&&v.tag===6?(l(g,v.sibling),N=n(v,S),N.return=g,g=N):(l(g,v),N=ki(S,g.mode,N),N.return=g,g=N),i(g)):l(g,v)}return function(g,v,S,N){try{gn=0;var V=zt(g,v,S,N);return Aa=null,V}catch(J){if(J===rn||J===hu)throw J;var it=oe(29,J,null,g.mode);return it.lanes=N,it.return=g,it}finally{}}}var Ta=Kf(!0),Jf=Kf(!1),Ae=U(null),Ue=null;function sl(t){var e=t.alternate;B(Xt,Xt.current&1),B(Ae,t),Ue===null&&(e===null||ba.current!==null||e.memoizedState!==null)&&(Ue=t)}function Wf(t){if(t.tag===22){if(B(Xt,Xt.current),B(Ae,t),Ue===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ue=t)}}else dl()}function dl(){B(Xt,Xt.current),B(Ae,Ae.current)}function Ze(t){G(Ae),Ue===t&&(Ue=null),G(Xt)}var Xt=U(0);function Mu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||vr(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function _c(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Mc={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=me(),n=rl(a);n.payload=e,l!=null&&(n.callback=l),e=ol(t,n,a),e!==null&&(he(e,t,a),fn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=me(),n=rl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=ol(t,n,a),e!==null&&(he(e,t,a),fn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=me(),a=rl(l);a.tag=2,e!=null&&(a.callback=e),e=ol(t,a,l),e!==null&&(he(e,t,l),fn(e,t,l))}};function $f(t,e,l,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):e.prototype&&e.prototype.isPureReactComponent?!Ia(l,a)||!Ia(n,u):!0}function Ff(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Mc.enqueueReplaceState(e,e.state,null)}function Ll(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Ru=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Pf(t){Ru(t)}function If(t){console.error(t)}function ts(t){Ru(t)}function Ou(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function es(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Rc(t,e,l){return l=rl(l),l.tag=3,l.payload={element:null},l.callback=function(){Ou(t,e)},l}function ls(t){return t=rl(t),t.tag=3,t}function as(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){es(e,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){es(e,l,a),typeof n!="function"&&(bl===null?bl=new Set([this]):bl.add(this));var r=a.stack;this.componentDidCatch(a.value,{componentStack:r!==null?r:""})})}function nv(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&nn(e,l,n,!0),l=Ae.current,l!==null){switch(l.tag){case 13:return Ue===null?Pc():l.alternate===null&&Ct===0&&(Ct=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===ac?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),tr(t,a,n)),!1;case 22:return l.flags|=65536,a===ac?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),tr(t,a,n)),!1}throw Error(o(435,l.tag))}return tr(t,a,n),Pc(),!1}if(yt)return e=Ae.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==$i&&(t=Error(o(422),{cause:a}),an(be(t,l)))):(a!==$i&&(e=Error(o(423),{cause:a}),an(be(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=be(a,l),n=Rc(t.stateNode,a,n),ic(t,n),Ct!==4&&(Ct=2)),!1;var u=Error(o(520),{cause:a});if(u=be(u,l),zn===null?zn=[u]:zn.push(u),Ct!==4&&(Ct=2),e===null)return!0;a=be(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Rc(l.stateNode,a,t),ic(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(bl===null||!bl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=ls(n),as(n,t,l,a),ic(l,n),!1}l=l.return}while(l!==null);return!1}var ns=Error(o(461)),Vt=!1;function Kt(t,e,l,a){e.child=t===null?Jf(e,null,l,a):Ta(e,t.child,l,a)}function us(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var i={};for(var r in a)r!=="ref"&&(i[r]=a[r])}else i=a;return Yl(e),a=sc(t,e,l,i,u,n),r=dc(),t!==null&&!Vt?(mc(t,e,n),ke(t,e,n)):(yt&&r&&Ji(e),e.flags|=1,Kt(t,e,a,n),e.child)}function is(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!Zi(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,cs(t,e,u,a,n)):(t=ru(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Hc(t,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:Ia,l(i,a)&&t.ref===e.ref)return ke(t,e,n)}return e.flags|=1,t=Ye(u,a),t.ref=e.ref,t.return=e,e.child=t}function cs(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(Ia(u,a)&&t.ref===e.ref)if(Vt=!1,e.pendingProps=a=u,Hc(t,n))(t.flags&131072)!==0&&(Vt=!0);else return e.lanes=t.lanes,ke(t,e,n)}return Oc(t,e,l,a,n)}function rs(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return os(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&mu(e,u!==null?u.cachePool:null),u!==null?cf(e,u):rc(),Wf(e);else return e.lanes=e.childLanes=536870912,os(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(mu(e,u.cachePool),cf(e,u),dl(),e.memoizedState=null):(t!==null&&mu(e,null),rc(),dl());return Kt(t,e,n,l),e.child}function os(t,e,l,a){var n=lc();return n=n===null?null:{parent:Gt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&mu(e,null),rc(),Wf(e),t!==null&&nn(t,e,a,!0),null}function Nu(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(o(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function Oc(t,e,l,a,n){return Yl(e),l=sc(t,e,l,a,void 0,n),a=dc(),t!==null&&!Vt?(mc(t,e,n),ke(t,e,n)):(yt&&a&&Ji(e),e.flags|=1,Kt(t,e,l,n),e.child)}function fs(t,e,l,a,n,u){return Yl(e),e.updateQueue=null,l=of(e,a,l,n),rf(t),a=dc(),t!==null&&!Vt?(mc(t,e,u),ke(t,e,u)):(yt&&a&&Ji(e),e.flags|=1,Kt(t,e,l,u),e.child)}function ss(t,e,l,a,n){if(Yl(e),e.stateNode===null){var u=ma,i=l.contextType;typeof i=="object"&&i!==null&&(u=Ft(i)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Mc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},nc(e),i=l.contextType,u.context=typeof i=="object"&&i!==null?Ft(i):ma,u.state=e.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(_c(e,l,i,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&Mc.enqueueReplaceState(u,u.state,null),dn(e,a,u,n),sn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var r=e.memoizedProps,d=Ll(l,r);u.props=d;var x=u.context,R=l.contextType;i=ma,typeof R=="object"&&R!==null&&(i=Ft(R));var D=l.getDerivedStateFromProps;R=typeof D=="function"||typeof u.getSnapshotBeforeUpdate=="function",r=e.pendingProps!==r,R||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r||x!==i)&&Ff(e,u,a,i),cl=!1;var A=e.memoizedState;u.state=A,dn(e,a,u,n),sn(),x=e.memoizedState,r||A!==x||cl?(typeof D=="function"&&(_c(e,l,D,a),x=e.memoizedState),(d=cl||$f(e,l,d,a,A,x,i))?(R||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=x),u.props=a,u.state=x,u.context=i,a=d):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,uc(t,e),i=e.memoizedProps,R=Ll(l,i),u.props=R,D=e.pendingProps,A=u.context,x=l.contextType,d=ma,typeof x=="object"&&x!==null&&(d=Ft(x)),r=l.getDerivedStateFromProps,(x=typeof r=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==D||A!==d)&&Ff(e,u,a,d),cl=!1,A=e.memoizedState,u.state=A,dn(e,a,u,n),sn();var T=e.memoizedState;i!==D||A!==T||cl||t!==null&&t.dependencies!==null&&su(t.dependencies)?(typeof r=="function"&&(_c(e,l,r,a),T=e.memoizedState),(R=cl||$f(e,l,R,a,A,T,d)||t!==null&&t.dependencies!==null&&su(t.dependencies))?(x||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,T,d),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,T,d)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=T),u.props=a,u.state=T,u.context=d,a=R):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Nu(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Ta(e,t.child,null,n),e.child=Ta(e,null,l,n)):Kt(t,e,l,n),e.memoizedState=u.state,t=e.child):t=ke(t,e,n),t}function ds(t,e,l,a){return ln(),e.flags|=256,Kt(t,e,l,a),e.child}var Nc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Dc(t){return{baseLanes:t,cachePool:Po()}}function Uc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Te),t}function ms(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(Xt.current&2)!==0),i&&(n=!0,e.flags&=-129),i=(e.flags&32)!==0,e.flags&=-33,t===null){if(yt){if(n?sl(e):dl(),yt){var r=wt,d;if(d=r){t:{for(d=r,r=De;d.nodeType!==8;){if(!r){r=null;break t}if(d=Oe(d.nextSibling),d===null){r=null;break t}}r=d}r!==null?(e.memoizedState={dehydrated:r,treeContext:Cl!==null?{id:Ge,overflow:Xe}:null,retryLane:536870912,hydrationErrors:null},d=oe(18,null,null,0),d.stateNode=r,d.return=e,e.child=d,It=e,wt=null,d=!0):d=!1}d||Bl(e)}if(r=e.memoizedState,r!==null&&(r=r.dehydrated,r!==null))return vr(r)?e.lanes=32:e.lanes=536870912,null;Ze(e)}return r=a.children,a=a.fallback,n?(dl(),n=e.mode,r=Du({mode:"hidden",children:r},n),a=wl(a,n,l,null),r.return=e,a.return=e,r.sibling=a,e.child=r,n=e.child,n.memoizedState=Dc(l),n.childLanes=Uc(t,i,l),e.memoizedState=Nc,a):(sl(e),wc(e,r))}if(d=t.memoizedState,d!==null&&(r=d.dehydrated,r!==null)){if(u)e.flags&256?(sl(e),e.flags&=-257,e=Cc(t,e,l)):e.memoizedState!==null?(dl(),e.child=t.child,e.flags|=128,e=null):(dl(),n=a.fallback,r=e.mode,a=Du({mode:"visible",children:a.children},r),n=wl(n,r,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ta(e,t.child,null,l),a=e.child,a.memoizedState=Dc(l),a.childLanes=Uc(t,i,l),e.memoizedState=Nc,e=n);else if(sl(e),vr(r)){if(i=r.nextSibling&&r.nextSibling.dataset,i)var x=i.dgst;i=x,a=Error(o(419)),a.stack="",a.digest=i,an({value:a,source:null,stack:null}),e=Cc(t,e,l)}else if(Vt||nn(t,e,l,!1),i=(l&t.childLanes)!==0,Vt||i){if(i=Rt,i!==null&&(a=l&-l,a=(a&42)!==0?1:yi(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==d.retryLane))throw d.retryLane=a,da(t,a),he(i,t,a),ns;r.data==="$?"||Pc(),e=Cc(t,e,l)}else r.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=d.treeContext,wt=Oe(r.nextSibling),It=e,yt=!0,Hl=null,De=!1,t!==null&&(Se[xe++]=Ge,Se[xe++]=Xe,Se[xe++]=Cl,Ge=t.id,Xe=t.overflow,Cl=e),e=wc(e,a.children),e.flags|=4096);return e}return n?(dl(),n=a.fallback,r=e.mode,d=t.child,x=d.sibling,a=Ye(d,{mode:"hidden",children:a.children}),a.subtreeFlags=d.subtreeFlags&65011712,x!==null?n=Ye(x,n):(n=wl(n,r,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,r=t.child.memoizedState,r===null?r=Dc(l):(d=r.cachePool,d!==null?(x=Gt._currentValue,d=d.parent!==x?{parent:x,pool:x}:d):d=Po(),r={baseLanes:r.baseLanes|l,cachePool:d}),n.memoizedState=r,n.childLanes=Uc(t,i,l),e.memoizedState=Nc,a):(sl(e),l=t.child,t=l.sibling,l=Ye(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(i=e.deletions,i===null?(e.deletions=[t],e.flags|=16):i.push(t)),e.child=l,e.memoizedState=null,l)}function wc(t,e){return e=Du({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Du(t,e){return t=oe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Cc(t,e,l){return Ta(e,t.child,null,l),t=wc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function hs(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Pi(t.return,e,l)}function jc(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function vs(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(Kt(t,e,a.children,l),a=Xt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&hs(t,l,e);else if(t.tag===19)hs(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(B(Xt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Mu(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),jc(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Mu(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}jc(e,!0,l,null,u);break;case"together":jc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ke(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),gl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(nn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,l=Ye(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Ye(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Hc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&su(t)))}function uv(t,e,l){switch(e.tag){case 3:_t(e,e.stateNode.containerInfo),il(e,Gt,t.memoizedState.cache),ln();break;case 27:case 5:tl(e);break;case 4:_t(e,e.stateNode.containerInfo);break;case 10:il(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(sl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?ms(t,e,l):(sl(e),t=ke(t,e,l),t!==null?t.sibling:null);sl(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(nn(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return vs(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),B(Xt,Xt.current),a)break;return null;case 22:case 23:return e.lanes=0,rs(t,e,l);case 24:il(e,Gt,t.memoizedState.cache)}return ke(t,e,l)}function ys(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Vt=!0;else{if(!Hc(t,l)&&(e.flags&128)===0)return Vt=!1,uv(t,e,l);Vt=(t.flags&131072)!==0}else Vt=!1,yt&&(e.flags&1048576)!==0&&Zo(e,fu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Zi(a)?(t=Ll(a,t),e.tag=1,e=ss(null,e,a,t,l)):(e.tag=0,e=Oc(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===Ot){e.tag=11,e=us(null,e,a,t,l);break t}else if(n===X){e.tag=14,e=is(null,e,a,t,l);break t}}throw e=_e(a)||a,Error(o(306,e,""))}}return e;case 0:return Oc(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Ll(a,e.pendingProps),ss(t,e,a,n,l);case 3:t:{if(_t(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,uc(t,e),dn(e,a,null,l);var i=e.memoizedState;if(a=i.cache,il(e,Gt,a),a!==u.cache&&Ii(e,[Gt],l,!0),sn(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=ds(t,e,a,l);break t}else if(a!==n){n=be(Error(o(424)),e),an(n),e=ds(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(wt=Oe(t.firstChild),It=e,yt=!0,Hl=null,De=!0,l=Jf(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(ln(),a===n){e=ke(t,e,l);break t}Kt(t,e,a,l)}e=e.child}return e;case 26:return Nu(t,e),t===null?(l=Sd(e.type,null,e.pendingProps,null))?e.memoizedState=l:yt||(l=e.type,t=e.pendingProps,a=Zu(tt.current).createElement(l),a[$t]=e,a[te]=t,Wt(a,l,t),Qt(a),e.stateNode=a):e.memoizedState=Sd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return tl(e),t===null&&yt&&(a=e.stateNode=gd(e.type,e.pendingProps,tt.current),It=e,De=!0,n=wt,xl(e.type)?(yr=n,wt=Oe(a.firstChild)):wt=n),Kt(t,e,e.pendingProps.children,l),Nu(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&yt&&((n=a=wt)&&(a=wv(a,e.type,e.pendingProps,De),a!==null?(e.stateNode=a,It=e,wt=Oe(a.firstChild),De=!1,n=!0):n=!1),n||Bl(e)),tl(e),n=e.type,u=e.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,dr(n,u)?a=null:i!==null&&dr(n,i)&&(e.flags|=32),e.memoizedState!==null&&(n=sc(t,e,Fh,null,null,l),Cn._currentValue=n),Nu(t,e),Kt(t,e,a,l),e.child;case 6:return t===null&&yt&&((t=l=wt)&&(l=Cv(l,e.pendingProps,De),l!==null?(e.stateNode=l,It=e,wt=null,t=!0):t=!1),t||Bl(e)),null;case 13:return ms(t,e,l);case 4:return _t(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ta(e,null,a,l):Kt(t,e,a,l),e.child;case 11:return us(t,e,e.type,e.pendingProps,l);case 7:return Kt(t,e,e.pendingProps,l),e.child;case 8:return Kt(t,e,e.pendingProps.children,l),e.child;case 12:return Kt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,il(e,e.type,a.value),Kt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Yl(e),n=Ft(n),a=a(n),e.flags|=1,Kt(t,e,a,l),e.child;case 14:return is(t,e,e.type,e.pendingProps,l);case 15:return cs(t,e,e.type,e.pendingProps,l);case 19:return vs(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Du(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Ye(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return rs(t,e,l);case 24:return Yl(e),a=Ft(Gt),t===null?(n=lc(),n===null&&(n=Rt,u=tc(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},nc(e),il(e,Gt,n)):((t.lanes&l)!==0&&(uc(t,e),dn(e,null,null,l),sn()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),il(e,Gt,a)):(a=u.cache,il(e,Gt,a),a!==n.cache&&Ii(e,[Gt],l,!0))),Kt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function Ke(t){t.flags|=4}function gs(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!zd(e)){if(e=Ae.current,e!==null&&((dt&4194048)===dt?Ue!==null:(dt&62914560)!==dt&&(dt&536870912)===0||e!==Ue))throw on=ac,Io;t.flags|=8192}}function Uu(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Kr():536870912,t.lanes|=e,Ma|=e)}function pn(t,e){if(!yt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Ut(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function iv(t,e,l){var a=e.pendingProps;switch(Wi(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ut(e),null;case 1:return Ut(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Qe(Gt),ue(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(en(e)?Ke(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Jo())),Ut(e),null;case 26:return l=e.memoizedState,t===null?(Ke(e),l!==null?(Ut(e),gs(e,l)):(Ut(e),e.flags&=-16777217)):l?l!==t.memoizedState?(Ke(e),Ut(e),gs(e,l)):(Ut(e),e.flags&=-16777217):(t.memoizedProps!==a&&Ke(e),Ut(e),e.flags&=-16777217),null;case 27:el(e),l=tt.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Ut(e),null}t=Q.current,en(e)?ko(e):(t=gd(n,a,l),e.stateNode=t,Ke(e))}return Ut(e),null;case 5:if(el(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Ut(e),null}if(t=Q.current,en(e))ko(e);else{switch(n=Zu(tt.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[$t]=e,t[te]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(Wt(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Ke(e)}}return Ut(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=tt.current,en(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=It,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[$t]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||fd(t.nodeValue,l)),t||Bl(e)}else t=Zu(t).createTextNode(a),t[$t]=e,e.stateNode=t}return Ut(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=en(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(o(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(o(317));n[$t]=e}else ln(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ut(e),n=!1}else n=Jo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ze(e),e):(Ze(e),null)}if(Ze(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Uu(e,e.updateQueue),Ut(e),null;case 4:return ue(),t===null&&cr(e.stateNode.containerInfo),Ut(e),null;case 10:return Qe(e.type),Ut(e),null;case 19:if(G(Xt),n=e.memoizedState,n===null)return Ut(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)pn(n,!1);else{if(Ct!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Mu(t),u!==null){for(e.flags|=128,pn(n,!1),t=u.updateQueue,e.updateQueue=t,Uu(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Vo(l,t),l=l.sibling;return B(Xt,Xt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Ne()>ju&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Mu(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,Uu(e,t),pn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!yt)return Ut(e),null}else 2*Ne()-n.renderingStartTime>ju&&l!==536870912&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Ne(),e.sibling=null,t=Xt.current,B(Xt,a?t&1|2:t&1),e):(Ut(e),null);case 22:case 23:return Ze(e),oc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Ut(e),e.subtreeFlags&6&&(e.flags|=8192)):Ut(e),l=e.updateQueue,l!==null&&Uu(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&G(Gl),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Qe(Gt),Ut(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function cv(t,e){switch(Wi(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Qe(Gt),ue(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return el(e),null;case 13:if(Ze(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));ln()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return G(Xt),null;case 4:return ue(),null;case 10:return Qe(e.type),null;case 22:case 23:return Ze(e),oc(),t!==null&&G(Gl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Qe(Gt),null;case 25:return null;default:return null}}function bs(t,e){switch(Wi(e),e.tag){case 3:Qe(Gt),ue();break;case 26:case 27:case 5:el(e);break;case 4:ue();break;case 13:Ze(e);break;case 19:G(Xt);break;case 10:Qe(e.type);break;case 22:case 23:Ze(e),oc(),t!==null&&G(Gl);break;case 24:Qe(Gt)}}function Sn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(r){Mt(e,e.return,r)}}function ml(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,r=i.destroy;if(r!==void 0){i.destroy=void 0,n=e;var d=l,x=r;try{x()}catch(R){Mt(n,d,R)}}}a=a.next}while(a!==u)}}catch(R){Mt(e,e.return,R)}}function ps(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{uf(e,l)}catch(a){Mt(t,t.return,a)}}}function Ss(t,e,l){l.props=Ll(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Mt(t,e,a)}}function xn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Mt(t,e,n)}}function we(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Mt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Mt(t,e,n)}else l.current=null}function xs(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Mt(t,t.return,n)}}function Bc(t,e,l){try{var a=t.stateNode;Rv(a,t.type,l,e),a[te]=e}catch(n){Mt(t,t.return,n)}}function As(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&xl(t.type)||t.tag===4}function qc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||As(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&xl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Yc(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Vu));else if(a!==4&&(a===27&&xl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Yc(t,e,l),t=t.sibling;t!==null;)Yc(t,e,l),t=t.sibling}function wu(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&xl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(wu(t,e,l),t=t.sibling;t!==null;)wu(t,e,l),t=t.sibling}function Ts(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Wt(e,a,l),e[$t]=t,e[te]=l}catch(u){Mt(t,t.return,u)}}var Je=!1,Bt=!1,Gc=!1,Es=typeof WeakSet=="function"?WeakSet:Set,Zt=null;function rv(t,e){if(t=t.containerInfo,fr=Fu,t=Co(t),qi(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var i=0,r=-1,d=-1,x=0,R=0,D=t,A=null;e:for(;;){for(var T;D!==l||n!==0&&D.nodeType!==3||(r=i+n),D!==u||a!==0&&D.nodeType!==3||(d=i+a),D.nodeType===3&&(i+=D.nodeValue.length),(T=D.firstChild)!==null;)A=D,D=T;for(;;){if(D===t)break e;if(A===l&&++x===n&&(r=i),A===u&&++R===a&&(d=i),(T=D.nextSibling)!==null)break;D=A,A=D.parentNode}D=T}l=r===-1||d===-1?null:{start:r,end:d}}else l=null}l=l||{start:0,end:0}}else l=null;for(sr={focusedElem:t,selectionRange:l},Fu=!1,Zt=e;Zt!==null;)if(e=Zt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Zt=t;else for(;Zt!==null;){switch(e=Zt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var I=Ll(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(I,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(F){Mt(l,l.return,F)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)hr(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":hr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,Zt=t;break}Zt=e.return}}function zs(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:hl(t,l),a&4&&Sn(5,l);break;case 1:if(hl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(i){Mt(l,l.return,i)}else{var n=Ll(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(i){Mt(l,l.return,i)}}a&64&&ps(l),a&512&&xn(l,l.return);break;case 3:if(hl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{uf(t,e)}catch(i){Mt(l,l.return,i)}}break;case 27:e===null&&a&4&&Ts(l);case 26:case 5:hl(t,l),e===null&&a&4&&xs(l),a&512&&xn(l,l.return);break;case 12:hl(t,l);break;case 13:hl(t,l),a&4&&Rs(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=gv.bind(null,l),jv(t,l))));break;case 22:if(a=l.memoizedState!==null||Je,!a){e=e!==null&&e.memoizedState!==null||Bt,n=Je;var u=Bt;Je=a,(Bt=e)&&!u?vl(t,l,(l.subtreeFlags&8772)!==0):hl(t,l),Je=n,Bt=u}break;case 30:break;default:hl(t,l)}}function _s(t){var e=t.alternate;e!==null&&(t.alternate=null,_s(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&pi(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Nt=null,ae=!1;function We(t,e,l){for(l=l.child;l!==null;)Ms(t,e,l),l=l.sibling}function Ms(t,e,l){if(ie&&typeof ie.onCommitFiberUnmount=="function")try{ie.onCommitFiberUnmount(Xa,l)}catch{}switch(l.tag){case 26:Bt||we(l,e),We(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Bt||we(l,e);var a=Nt,n=ae;xl(l.type)&&(Nt=l.stateNode,ae=!1),We(t,e,l),Nn(l.stateNode),Nt=a,ae=n;break;case 5:Bt||we(l,e);case 6:if(a=Nt,n=ae,Nt=null,We(t,e,l),Nt=a,ae=n,Nt!==null)if(ae)try{(Nt.nodeType===9?Nt.body:Nt.nodeName==="HTML"?Nt.ownerDocument.body:Nt).removeChild(l.stateNode)}catch(u){Mt(l,e,u)}else try{Nt.removeChild(l.stateNode)}catch(u){Mt(l,e,u)}break;case 18:Nt!==null&&(ae?(t=Nt,vd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),qn(t)):vd(Nt,l.stateNode));break;case 4:a=Nt,n=ae,Nt=l.stateNode.containerInfo,ae=!0,We(t,e,l),Nt=a,ae=n;break;case 0:case 11:case 14:case 15:Bt||ml(2,l,e),Bt||ml(4,l,e),We(t,e,l);break;case 1:Bt||(we(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Ss(l,e,a)),We(t,e,l);break;case 21:We(t,e,l);break;case 22:Bt=(a=Bt)||l.memoizedState!==null,We(t,e,l),Bt=a;break;default:We(t,e,l)}}function Rs(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{qn(t)}catch(l){Mt(e,e.return,l)}}function ov(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Es),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Es),e;default:throw Error(o(435,t.tag))}}function Xc(t,e){var l=ov(t);e.forEach(function(a){var n=bv.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function fe(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,i=e,r=i;t:for(;r!==null;){switch(r.tag){case 27:if(xl(r.type)){Nt=r.stateNode,ae=!1;break t}break;case 5:Nt=r.stateNode,ae=!1;break t;case 3:case 4:Nt=r.stateNode.containerInfo,ae=!0;break t}r=r.return}if(Nt===null)throw Error(o(160));Ms(u,i,n),Nt=null,ae=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Os(e,t),e=e.sibling}var Re=null;function Os(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:fe(e,t),se(t),a&4&&(ml(3,t,t.return),Sn(3,t),ml(5,t,t.return));break;case 1:fe(e,t),se(t),a&512&&(Bt||l===null||we(l,l.return)),a&64&&Je&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Re;if(fe(e,t),se(t),a&512&&(Bt||l===null||we(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Va]||u[$t]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Wt(u,a,l),u[$t]=t,Qt(u),a=u;break t;case"link":var i=Td("link","href",n).get(a+(l.href||""));if(i){for(var r=0;r<i.length;r++)if(u=i[r],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(r,1);break e}}u=n.createElement(a),Wt(u,a,l),n.head.appendChild(u);break;case"meta":if(i=Td("meta","content",n).get(a+(l.content||""))){for(r=0;r<i.length;r++)if(u=i[r],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(r,1);break e}}u=n.createElement(a),Wt(u,a,l),n.head.appendChild(u);break;default:throw Error(o(468,a))}u[$t]=t,Qt(u),a=u}t.stateNode=a}else Ed(n,t.type,t.stateNode);else t.stateNode=Ad(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?Ed(n,t.type,t.stateNode):Ad(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Bc(t,t.memoizedProps,l.memoizedProps)}break;case 27:fe(e,t),se(t),a&512&&(Bt||l===null||we(l,l.return)),l!==null&&a&4&&Bc(t,t.memoizedProps,l.memoizedProps);break;case 5:if(fe(e,t),se(t),a&512&&(Bt||l===null||we(l,l.return)),t.flags&32){n=t.stateNode;try{ua(n,"")}catch(T){Mt(t,t.return,T)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Bc(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Gc=!0);break;case 6:if(fe(e,t),se(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(T){Mt(t,t.return,T)}}break;case 3:if(Ju=null,n=Re,Re=ku(e.containerInfo),fe(e,t),Re=n,se(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{qn(e.containerInfo)}catch(T){Mt(t,t.return,T)}Gc&&(Gc=!1,Ns(t));break;case 4:a=Re,Re=ku(t.stateNode.containerInfo),fe(e,t),se(t),Re=a;break;case 12:fe(e,t),se(t);break;case 13:fe(e,t),se(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Kc=Ne()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Xc(t,a)));break;case 22:n=t.memoizedState!==null;var d=l!==null&&l.memoizedState!==null,x=Je,R=Bt;if(Je=x||n,Bt=R||d,fe(e,t),Bt=R,Je=x,se(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||d||Je||Bt||Ql(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){d=l=e;try{if(u=d.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{r=d.stateNode;var D=d.memoizedProps.style,A=D!=null&&D.hasOwnProperty("display")?D.display:null;r.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(T){Mt(d,d.return,T)}}}else if(e.tag===6){if(l===null){d=e;try{d.stateNode.nodeValue=n?"":d.memoizedProps}catch(T){Mt(d,d.return,T)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Xc(t,l))));break;case 19:fe(e,t),se(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Xc(t,a)));break;case 30:break;case 21:break;default:fe(e,t),se(t)}}function se(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(As(a)){l=a;break}a=a.return}if(l==null)throw Error(o(160));switch(l.tag){case 27:var n=l.stateNode,u=qc(t);wu(t,u,n);break;case 5:var i=l.stateNode;l.flags&32&&(ua(i,""),l.flags&=-33);var r=qc(t);wu(t,r,i);break;case 3:case 4:var d=l.stateNode.containerInfo,x=qc(t);Yc(t,x,d);break;default:throw Error(o(161))}}catch(R){Mt(t,t.return,R)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Ns(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Ns(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function hl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)zs(t,e.alternate,e),e=e.sibling}function Ql(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ml(4,e,e.return),Ql(e);break;case 1:we(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Ss(e,e.return,l),Ql(e);break;case 27:Nn(e.stateNode);case 26:case 5:we(e,e.return),Ql(e);break;case 22:e.memoizedState===null&&Ql(e);break;case 30:Ql(e);break;default:Ql(e)}t=t.sibling}}function vl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,i=u.flags;switch(u.tag){case 0:case 11:case 15:vl(n,u,l),Sn(4,u);break;case 1:if(vl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(x){Mt(a,a.return,x)}if(a=u,n=a.updateQueue,n!==null){var r=a.stateNode;try{var d=n.shared.hiddenCallbacks;if(d!==null)for(n.shared.hiddenCallbacks=null,n=0;n<d.length;n++)nf(d[n],r)}catch(x){Mt(a,a.return,x)}}l&&i&64&&ps(u),xn(u,u.return);break;case 27:Ts(u);case 26:case 5:vl(n,u,l),l&&a===null&&i&4&&xs(u),xn(u,u.return);break;case 12:vl(n,u,l);break;case 13:vl(n,u,l),l&&i&4&&Rs(n,u);break;case 22:u.memoizedState===null&&vl(n,u,l),xn(u,u.return);break;case 30:break;default:vl(n,u,l)}e=e.sibling}}function Lc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&un(l))}function Qc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t))}function Ce(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ds(t,e,l,a),e=e.sibling}function Ds(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:Ce(t,e,l,a),n&2048&&Sn(9,e);break;case 1:Ce(t,e,l,a);break;case 3:Ce(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t)));break;case 12:if(n&2048){Ce(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,i=u.id,r=u.onPostCommit;typeof r=="function"&&r(i,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(d){Mt(e,e.return,d)}}else Ce(t,e,l,a);break;case 13:Ce(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,i=e.alternate,e.memoizedState!==null?u._visibility&2?Ce(t,e,l,a):An(t,e):u._visibility&2?Ce(t,e,l,a):(u._visibility|=2,Ea(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Lc(i,e);break;case 24:Ce(t,e,l,a),n&2048&&Qc(e.alternate,e);break;default:Ce(t,e,l,a)}}function Ea(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,i=e,r=l,d=a,x=i.flags;switch(i.tag){case 0:case 11:case 15:Ea(u,i,r,d,n),Sn(8,i);break;case 23:break;case 22:var R=i.stateNode;i.memoizedState!==null?R._visibility&2?Ea(u,i,r,d,n):An(u,i):(R._visibility|=2,Ea(u,i,r,d,n)),n&&x&2048&&Lc(i.alternate,i);break;case 24:Ea(u,i,r,d,n),n&&x&2048&&Qc(i.alternate,i);break;default:Ea(u,i,r,d,n)}e=e.sibling}}function An(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:An(l,a),n&2048&&Lc(a.alternate,a);break;case 24:An(l,a),n&2048&&Qc(a.alternate,a);break;default:An(l,a)}e=e.sibling}}var Tn=8192;function za(t){if(t.subtreeFlags&Tn)for(t=t.child;t!==null;)Us(t),t=t.sibling}function Us(t){switch(t.tag){case 26:za(t),t.flags&Tn&&t.memoizedState!==null&&Jv(Re,t.memoizedState,t.memoizedProps);break;case 5:za(t);break;case 3:case 4:var e=Re;Re=ku(t.stateNode.containerInfo),za(t),Re=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Tn,Tn=16777216,za(t),Tn=e):za(t));break;default:za(t)}}function ws(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function En(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Zt=a,js(a,t)}ws(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Cs(t),t=t.sibling}function Cs(t){switch(t.tag){case 0:case 11:case 15:En(t),t.flags&2048&&ml(9,t,t.return);break;case 3:En(t);break;case 12:En(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Cu(t)):En(t);break;default:En(t)}}function Cu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Zt=a,js(a,t)}ws(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ml(8,e,e.return),Cu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Cu(e));break;default:Cu(e)}t=t.sibling}}function js(t,e){for(;Zt!==null;){var l=Zt;switch(l.tag){case 0:case 11:case 15:ml(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:un(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Zt=a;else t:for(l=t;Zt!==null;){a=Zt;var n=a.sibling,u=a.return;if(_s(a),a===l){Zt=null;break t}if(n!==null){n.return=u,Zt=n;break t}Zt=u}}}var fv={getCacheForType:function(t){var e=Ft(Gt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},sv=typeof WeakMap=="function"?WeakMap:Map,xt=0,Rt=null,ft=null,dt=0,At=0,de=null,yl=!1,_a=!1,Vc=!1,$e=0,Ct=0,gl=0,Vl=0,Zc=0,Te=0,Ma=0,zn=null,ne=null,kc=!1,Kc=0,ju=1/0,Hu=null,bl=null,Jt=0,pl=null,Ra=null,Oa=0,Jc=0,Wc=null,Hs=null,_n=0,$c=null;function me(){if((xt&2)!==0&&dt!==0)return dt&-dt;if(_.T!==null){var t=ya;return t!==0?t:ar()}return $r()}function Bs(){Te===0&&(Te=(dt&536870912)===0||yt?kr():536870912);var t=Ae.current;return t!==null&&(t.flags|=32),Te}function he(t,e,l){(t===Rt&&(At===2||At===9)||t.cancelPendingCommit!==null)&&(Na(t,0),Sl(t,dt,Te,!1)),Qa(t,l),((xt&2)===0||t!==Rt)&&(t===Rt&&((xt&2)===0&&(Vl|=l),Ct===4&&Sl(t,dt,Te,!1)),je(t))}function qs(t,e,l){if((xt&6)!==0)throw Error(o(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||La(t,e),n=a?hv(t,e):Ic(t,e,!0),u=a;do{if(n===0){_a&&!a&&Sl(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!dv(l)){n=Ic(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){e=i;t:{var r=t;n=zn;var d=r.current.memoizedState.isDehydrated;if(d&&(Na(r,i).flags|=256),i=Ic(r,i,!1),i!==2){if(Vc&&!d){r.errorRecoveryDisabledLanes|=u,Vl|=u,n=4;break t}u=ne,ne=n,u!==null&&(ne===null?ne=u:ne.push.apply(ne,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){Na(t,0),Sl(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Sl(a,e,Te,!yl);break t;case 2:ne=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(n=Kc+300-Ne(),10<n)){if(Sl(a,e,Te,!yl),Kn(a,0,!0)!==0)break t;a.timeoutHandle=md(Ys.bind(null,a,l,ne,Hu,kc,e,Te,Vl,Ma,yl,u,2,-0,0),n);break t}Ys(a,l,ne,Hu,kc,e,Te,Vl,Ma,yl,u,0,-0,0)}}break}while(!0);je(t)}function Ys(t,e,l,a,n,u,i,r,d,x,R,D,A,T){if(t.timeoutHandle=-1,D=e.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(wn={stylesheets:null,count:0,unsuspend:Kv},Us(e),D=Wv(),D!==null)){t.cancelPendingCommit=D(ks.bind(null,t,e,u,l,a,n,i,r,d,R,1,A,T)),Sl(t,u,i,!x);return}ks(t,e,u,l,a,n,i,r,d)}function dv(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!re(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Sl(t,e,l,a){e&=~Zc,e&=~Vl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-ce(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&Jr(t,l,e)}function Bu(){return(xt&6)===0?(Mn(0),!1):!0}function Fc(){if(ft!==null){if(At===0)var t=ft.return;else t=ft,Le=ql=null,hc(t),Aa=null,gn=0,t=ft;for(;t!==null;)bs(t.alternate,t),t=t.return;ft=null}}function Na(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Nv(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Fc(),Rt=t,ft=l=Ye(t.current,null),dt=e,At=0,de=null,yl=!1,_a=La(t,e),Vc=!1,Ma=Te=Zc=Vl=gl=Ct=0,ne=zn=null,kc=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-ce(a),u=1<<n;e|=t[n],a&=~u}return $e=e,uu(),l}function Gs(t,e){nt=null,_.H=Eu,e===rn||e===hu?(e=lf(),At=3):e===Io?(e=lf(),At=4):At=e===ns?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,de=e,ft===null&&(Ct=1,Ou(t,be(e,t.current)))}function Xs(){var t=_.H;return _.H=Eu,t===null?Eu:t}function Ls(){var t=_.A;return _.A=fv,t}function Pc(){Ct=4,yl||(dt&4194048)!==dt&&Ae.current!==null||(_a=!0),(gl&134217727)===0&&(Vl&134217727)===0||Rt===null||Sl(Rt,dt,Te,!1)}function Ic(t,e,l){var a=xt;xt|=2;var n=Xs(),u=Ls();(Rt!==t||dt!==e)&&(Hu=null,Na(t,e)),e=!1;var i=Ct;t:do try{if(At!==0&&ft!==null){var r=ft,d=de;switch(At){case 8:Fc(),i=6;break t;case 3:case 2:case 9:case 6:Ae.current===null&&(e=!0);var x=At;if(At=0,de=null,Da(t,r,d,x),l&&_a){i=0;break t}break;default:x=At,At=0,de=null,Da(t,r,d,x)}}mv(),i=Ct;break}catch(R){Gs(t,R)}while(!0);return e&&t.shellSuspendCounter++,Le=ql=null,xt=a,_.H=n,_.A=u,ft===null&&(Rt=null,dt=0,uu()),i}function mv(){for(;ft!==null;)Qs(ft)}function hv(t,e){var l=xt;xt|=2;var a=Xs(),n=Ls();Rt!==t||dt!==e?(Hu=null,ju=Ne()+500,Na(t,e)):_a=La(t,e);t:do try{if(At!==0&&ft!==null){e=ft;var u=de;e:switch(At){case 1:At=0,de=null,Da(t,e,u,1);break;case 2:case 9:if(tf(u)){At=0,de=null,Vs(e);break}e=function(){At!==2&&At!==9||Rt!==t||(At=7),je(t)},u.then(e,e);break t;case 3:At=7;break t;case 4:At=5;break t;case 7:tf(u)?(At=0,de=null,Vs(e)):(At=0,de=null,Da(t,e,u,7));break;case 5:var i=null;switch(ft.tag){case 26:i=ft.memoizedState;case 5:case 27:var r=ft;if(!i||zd(i)){At=0,de=null;var d=r.sibling;if(d!==null)ft=d;else{var x=r.return;x!==null?(ft=x,qu(x)):ft=null}break e}}At=0,de=null,Da(t,e,u,5);break;case 6:At=0,de=null,Da(t,e,u,6);break;case 8:Fc(),Ct=6;break t;default:throw Error(o(462))}}vv();break}catch(R){Gs(t,R)}while(!0);return Le=ql=null,_.H=a,_.A=n,xt=l,ft!==null?0:(Rt=null,dt=0,uu(),Ct)}function vv(){for(;ft!==null&&!Bm();)Qs(ft)}function Qs(t){var e=ys(t.alternate,t,$e);t.memoizedProps=t.pendingProps,e===null?qu(t):ft=e}function Vs(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=fs(l,e,e.pendingProps,e.type,void 0,dt);break;case 11:e=fs(l,e,e.pendingProps,e.type.render,e.ref,dt);break;case 5:hc(e);default:bs(l,e),e=ft=Vo(e,$e),e=ys(l,e,$e)}t.memoizedProps=t.pendingProps,e===null?qu(t):ft=e}function Da(t,e,l,a){Le=ql=null,hc(e),Aa=null,gn=0;var n=e.return;try{if(nv(t,n,e,l,dt)){Ct=1,Ou(t,be(l,t.current)),ft=null;return}}catch(u){if(n!==null)throw ft=n,u;Ct=1,Ou(t,be(l,t.current)),ft=null;return}e.flags&32768?(yt||a===1?t=!0:_a||(dt&536870912)!==0?t=!1:(yl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Ae.current,a!==null&&a.tag===13&&(a.flags|=16384))),Zs(e,t)):qu(e)}function qu(t){var e=t;do{if((e.flags&32768)!==0){Zs(e,yl);return}t=e.return;var l=iv(e.alternate,e,$e);if(l!==null){ft=l;return}if(e=e.sibling,e!==null){ft=e;return}ft=e=t}while(e!==null);Ct===0&&(Ct=5)}function Zs(t,e){do{var l=cv(t.alternate,t);if(l!==null){l.flags&=32767,ft=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ft=t;return}ft=t=l}while(t!==null);Ct=6,ft=null}function ks(t,e,l,a,n,u,i,r,d){t.cancelPendingCommit=null;do Yu();while(Jt!==0);if((xt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(u=e.lanes|e.childLanes,u|=Qi,Km(t,l,u,i,r,d),t===Rt&&(ft=Rt=null,dt=0),Ra=e,pl=t,Oa=l,Jc=u,Wc=n,Hs=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,pv(Vn,function(){return Fs(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=_.T,_.T=null,n=Y.p,Y.p=2,i=xt,xt|=4;try{rv(t,e,l)}finally{xt=i,Y.p=n,_.T=a}}Jt=1,Ks(),Js(),Ws()}}function Ks(){if(Jt===1){Jt=0;var t=pl,e=Ra,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=_.T,_.T=null;var a=Y.p;Y.p=2;var n=xt;xt|=4;try{Os(e,t);var u=sr,i=Co(t.containerInfo),r=u.focusedElem,d=u.selectionRange;if(i!==r&&r&&r.ownerDocument&&wo(r.ownerDocument.documentElement,r)){if(d!==null&&qi(r)){var x=d.start,R=d.end;if(R===void 0&&(R=x),"selectionStart"in r)r.selectionStart=x,r.selectionEnd=Math.min(R,r.value.length);else{var D=r.ownerDocument||document,A=D&&D.defaultView||window;if(A.getSelection){var T=A.getSelection(),I=r.textContent.length,F=Math.min(d.start,I),zt=d.end===void 0?F:Math.min(d.end,I);!T.extend&&F>zt&&(i=zt,zt=F,F=i);var g=Uo(r,F),v=Uo(r,zt);if(g&&v&&(T.rangeCount!==1||T.anchorNode!==g.node||T.anchorOffset!==g.offset||T.focusNode!==v.node||T.focusOffset!==v.offset)){var S=D.createRange();S.setStart(g.node,g.offset),T.removeAllRanges(),F>zt?(T.addRange(S),T.extend(v.node,v.offset)):(S.setEnd(v.node,v.offset),T.addRange(S))}}}}for(D=[],T=r;T=T.parentNode;)T.nodeType===1&&D.push({element:T,left:T.scrollLeft,top:T.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<D.length;r++){var N=D[r];N.element.scrollLeft=N.left,N.element.scrollTop=N.top}}Fu=!!fr,sr=fr=null}finally{xt=n,Y.p=a,_.T=l}}t.current=e,Jt=2}}function Js(){if(Jt===2){Jt=0;var t=pl,e=Ra,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=_.T,_.T=null;var a=Y.p;Y.p=2;var n=xt;xt|=4;try{zs(t,e.alternate,e)}finally{xt=n,Y.p=a,_.T=l}}Jt=3}}function Ws(){if(Jt===4||Jt===3){Jt=0,qm();var t=pl,e=Ra,l=Oa,a=Hs;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Jt=5:(Jt=0,Ra=pl=null,$s(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(bl=null),gi(l),e=e.stateNode,ie&&typeof ie.onCommitFiberRoot=="function")try{ie.onCommitFiberRoot(Xa,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=_.T,n=Y.p,Y.p=2,_.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var r=a[i];u(r.value,{componentStack:r.stack})}}finally{_.T=e,Y.p=n}}(Oa&3)!==0&&Yu(),je(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===$c?_n++:(_n=0,$c=t):_n=0,Mn(0)}}function $s(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,un(e)))}function Yu(t){return Ks(),Js(),Ws(),Fs()}function Fs(){if(Jt!==5)return!1;var t=pl,e=Jc;Jc=0;var l=gi(Oa),a=_.T,n=Y.p;try{Y.p=32>l?32:l,_.T=null,l=Wc,Wc=null;var u=pl,i=Oa;if(Jt=0,Ra=pl=null,Oa=0,(xt&6)!==0)throw Error(o(331));var r=xt;if(xt|=4,Cs(u.current),Ds(u,u.current,i,l),xt=r,Mn(0,!1),ie&&typeof ie.onPostCommitFiberRoot=="function")try{ie.onPostCommitFiberRoot(Xa,u)}catch{}return!0}finally{Y.p=n,_.T=a,$s(t,e)}}function Ps(t,e,l){e=be(l,e),e=Rc(t.stateNode,e,2),t=ol(t,e,2),t!==null&&(Qa(t,2),je(t))}function Mt(t,e,l){if(t.tag===3)Ps(t,t,l);else for(;e!==null;){if(e.tag===3){Ps(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(bl===null||!bl.has(a))){t=be(l,t),l=ls(2),a=ol(e,l,2),a!==null&&(as(l,a,e,t),Qa(a,2),je(a));break}}e=e.return}}function tr(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new sv;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Vc=!0,n.add(l),t=yv.bind(null,t,e,l),e.then(t,t))}function yv(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Rt===t&&(dt&l)===l&&(Ct===4||Ct===3&&(dt&62914560)===dt&&300>Ne()-Kc?(xt&2)===0&&Na(t,0):Zc|=l,Ma===dt&&(Ma=0)),je(t)}function Is(t,e){e===0&&(e=Kr()),t=da(t,e),t!==null&&(Qa(t,e),je(t))}function gv(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),Is(t,l)}function bv(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),Is(t,l)}function pv(t,e){return mi(t,e)}var Gu=null,Ua=null,er=!1,Xu=!1,lr=!1,Zl=0;function je(t){t!==Ua&&t.next===null&&(Ua===null?Gu=Ua=t:Ua=Ua.next=t),Xu=!0,er||(er=!0,xv())}function Mn(t,e){if(!lr&&Xu){lr=!0;do for(var l=!1,a=Gu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,r=a.pingedLanes;u=(1<<31-ce(42|t)+1)-1,u&=n&~(i&~r),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,ad(a,u))}else u=dt,u=Kn(a,a===Rt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||La(a,u)||(l=!0,ad(a,u));a=a.next}while(l);lr=!1}}function Sv(){td()}function td(){Xu=er=!1;var t=0;Zl!==0&&(Ov()&&(t=Zl),Zl=0);for(var e=Ne(),l=null,a=Gu;a!==null;){var n=a.next,u=ed(a,e);u===0?(a.next=null,l===null?Gu=n:l.next=n,n===null&&(Ua=l)):(l=a,(t!==0||(u&3)!==0)&&(Xu=!0)),a=n}Mn(t)}function ed(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-ce(u),r=1<<i,d=n[i];d===-1?((r&l)===0||(r&a)!==0)&&(n[i]=km(r,e)):d<=e&&(t.expiredLanes|=r),u&=~r}if(e=Rt,l=dt,l=Kn(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(At===2||At===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&hi(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||La(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&hi(a),gi(l)){case 2:case 8:l=Vr;break;case 32:l=Vn;break;case 268435456:l=Zr;break;default:l=Vn}return a=ld.bind(null,t),l=mi(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&hi(a),t.callbackPriority=2,t.callbackNode=null,2}function ld(t,e){if(Jt!==0&&Jt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Yu()&&t.callbackNode!==l)return null;var a=dt;return a=Kn(t,t===Rt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(qs(t,a,e),ed(t,Ne()),t.callbackNode!=null&&t.callbackNode===l?ld.bind(null,t):null)}function ad(t,e){if(Yu())return null;qs(t,e,!0)}function xv(){Dv(function(){(xt&6)!==0?mi(Qr,Sv):td()})}function ar(){return Zl===0&&(Zl=kr()),Zl}function nd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Pn(""+t)}function ud(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Av(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=nd((n[te]||null).action),i=a.submitter;i&&(e=(e=i[te]||null)?nd(e.formAction):i.getAttribute("formAction"),e!==null&&(u=e,i=null));var r=new lu("action","action",null,a,n);t.push({event:r,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Zl!==0){var d=i?ud(n,i):new FormData(n);Tc(l,{pending:!0,data:d,method:n.method,action:u},null,d)}}else typeof u=="function"&&(r.preventDefault(),d=i?ud(n,i):new FormData(n),Tc(l,{pending:!0,data:d,method:n.method,action:u},u,d))},currentTarget:n}]})}}for(var nr=0;nr<Li.length;nr++){var ur=Li[nr],Tv=ur.toLowerCase(),Ev=ur[0].toUpperCase()+ur.slice(1);Me(Tv,"on"+Ev)}Me(Bo,"onAnimationEnd"),Me(qo,"onAnimationIteration"),Me(Yo,"onAnimationStart"),Me("dblclick","onDoubleClick"),Me("focusin","onFocus"),Me("focusout","onBlur"),Me(Xh,"onTransitionRun"),Me(Lh,"onTransitionStart"),Me(Qh,"onTransitionCancel"),Me(Go,"onTransitionEnd"),la("onMouseEnter",["mouseout","mouseover"]),la("onMouseLeave",["mouseout","mouseover"]),la("onPointerEnter",["pointerout","pointerover"]),la("onPointerLeave",["pointerout","pointerover"]),Ol("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ol("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ol("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ol("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ol("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ol("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Rn));function id(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var i=a.length-1;0<=i;i--){var r=a[i],d=r.instance,x=r.currentTarget;if(r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=x;try{u(n)}catch(R){Ru(R)}n.currentTarget=null,u=d}else for(i=0;i<a.length;i++){if(r=a[i],d=r.instance,x=r.currentTarget,r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=x;try{u(n)}catch(R){Ru(R)}n.currentTarget=null,u=d}}}}function st(t,e){var l=e[bi];l===void 0&&(l=e[bi]=new Set);var a=t+"__bubble";l.has(a)||(cd(e,t,2,!1),l.add(a))}function ir(t,e,l){var a=0;e&&(a|=4),cd(l,t,a,e)}var Lu="_reactListening"+Math.random().toString(36).slice(2);function cr(t){if(!t[Lu]){t[Lu]=!0,Pr.forEach(function(l){l!=="selectionchange"&&(zv.has(l)||ir(l,!1,t),ir(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Lu]||(e[Lu]=!0,ir("selectionchange",!1,e))}}function cd(t,e,l,a){switch(Dd(e)){case 2:var n=Pv;break;case 8:n=Iv;break;default:n=xr}l=n.bind(null,e,l,t),n=void 0,!Oi||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function rr(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var r=a.stateNode.containerInfo;if(r===n)break;if(i===4)for(i=a.return;i!==null;){var d=i.tag;if((d===3||d===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;r!==null;){if(i=Il(r),i===null)return;if(d=i.tag,d===5||d===6||d===26||d===27){a=u=i;continue t}r=r.parentNode}}a=a.return}mo(function(){var x=u,R=Mi(l),D=[];t:{var A=Xo.get(t);if(A!==void 0){var T=lu,I=t;switch(t){case"keypress":if(tu(l)===0)break t;case"keydown":case"keyup":T=ph;break;case"focusin":I="focus",T=wi;break;case"focusout":I="blur",T=wi;break;case"beforeblur":case"afterblur":T=wi;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=yo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=ch;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=Ah;break;case Bo:case qo:case Yo:T=fh;break;case Go:T=Eh;break;case"scroll":case"scrollend":T=uh;break;case"wheel":T=_h;break;case"copy":case"cut":case"paste":T=dh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=bo;break;case"toggle":case"beforetoggle":T=Rh}var F=(e&4)!==0,zt=!F&&(t==="scroll"||t==="scrollend"),g=F?A!==null?A+"Capture":null:A;F=[];for(var v=x,S;v!==null;){var N=v;if(S=N.stateNode,N=N.tag,N!==5&&N!==26&&N!==27||S===null||g===null||(N=ka(v,g),N!=null&&F.push(On(v,N,S))),zt)break;v=v.return}0<F.length&&(A=new T(A,I,null,l,R),D.push({event:A,listeners:F}))}}if((e&7)===0){t:{if(A=t==="mouseover"||t==="pointerover",T=t==="mouseout"||t==="pointerout",A&&l!==_i&&(I=l.relatedTarget||l.fromElement)&&(Il(I)||I[Pl]))break t;if((T||A)&&(A=R.window===R?R:(A=R.ownerDocument)?A.defaultView||A.parentWindow:window,T?(I=l.relatedTarget||l.toElement,T=x,I=I?Il(I):null,I!==null&&(zt=b(I),F=I.tag,I!==zt||F!==5&&F!==27&&F!==6)&&(I=null)):(T=null,I=x),T!==I)){if(F=yo,N="onMouseLeave",g="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(F=bo,N="onPointerLeave",g="onPointerEnter",v="pointer"),zt=T==null?A:Za(T),S=I==null?A:Za(I),A=new F(N,v+"leave",T,l,R),A.target=zt,A.relatedTarget=S,N=null,Il(R)===x&&(F=new F(g,v+"enter",I,l,R),F.target=S,F.relatedTarget=zt,N=F),zt=N,T&&I)e:{for(F=T,g=I,v=0,S=F;S;S=wa(S))v++;for(S=0,N=g;N;N=wa(N))S++;for(;0<v-S;)F=wa(F),v--;for(;0<S-v;)g=wa(g),S--;for(;v--;){if(F===g||g!==null&&F===g.alternate)break e;F=wa(F),g=wa(g)}F=null}else F=null;T!==null&&rd(D,A,T,F,!1),I!==null&&zt!==null&&rd(D,zt,I,F,!0)}}t:{if(A=x?Za(x):window,T=A.nodeName&&A.nodeName.toLowerCase(),T==="select"||T==="input"&&A.type==="file")var V=_o;else if(Eo(A))if(Mo)V=qh;else{V=Hh;var it=jh}else T=A.nodeName,!T||T.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?x&&zi(x.elementType)&&(V=_o):V=Bh;if(V&&(V=V(t,x))){zo(D,V,l,R);break t}it&&it(t,A,x),t==="focusout"&&x&&A.type==="number"&&x.memoizedProps.value!=null&&Ei(A,"number",A.value)}switch(it=x?Za(x):window,t){case"focusin":(Eo(it)||it.contentEditable==="true")&&(oa=it,Yi=x,tn=null);break;case"focusout":tn=Yi=oa=null;break;case"mousedown":Gi=!0;break;case"contextmenu":case"mouseup":case"dragend":Gi=!1,jo(D,l,R);break;case"selectionchange":if(Gh)break;case"keydown":case"keyup":jo(D,l,R)}var J;if(ji)t:{switch(t){case"compositionstart":var P="onCompositionStart";break t;case"compositionend":P="onCompositionEnd";break t;case"compositionupdate":P="onCompositionUpdate";break t}P=void 0}else ra?Ao(t,l)&&(P="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(P="onCompositionStart");P&&(po&&l.locale!=="ko"&&(ra||P!=="onCompositionStart"?P==="onCompositionEnd"&&ra&&(J=ho()):(ul=R,Ni="value"in ul?ul.value:ul.textContent,ra=!0)),it=Qu(x,P),0<it.length&&(P=new go(P,t,null,l,R),D.push({event:P,listeners:it}),J?P.data=J:(J=To(l),J!==null&&(P.data=J)))),(J=Nh?Dh(t,l):Uh(t,l))&&(P=Qu(x,"onBeforeInput"),0<P.length&&(it=new go("onBeforeInput","beforeinput",null,l,R),D.push({event:it,listeners:P}),it.data=J)),Av(D,t,x,l,R)}id(D,e)})}function On(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Qu(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=ka(t,l),n!=null&&a.unshift(On(t,n,u)),n=ka(t,e),n!=null&&a.push(On(t,n,u))),t.tag===3)return a;t=t.return}return[]}function wa(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function rd(t,e,l,a,n){for(var u=e._reactName,i=[];l!==null&&l!==a;){var r=l,d=r.alternate,x=r.stateNode;if(r=r.tag,d!==null&&d===a)break;r!==5&&r!==26&&r!==27||x===null||(d=x,n?(x=ka(l,u),x!=null&&i.unshift(On(l,x,d))):n||(x=ka(l,u),x!=null&&i.push(On(l,x,d)))),l=l.return}i.length!==0&&t.push({event:e,listeners:i})}var _v=/\r\n?/g,Mv=/\u0000|\uFFFD/g;function od(t){return(typeof t=="string"?t:""+t).replace(_v,`
`).replace(Mv,"")}function fd(t,e){return e=od(e),od(t)===e}function Vu(){}function Et(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ua(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ua(t,""+a);break;case"className":Wn(t,"class",a);break;case"tabIndex":Wn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Wn(t,l,a);break;case"style":fo(t,a,u);break;case"data":if(e!=="object"){Wn(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&Et(t,e,"name",n.name,n,null),Et(t,e,"formEncType",n.formEncType,n,null),Et(t,e,"formMethod",n.formMethod,n,null),Et(t,e,"formTarget",n.formTarget,n,null)):(Et(t,e,"encType",n.encType,n,null),Et(t,e,"method",n.method,n,null),Et(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"onScroll":a!=null&&st("scroll",t);break;case"onScrollEnd":a!=null&&st("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Pn(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":st("beforetoggle",t),st("toggle",t),Jn(t,"popover",a);break;case"xlinkActuate":Be(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Be(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Be(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Be(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Be(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Be(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Be(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Be(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Be(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Jn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=ah.get(l)||l,Jn(t,l,a))}}function or(t,e,l,a,n,u){switch(l){case"style":fo(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"children":typeof a=="string"?ua(t,a):(typeof a=="number"||typeof a=="bigint")&&ua(t,""+a);break;case"onScroll":a!=null&&st("scroll",t);break;case"onScrollEnd":a!=null&&st("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ir.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[te]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Jn(t,l,a)}}}function Wt(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":st("error",t),st("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Et(t,e,u,i,l,null)}}n&&Et(t,e,"srcSet",l.srcSet,l,null),a&&Et(t,e,"src",l.src,l,null);return;case"input":st("invalid",t);var r=u=i=n=null,d=null,x=null;for(a in l)if(l.hasOwnProperty(a)){var R=l[a];if(R!=null)switch(a){case"name":n=R;break;case"type":i=R;break;case"checked":d=R;break;case"defaultChecked":x=R;break;case"value":u=R;break;case"defaultValue":r=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(o(137,e));break;default:Et(t,e,a,R,l,null)}}io(t,u,r,d,x,i,n,!1),$n(t);return;case"select":st("invalid",t),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(r=l[n],r!=null))switch(n){case"value":u=r;break;case"defaultValue":i=r;break;case"multiple":a=r;default:Et(t,e,n,r,l,null)}e=u,l=i,t.multiple=!!a,e!=null?na(t,!!a,e,!1):l!=null&&na(t,!!a,l,!0);return;case"textarea":st("invalid",t),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(r=l[i],r!=null))switch(i){case"value":a=r;break;case"defaultValue":n=r;break;case"children":u=r;break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(o(91));break;default:Et(t,e,i,r,l,null)}ro(t,a,n,u),$n(t);return;case"option":for(d in l)if(l.hasOwnProperty(d)&&(a=l[d],a!=null))switch(d){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Et(t,e,d,a,l,null)}return;case"dialog":st("beforetoggle",t),st("toggle",t),st("cancel",t),st("close",t);break;case"iframe":case"object":st("load",t);break;case"video":case"audio":for(a=0;a<Rn.length;a++)st(Rn[a],t);break;case"image":st("error",t),st("load",t);break;case"details":st("toggle",t);break;case"embed":case"source":case"link":st("error",t),st("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(x in l)if(l.hasOwnProperty(x)&&(a=l[x],a!=null))switch(x){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Et(t,e,x,a,l,null)}return;default:if(zi(e)){for(R in l)l.hasOwnProperty(R)&&(a=l[R],a!==void 0&&or(t,e,R,a,l,void 0));return}}for(r in l)l.hasOwnProperty(r)&&(a=l[r],a!=null&&Et(t,e,r,a,l,null))}function Rv(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,r=null,d=null,x=null,R=null;for(T in l){var D=l[T];if(l.hasOwnProperty(T)&&D!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":d=D;default:a.hasOwnProperty(T)||Et(t,e,T,null,a,D)}}for(var A in a){var T=a[A];if(D=l[A],a.hasOwnProperty(A)&&(T!=null||D!=null))switch(A){case"type":u=T;break;case"name":n=T;break;case"checked":x=T;break;case"defaultChecked":R=T;break;case"value":i=T;break;case"defaultValue":r=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(o(137,e));break;default:T!==D&&Et(t,e,A,T,a,D)}}Ti(t,i,r,d,x,R,u,n);return;case"select":T=i=r=A=null;for(u in l)if(d=l[u],l.hasOwnProperty(u)&&d!=null)switch(u){case"value":break;case"multiple":T=d;default:a.hasOwnProperty(u)||Et(t,e,u,null,a,d)}for(n in a)if(u=a[n],d=l[n],a.hasOwnProperty(n)&&(u!=null||d!=null))switch(n){case"value":A=u;break;case"defaultValue":r=u;break;case"multiple":i=u;default:u!==d&&Et(t,e,n,u,a,d)}e=r,l=i,a=T,A!=null?na(t,!!l,A,!1):!!a!=!!l&&(e!=null?na(t,!!l,e,!0):na(t,!!l,l?[]:"",!1));return;case"textarea":T=A=null;for(r in l)if(n=l[r],l.hasOwnProperty(r)&&n!=null&&!a.hasOwnProperty(r))switch(r){case"value":break;case"children":break;default:Et(t,e,r,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":A=n;break;case"defaultValue":T=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(o(91));break;default:n!==u&&Et(t,e,i,n,a,u)}co(t,A,T);return;case"option":for(var I in l)if(A=l[I],l.hasOwnProperty(I)&&A!=null&&!a.hasOwnProperty(I))switch(I){case"selected":t.selected=!1;break;default:Et(t,e,I,null,a,A)}for(d in a)if(A=a[d],T=l[d],a.hasOwnProperty(d)&&A!==T&&(A!=null||T!=null))switch(d){case"selected":t.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:Et(t,e,d,A,a,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var F in l)A=l[F],l.hasOwnProperty(F)&&A!=null&&!a.hasOwnProperty(F)&&Et(t,e,F,null,a,A);for(x in a)if(A=a[x],T=l[x],a.hasOwnProperty(x)&&A!==T&&(A!=null||T!=null))switch(x){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(o(137,e));break;default:Et(t,e,x,A,a,T)}return;default:if(zi(e)){for(var zt in l)A=l[zt],l.hasOwnProperty(zt)&&A!==void 0&&!a.hasOwnProperty(zt)&&or(t,e,zt,void 0,a,A);for(R in a)A=a[R],T=l[R],!a.hasOwnProperty(R)||A===T||A===void 0&&T===void 0||or(t,e,R,A,a,T);return}}for(var g in l)A=l[g],l.hasOwnProperty(g)&&A!=null&&!a.hasOwnProperty(g)&&Et(t,e,g,null,a,A);for(D in a)A=a[D],T=l[D],!a.hasOwnProperty(D)||A===T||A==null&&T==null||Et(t,e,D,A,a,T)}var fr=null,sr=null;function Zu(t){return t.nodeType===9?t:t.ownerDocument}function sd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function dd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function dr(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var mr=null;function Ov(){var t=window.event;return t&&t.type==="popstate"?t===mr?!1:(mr=t,!0):(mr=null,!1)}var md=typeof setTimeout=="function"?setTimeout:void 0,Nv=typeof clearTimeout=="function"?clearTimeout:void 0,hd=typeof Promise=="function"?Promise:void 0,Dv=typeof queueMicrotask=="function"?queueMicrotask:typeof hd<"u"?function(t){return hd.resolve(null).then(t).catch(Uv)}:md;function Uv(t){setTimeout(function(){throw t})}function xl(t){return t==="head"}function vd(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var i=t.ownerDocument;if(l&1&&Nn(i.documentElement),l&2&&Nn(i.body),l&4)for(l=i.head,Nn(l),i=l.firstChild;i;){var r=i.nextSibling,d=i.nodeName;i[Va]||d==="SCRIPT"||d==="STYLE"||d==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=r}}if(n===0){t.removeChild(u),qn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);qn(e)}function hr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":hr(l),pi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function wv(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Va])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Oe(t.nextSibling),t===null)break}return null}function Cv(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Oe(t.nextSibling),t===null))return null;return t}function vr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function jv(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Oe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var yr=null;function yd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function gd(t,e,l){switch(e=Zu(l),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Nn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);pi(t)}var Ee=new Map,bd=new Set;function ku(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Fe=Y.d;Y.d={f:Hv,r:Bv,D:qv,C:Yv,L:Gv,m:Xv,X:Qv,S:Lv,M:Vv};function Hv(){var t=Fe.f(),e=Bu();return t||e}function Bv(t){var e=ta(t);e!==null&&e.tag===5&&e.type==="form"?qf(e):Fe.r(t)}var Ca=typeof document>"u"?null:document;function pd(t,e,l){var a=Ca;if(a&&typeof e=="string"&&e){var n=ge(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),bd.has(n)||(bd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),Wt(e,"link",t),Qt(e),a.head.appendChild(e)))}}function qv(t){Fe.D(t),pd("dns-prefetch",t,null)}function Yv(t,e){Fe.C(t,e),pd("preconnect",t,e)}function Gv(t,e,l){Fe.L(t,e,l);var a=Ca;if(a&&t&&e){var n='link[rel="preload"][as="'+ge(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+ge(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+ge(l.imageSizes)+'"]')):n+='[href="'+ge(t)+'"]';var u=n;switch(e){case"style":u=ja(t);break;case"script":u=Ha(t)}Ee.has(u)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Ee.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Dn(u))||e==="script"&&a.querySelector(Un(u))||(e=a.createElement("link"),Wt(e,"link",t),Qt(e),a.head.appendChild(e)))}}function Xv(t,e){Fe.m(t,e);var l=Ca;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+ge(a)+'"][href="'+ge(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ha(t)}if(!Ee.has(u)&&(t=z({rel:"modulepreload",href:t},e),Ee.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Un(u)))return}a=l.createElement("link"),Wt(a,"link",t),Qt(a),l.head.appendChild(a)}}}function Lv(t,e,l){Fe.S(t,e,l);var a=Ca;if(a&&t){var n=ea(a).hoistableStyles,u=ja(t);e=e||"default";var i=n.get(u);if(!i){var r={loading:0,preload:null};if(i=a.querySelector(Dn(u)))r.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Ee.get(u))&&gr(t,l);var d=i=a.createElement("link");Qt(d),Wt(d,"link",t),d._p=new Promise(function(x,R){d.onload=x,d.onerror=R}),d.addEventListener("load",function(){r.loading|=1}),d.addEventListener("error",function(){r.loading|=2}),r.loading|=4,Ku(i,e,a)}i={type:"stylesheet",instance:i,count:1,state:r},n.set(u,i)}}}function Qv(t,e){Fe.X(t,e);var l=Ca;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(Un(n)),u||(t=z({src:t,async:!0},e),(e=Ee.get(n))&&br(t,e),u=l.createElement("script"),Qt(u),Wt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Vv(t,e){Fe.M(t,e);var l=Ca;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(Un(n)),u||(t=z({src:t,async:!0,type:"module"},e),(e=Ee.get(n))&&br(t,e),u=l.createElement("script"),Qt(u),Wt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Sd(t,e,l,a){var n=(n=tt.current)?ku(n):null;if(!n)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=ja(l.href),l=ea(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=ja(l.href);var u=ea(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(Dn(t)))&&!u._p&&(i.instance=u,i.state.loading=5),Ee.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ee.set(t,l),u||Zv(n,t,l,i.state))),e&&a===null)throw Error(o(528,""));return i}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ha(l),l=ea(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function ja(t){return'href="'+ge(t)+'"'}function Dn(t){return'link[rel="stylesheet"]['+t+"]"}function xd(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function Zv(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Wt(e,"link",l),Qt(e),t.head.appendChild(e))}function Ha(t){return'[src="'+ge(t)+'"]'}function Un(t){return"script[async]"+t}function Ad(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+ge(l.href)+'"]');if(a)return e.instance=a,Qt(a),a;var n=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Qt(a),Wt(a,"style",n),Ku(a,l.precedence,t),e.instance=a;case"stylesheet":n=ja(l.href);var u=t.querySelector(Dn(n));if(u)return e.state.loading|=4,e.instance=u,Qt(u),u;a=xd(l),(n=Ee.get(n))&&gr(a,n),u=(t.ownerDocument||t).createElement("link"),Qt(u);var i=u;return i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),Wt(u,"link",a),e.state.loading|=4,Ku(u,l.precedence,t),e.instance=u;case"script":return u=Ha(l.src),(n=t.querySelector(Un(u)))?(e.instance=n,Qt(n),n):(a=l,(n=Ee.get(u))&&(a=z({},l),br(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Qt(n),Wt(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ku(a,l.precedence,t));return e.instance}function Ku(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var r=a[i];if(r.dataset.precedence===e)u=r;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function gr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function br(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ju=null;function Td(t,e,l){if(Ju===null){var a=new Map,n=Ju=new Map;n.set(l,a)}else n=Ju,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[Va]||u[$t]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(e)||"";i=t+i;var r=a.get(i);r?r.push(u):a.set(i,[u])}}return a}function Ed(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function kv(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function zd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var wn=null;function Kv(){}function Jv(t,e,l){if(wn===null)throw Error(o(475));var a=wn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=ja(l.href),u=t.querySelector(Dn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Wu.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,Qt(u);return}u=t.ownerDocument||t,l=xd(l),(n=Ee.get(n))&&gr(l,n),u=u.createElement("link"),Qt(u);var i=u;i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),Wt(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Wu.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function Wv(){if(wn===null)throw Error(o(475));var t=wn;return t.stylesheets&&t.count===0&&pr(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&pr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Wu(){if(this.count--,this.count===0){if(this.stylesheets)pr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var $u=null;function pr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,$u=new Map,e.forEach($v,t),$u=null,Wu.call(t))}function $v(t,e){if(!(e.state.loading&4)){var l=$u.get(t);if(l)var a=l.get(null);else{l=new Map,$u.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=e.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=Wu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Cn={$$typeof:pt,Provider:null,Consumer:null,_currentValue:j,_currentValue2:j,_threadCount:0};function Fv(t,e,l,a,n,u,i,r){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=vi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vi(0),this.hiddenUpdates=vi(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=r,this.incompleteTransitions=new Map}function _d(t,e,l,a,n,u,i,r,d,x,R,D){return t=new Fv(t,e,l,i,r,d,x,D),e=1,u===!0&&(e|=24),u=oe(3,null,null,e),t.current=u,u.stateNode=t,e=tc(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},nc(u),t}function Md(t){return t?(t=ma,t):ma}function Rd(t,e,l,a,n,u){n=Md(n),a.context===null?a.context=n:a.pendingContext=n,a=rl(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=ol(t,a,e),l!==null&&(he(l,t,e),fn(l,t,e))}function Od(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function Sr(t,e){Od(t,e),(t=t.alternate)&&Od(t,e)}function Nd(t){if(t.tag===13){var e=da(t,67108864);e!==null&&he(e,t,67108864),Sr(t,67108864)}}var Fu=!0;function Pv(t,e,l,a){var n=_.T;_.T=null;var u=Y.p;try{Y.p=2,xr(t,e,l,a)}finally{Y.p=u,_.T=n}}function Iv(t,e,l,a){var n=_.T;_.T=null;var u=Y.p;try{Y.p=8,xr(t,e,l,a)}finally{Y.p=u,_.T=n}}function xr(t,e,l,a){if(Fu){var n=Ar(a);if(n===null)rr(t,e,a,Pu,l),Ud(t,a);else if(e0(n,t,e,l,a))a.stopPropagation();else if(Ud(t,a),e&4&&-1<t0.indexOf(t)){for(;n!==null;){var u=ta(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=Rl(u.pendingLanes);if(i!==0){var r=u;for(r.pendingLanes|=2,r.entangledLanes|=2;i;){var d=1<<31-ce(i);r.entanglements[1]|=d,i&=~d}je(u),(xt&6)===0&&(ju=Ne()+500,Mn(0))}}break;case 13:r=da(u,2),r!==null&&he(r,u,2),Bu(),Sr(u,2)}if(u=Ar(a),u===null&&rr(t,e,a,Pu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else rr(t,e,a,null,l)}}function Ar(t){return t=Mi(t),Tr(t)}var Pu=null;function Tr(t){if(Pu=null,t=Il(t),t!==null){var e=b(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=E(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Pu=t,null}function Dd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ym()){case Qr:return 2;case Vr:return 8;case Vn:case Gm:return 32;case Zr:return 268435456;default:return 32}default:return 32}}var Er=!1,Al=null,Tl=null,El=null,jn=new Map,Hn=new Map,zl=[],t0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Ud(t,e){switch(t){case"focusin":case"focusout":Al=null;break;case"dragenter":case"dragleave":Tl=null;break;case"mouseover":case"mouseout":El=null;break;case"pointerover":case"pointerout":jn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hn.delete(e.pointerId)}}function Bn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=ta(e),e!==null&&Nd(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function e0(t,e,l,a,n){switch(e){case"focusin":return Al=Bn(Al,t,e,l,a,n),!0;case"dragenter":return Tl=Bn(Tl,t,e,l,a,n),!0;case"mouseover":return El=Bn(El,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return jn.set(u,Bn(jn.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Hn.set(u,Bn(Hn.get(u)||null,t,e,l,a,n)),!0}return!1}function wd(t){var e=Il(t.target);if(e!==null){var l=b(e);if(l!==null){if(e=l.tag,e===13){if(e=E(l),e!==null){t.blockedOn=e,Jm(t.priority,function(){if(l.tag===13){var a=me();a=yi(a);var n=da(l,a);n!==null&&he(n,l,a),Sr(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Iu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Ar(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);_i=a,l.target.dispatchEvent(a),_i=null}else return e=ta(l),e!==null&&Nd(e),t.blockedOn=l,!1;e.shift()}return!0}function Cd(t,e,l){Iu(t)&&l.delete(e)}function l0(){Er=!1,Al!==null&&Iu(Al)&&(Al=null),Tl!==null&&Iu(Tl)&&(Tl=null),El!==null&&Iu(El)&&(El=null),jn.forEach(Cd),Hn.forEach(Cd)}function ti(t,e){t.blockedOn===e&&(t.blockedOn=null,Er||(Er=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,l0)))}var ei=null;function jd(t){ei!==t&&(ei=t,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){ei===t&&(ei=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(Tr(a||l)===null)continue;break}var u=ta(l);u!==null&&(t.splice(e,3),e-=3,Tc(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function qn(t){function e(d){return ti(d,t)}Al!==null&&ti(Al,t),Tl!==null&&ti(Tl,t),El!==null&&ti(El,t),jn.forEach(e),Hn.forEach(e);for(var l=0;l<zl.length;l++){var a=zl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<zl.length&&(l=zl[0],l.blockedOn===null);)wd(l),l.blockedOn===null&&zl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[te]||null;if(typeof u=="function")i||jd(l);else if(i){var r=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[te]||null)r=i.formAction;else if(Tr(n)!==null)continue}else r=i.action;typeof r=="function"?l[a+1]=r:(l.splice(a,3),a-=3),jd(l)}}}function zr(t){this._internalRoot=t}li.prototype.render=zr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var l=e.current,a=me();Rd(l,a,t,e,null,null)},li.prototype.unmount=zr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Rd(t.current,2,null,t,null,null),Bu(),e[Pl]=null}};function li(t){this._internalRoot=t}li.prototype.unstable_scheduleHydration=function(t){if(t){var e=$r();t={blockedOn:null,target:t,priority:e};for(var l=0;l<zl.length&&e!==0&&e<zl[l].priority;l++);zl.splice(l,0,t),l===0&&wd(t)}};var Hd=f.version;if(Hd!=="19.1.0")throw Error(o(527,Hd,"19.1.0"));Y.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=p(e),t=t!==null?y(t):null,t=t===null?null:t.stateNode,t};var a0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ai=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ai.isDisabled&&ai.supportsFiber)try{Xa=ai.inject(a0),ie=ai}catch{}}return Gn.createRoot=function(t,e){if(!m(t))throw Error(o(299));var l=!1,a="",n=Pf,u=If,i=ts,r=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(i=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(r=e.unstable_transitionCallbacks)),e=_d(t,1,!1,null,null,l,a,n,u,i,r,null),t[Pl]=e.current,cr(t),new zr(e)},Gn.hydrateRoot=function(t,e,l){if(!m(t))throw Error(o(299));var a=!1,n="",u=Pf,i=If,r=ts,d=null,x=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(r=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(d=l.unstable_transitionCallbacks),l.formState!==void 0&&(x=l.formState)),e=_d(t,1,!0,e,l??null,a,n,u,i,r,d,x),e.context=Md(null),l=e.current,a=me(),a=yi(a),n=rl(a),n.callback=null,ol(l,n,a),l=a,e.current.lanes=l,Qa(e,l),je(e),t[Pl]=e.current,cr(t),new li(e)},Gn.version="19.1.0",Gn}var kd;function d0(){if(kd)return Rr.exports;kd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(f){console.error(f)}}return c(),Rr.exports=s0(),Rr.exports}var m0=d0();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h0=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),v0=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(f,s,o)=>o?o.toUpperCase():s.toLowerCase()),Kd=c=>{const f=v0(c);return f.charAt(0).toUpperCase()+f.slice(1)},cm=(...c)=>c.filter((f,s,o)=>!!f&&f.trim()!==""&&o.indexOf(f)===s).join(" ").trim(),y0=c=>{for(const f in c)if(f.startsWith("aria-")||f==="role"||f==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var g0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b0=w.forwardRef(({color:c="currentColor",size:f=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:m="",children:b,iconNode:E,...M},p)=>w.createElement("svg",{ref:p,...g0,width:f,height:f,stroke:c,strokeWidth:o?Number(s)*24/Number(f):s,className:cm("lucide",m),...!b&&!y0(M)&&{"aria-hidden":"true"},...M},[...E.map(([y,z])=>w.createElement(y,z)),...Array.isArray(b)?b:[b]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=(c,f)=>{const s=w.forwardRef(({className:o,...m},b)=>w.createElement(b0,{ref:b,iconNode:f,className:cm(`lucide-${h0(Kd(c))}`,`lucide-${c}`,o),...m}));return s.displayName=Kd(c),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],Jd=$l("book",p0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S0=[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]],x0=$l("corner-down-left",S0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A0=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],T0=$l("globe",A0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E0=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],z0=$l("message-square",E0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],M0=$l("search",_0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R0=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],O0=$l("tag",R0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N0=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],D0=$l("user",N0);function Wd(c,f){if(typeof c=="function")return c(f);c!=null&&(c.current=f)}function rm(...c){return f=>{let s=!1;const o=c.map(m=>{const b=Wd(m,f);return!s&&typeof b=="function"&&(s=!0),b});if(s)return()=>{for(let m=0;m<o.length;m++){const b=o[m];typeof b=="function"?b():Wd(c[m],null)}}}}function Fl(...c){return w.useCallback(rm(...c),c)}function om(c){const f=U0(c),s=w.forwardRef((o,m)=>{const{children:b,...E}=o,M=w.Children.toArray(b),p=M.find(C0);if(p){const y=p.props.children,z=M.map(C=>C===p?w.Children.count(y)>1?w.Children.only(null):w.isValidElement(y)?y.props.children:null:C);return O.jsx(f,{...E,ref:m,children:w.isValidElement(y)?w.cloneElement(y,void 0,z):null})}return O.jsx(f,{...E,ref:m,children:b})});return s.displayName=`${c}.Slot`,s}var fm=om("Slot");function U0(c){const f=w.forwardRef((s,o)=>{const{children:m,...b}=s;if(w.isValidElement(m)){const E=H0(m),M=j0(b,m.props);return m.type!==w.Fragment&&(M.ref=o?rm(o,E):E),w.cloneElement(m,M)}return w.Children.count(m)>1?w.Children.only(null):null});return f.displayName=`${c}.SlotClone`,f}var w0=Symbol("radix.slottable");function C0(c){return w.isValidElement(c)&&typeof c.type=="function"&&"__radixId"in c.type&&c.type.__radixId===w0}function j0(c,f){const s={...f};for(const o in f){const m=c[o],b=f[o];/^on[A-Z]/.test(o)?m&&b?s[o]=(...M)=>{const p=b(...M);return m(...M),p}:m&&(s[o]=m):o==="style"?s[o]={...m,...b}:o==="className"&&(s[o]=[m,b].filter(Boolean).join(" "))}return{...c,...s}}function H0(c){var o,m;let f=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,s=f&&"isReactWarning"in f&&f.isReactWarning;return s?c.ref:(f=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,s=f&&"isReactWarning"in f&&f.isReactWarning,s?c.props.ref:c.props.ref||c.ref)}function sm(c){var f,s,o="";if(typeof c=="string"||typeof c=="number")o+=c;else if(typeof c=="object")if(Array.isArray(c)){var m=c.length;for(f=0;f<m;f++)c[f]&&(s=sm(c[f]))&&(o&&(o+=" "),o+=s)}else for(s in c)c[s]&&(o&&(o+=" "),o+=s);return o}function dm(){for(var c,f,s=0,o="",m=arguments.length;s<m;s++)(c=arguments[s])&&(f=sm(c))&&(o&&(o+=" "),o+=f);return o}const $d=c=>typeof c=="boolean"?`${c}`:c===0?"0":c,Fd=dm,mm=(c,f)=>s=>{var o;if((f==null?void 0:f.variants)==null)return Fd(c,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:m,defaultVariants:b}=f,E=Object.keys(m).map(y=>{const z=s==null?void 0:s[y],C=b==null?void 0:b[y];if(z===null)return null;const H=$d(z)||$d(C);return m[y][H]}),M=s&&Object.entries(s).reduce((y,z)=>{let[C,H]=z;return H===void 0||(y[C]=H),y},{}),p=f==null||(o=f.compoundVariants)===null||o===void 0?void 0:o.reduce((y,z)=>{let{class:C,className:H,...K}=z;return Object.entries(K).every(W=>{let[et,$]=W;return Array.isArray($)?$.includes({...b,...M}[et]):{...b,...M}[et]===$})?[...y,C,H]:y},[]);return Fd(c,E,p,s==null?void 0:s.class,s==null?void 0:s.className)},Yr="-",B0=c=>{const f=Y0(c),{conflictingClassGroups:s,conflictingClassGroupModifiers:o}=c;return{getClassGroupId:E=>{const M=E.split(Yr);return M[0]===""&&M.length!==1&&M.shift(),hm(M,f)||q0(E)},getConflictingClassGroupIds:(E,M)=>{const p=s[E]||[];return M&&o[E]?[...p,...o[E]]:p}}},hm=(c,f)=>{var E;if(c.length===0)return f.classGroupId;const s=c[0],o=f.nextPart.get(s),m=o?hm(c.slice(1),o):void 0;if(m)return m;if(f.validators.length===0)return;const b=c.join(Yr);return(E=f.validators.find(({validator:M})=>M(b)))==null?void 0:E.classGroupId},Pd=/^\[(.+)\]$/,q0=c=>{if(Pd.test(c)){const f=Pd.exec(c)[1],s=f==null?void 0:f.substring(0,f.indexOf(":"));if(s)return"arbitrary.."+s}},Y0=c=>{const{theme:f,classGroups:s}=c,o={nextPart:new Map,validators:[]};for(const m in s)Cr(s[m],o,m,f);return o},Cr=(c,f,s,o)=>{c.forEach(m=>{if(typeof m=="string"){const b=m===""?f:Id(f,m);b.classGroupId=s;return}if(typeof m=="function"){if(G0(m)){Cr(m(o),f,s,o);return}f.validators.push({validator:m,classGroupId:s});return}Object.entries(m).forEach(([b,E])=>{Cr(E,Id(f,b),s,o)})})},Id=(c,f)=>{let s=c;return f.split(Yr).forEach(o=>{s.nextPart.has(o)||s.nextPart.set(o,{nextPart:new Map,validators:[]}),s=s.nextPart.get(o)}),s},G0=c=>c.isThemeGetter,X0=c=>{if(c<1)return{get:()=>{},set:()=>{}};let f=0,s=new Map,o=new Map;const m=(b,E)=>{s.set(b,E),f++,f>c&&(f=0,o=s,s=new Map)};return{get(b){let E=s.get(b);if(E!==void 0)return E;if((E=o.get(b))!==void 0)return m(b,E),E},set(b,E){s.has(b)?s.set(b,E):m(b,E)}}},jr="!",Hr=":",L0=Hr.length,Q0=c=>{const{prefix:f,experimentalParseClassName:s}=c;let o=m=>{const b=[];let E=0,M=0,p=0,y;for(let W=0;W<m.length;W++){let et=m[W];if(E===0&&M===0){if(et===Hr){b.push(m.slice(p,W)),p=W+L0;continue}if(et==="/"){y=W;continue}}et==="["?E++:et==="]"?E--:et==="("?M++:et===")"&&M--}const z=b.length===0?m:m.substring(p),C=V0(z),H=C!==z,K=y&&y>p?y-p:void 0;return{modifiers:b,hasImportantModifier:H,baseClassName:C,maybePostfixModifierPosition:K}};if(f){const m=f+Hr,b=o;o=E=>E.startsWith(m)?b(E.substring(m.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:E,maybePostfixModifierPosition:void 0}}if(s){const m=o;o=b=>s({className:b,parseClassName:m})}return o},V0=c=>c.endsWith(jr)?c.substring(0,c.length-1):c.startsWith(jr)?c.substring(1):c,Z0=c=>{const f=Object.fromEntries(c.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const m=[];let b=[];return o.forEach(E=>{E[0]==="["||f[E]?(m.push(...b.sort(),E),b=[]):b.push(E)}),m.push(...b.sort()),m}},k0=c=>({cache:X0(c.cacheSize),parseClassName:Q0(c),sortModifiers:Z0(c),...B0(c)}),K0=/\s+/,J0=(c,f)=>{const{parseClassName:s,getClassGroupId:o,getConflictingClassGroupIds:m,sortModifiers:b}=f,E=[],M=c.trim().split(K0);let p="";for(let y=M.length-1;y>=0;y-=1){const z=M[y],{isExternal:C,modifiers:H,hasImportantModifier:K,baseClassName:W,maybePostfixModifierPosition:et}=s(z);if(C){p=z+(p.length>0?" "+p:p);continue}let $=!!et,bt=o($?W.substring(0,et):W);if(!bt){if(!$){p=z+(p.length>0?" "+p:p);continue}if(bt=o(W),!bt){p=z+(p.length>0?" "+p:p);continue}$=!1}const jt=b(H).join(":"),pt=K?jt+jr:jt,Ot=pt+bt;if(E.includes(Ot))continue;E.push(Ot);const lt=m(bt,$);for(let St=0;St<lt.length;++St){const X=lt[St];E.push(pt+X)}p=z+(p.length>0?" "+p:p)}return p};function W0(){let c=0,f,s,o="";for(;c<arguments.length;)(f=arguments[c++])&&(s=vm(f))&&(o&&(o+=" "),o+=s);return o}const vm=c=>{if(typeof c=="string")return c;let f,s="";for(let o=0;o<c.length;o++)c[o]&&(f=vm(c[o]))&&(s&&(s+=" "),s+=f);return s};function $0(c,...f){let s,o,m,b=E;function E(p){const y=f.reduce((z,C)=>C(z),c());return s=k0(y),o=s.cache.get,m=s.cache.set,b=M,M(p)}function M(p){const y=o(p);if(y)return y;const z=J0(p,s);return m(p,z),z}return function(){return b(W0.apply(null,arguments))}}const Lt=c=>{const f=s=>s[c]||[];return f.isThemeGetter=!0,f},ym=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,gm=/^\((?:(\w[\w-]*):)?(.+)\)$/i,F0=/^\d+\/\d+$/,P0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,I0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ty=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,ey=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ly=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ba=c=>F0.test(c),ut=c=>!!c&&!Number.isNaN(Number(c)),Ml=c=>!!c&&Number.isInteger(Number(c)),Ur=c=>c.endsWith("%")&&ut(c.slice(0,-1)),Pe=c=>P0.test(c),ay=()=>!0,ny=c=>I0.test(c)&&!ty.test(c),bm=()=>!1,uy=c=>ey.test(c),iy=c=>ly.test(c),cy=c=>!Z(c)&&!k(c),ry=c=>Ya(c,xm,bm),Z=c=>ym.test(c),kl=c=>Ya(c,Am,ny),wr=c=>Ya(c,my,ut),tm=c=>Ya(c,pm,bm),oy=c=>Ya(c,Sm,iy),ni=c=>Ya(c,Tm,uy),k=c=>gm.test(c),Xn=c=>Ga(c,Am),fy=c=>Ga(c,hy),em=c=>Ga(c,pm),sy=c=>Ga(c,xm),dy=c=>Ga(c,Sm),ui=c=>Ga(c,Tm,!0),Ya=(c,f,s)=>{const o=ym.exec(c);return o?o[1]?f(o[1]):s(o[2]):!1},Ga=(c,f,s=!1)=>{const o=gm.exec(c);return o?o[1]?f(o[1]):s:!1},pm=c=>c==="position"||c==="percentage",Sm=c=>c==="image"||c==="url",xm=c=>c==="length"||c==="size"||c==="bg-size",Am=c=>c==="length",my=c=>c==="number",hy=c=>c==="family-name",Tm=c=>c==="shadow",vy=()=>{const c=Lt("color"),f=Lt("font"),s=Lt("text"),o=Lt("font-weight"),m=Lt("tracking"),b=Lt("leading"),E=Lt("breakpoint"),M=Lt("container"),p=Lt("spacing"),y=Lt("radius"),z=Lt("shadow"),C=Lt("inset-shadow"),H=Lt("text-shadow"),K=Lt("drop-shadow"),W=Lt("blur"),et=Lt("perspective"),$=Lt("aspect"),bt=Lt("ease"),jt=Lt("animate"),pt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Ot=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],lt=()=>[...Ot(),k,Z],St=()=>["auto","hidden","clip","visible","scroll"],X=()=>["auto","contain","none"],q=()=>[k,Z,p],L=()=>[Ba,"full","auto",...q()],ct=()=>[Ml,"none","subgrid",k,Z],mt=()=>["auto",{span:["full",Ml,k,Z]},Ml,k,Z],rt=()=>[Ml,"auto",k,Z],ve=()=>["auto","min","max","fr",k,Z],_e=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Dt=()=>["start","end","center","stretch","center-safe","end-safe"],_=()=>["auto",...q()],Y=()=>[Ba,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],j=()=>[c,k,Z],gt=()=>[...Ot(),em,tm,{position:[k,Z]}],h=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",sy,ry,{size:[k,Z]}],G=()=>[Ur,Xn,kl],B=()=>["","none","full",y,k,Z],Q=()=>["",ut,Xn,kl],ot=()=>["solid","dashed","dotted","double"],tt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ht=()=>[ut,Ur,em,tm],_t=()=>["","none",W,k,Z],ue=()=>["none",ut,k,Z],tl=()=>["none",ut,k,Z],el=()=>[ut,k,Z],ll=()=>[Ba,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Pe],breakpoint:[Pe],color:[ay],container:[Pe],"drop-shadow":[Pe],ease:["in","out","in-out"],font:[cy],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Pe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Pe],shadow:[Pe],spacing:["px",ut],text:[Pe],"text-shadow":[Pe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ba,Z,k,$]}],container:["container"],columns:[{columns:[ut,Z,k,M]}],"break-after":[{"break-after":pt()}],"break-before":[{"break-before":pt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:lt()}],overflow:[{overflow:St()}],"overflow-x":[{"overflow-x":St()}],"overflow-y":[{"overflow-y":St()}],overscroll:[{overscroll:X()}],"overscroll-x":[{"overscroll-x":X()}],"overscroll-y":[{"overscroll-y":X()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:L()}],"inset-x":[{"inset-x":L()}],"inset-y":[{"inset-y":L()}],start:[{start:L()}],end:[{end:L()}],top:[{top:L()}],right:[{right:L()}],bottom:[{bottom:L()}],left:[{left:L()}],visibility:["visible","invisible","collapse"],z:[{z:[Ml,"auto",k,Z]}],basis:[{basis:[Ba,"full","auto",M,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ut,Ba,"auto","initial","none",Z]}],grow:[{grow:["",ut,k,Z]}],shrink:[{shrink:["",ut,k,Z]}],order:[{order:[Ml,"first","last","none",k,Z]}],"grid-cols":[{"grid-cols":ct()}],"col-start-end":[{col:mt()}],"col-start":[{"col-start":rt()}],"col-end":[{"col-end":rt()}],"grid-rows":[{"grid-rows":ct()}],"row-start-end":[{row:mt()}],"row-start":[{"row-start":rt()}],"row-end":[{"row-end":rt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ve()}],"auto-rows":[{"auto-rows":ve()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[..._e(),"normal"]}],"justify-items":[{"justify-items":[...Dt(),"normal"]}],"justify-self":[{"justify-self":["auto",...Dt()]}],"align-content":[{content:["normal",..._e()]}],"align-items":[{items:[...Dt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Dt(),{baseline:["","last"]}]}],"place-content":[{"place-content":_e()}],"place-items":[{"place-items":[...Dt(),"baseline"]}],"place-self":[{"place-self":["auto",...Dt()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:Y()}],w:[{w:[M,"screen",...Y()]}],"min-w":[{"min-w":[M,"screen","none",...Y()]}],"max-w":[{"max-w":[M,"screen","none","prose",{screen:[E]},...Y()]}],h:[{h:["screen","lh",...Y()]}],"min-h":[{"min-h":["screen","lh","none",...Y()]}],"max-h":[{"max-h":["screen","lh",...Y()]}],"font-size":[{text:["base",s,Xn,kl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,k,wr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ur,Z]}],"font-family":[{font:[fy,Z,f]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[m,k,Z]}],"line-clamp":[{"line-clamp":[ut,"none",k,wr]}],leading:[{leading:[b,...q()]}],"list-image":[{"list-image":["none",k,Z]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",k,Z]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:j()}],"text-color":[{text:j()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ot(),"wavy"]}],"text-decoration-thickness":[{decoration:[ut,"from-font","auto",k,kl]}],"text-decoration-color":[{decoration:j()}],"underline-offset":[{"underline-offset":[ut,"auto",k,Z]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",k,Z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",k,Z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:gt()}],"bg-repeat":[{bg:h()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ml,k,Z],radial:["",k,Z],conic:[Ml,k,Z]},dy,oy]}],"bg-color":[{bg:j()}],"gradient-from-pos":[{from:G()}],"gradient-via-pos":[{via:G()}],"gradient-to-pos":[{to:G()}],"gradient-from":[{from:j()}],"gradient-via":[{via:j()}],"gradient-to":[{to:j()}],rounded:[{rounded:B()}],"rounded-s":[{"rounded-s":B()}],"rounded-e":[{"rounded-e":B()}],"rounded-t":[{"rounded-t":B()}],"rounded-r":[{"rounded-r":B()}],"rounded-b":[{"rounded-b":B()}],"rounded-l":[{"rounded-l":B()}],"rounded-ss":[{"rounded-ss":B()}],"rounded-se":[{"rounded-se":B()}],"rounded-ee":[{"rounded-ee":B()}],"rounded-es":[{"rounded-es":B()}],"rounded-tl":[{"rounded-tl":B()}],"rounded-tr":[{"rounded-tr":B()}],"rounded-br":[{"rounded-br":B()}],"rounded-bl":[{"rounded-bl":B()}],"border-w":[{border:Q()}],"border-w-x":[{"border-x":Q()}],"border-w-y":[{"border-y":Q()}],"border-w-s":[{"border-s":Q()}],"border-w-e":[{"border-e":Q()}],"border-w-t":[{"border-t":Q()}],"border-w-r":[{"border-r":Q()}],"border-w-b":[{"border-b":Q()}],"border-w-l":[{"border-l":Q()}],"divide-x":[{"divide-x":Q()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Q()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ot(),"hidden","none"]}],"divide-style":[{divide:[...ot(),"hidden","none"]}],"border-color":[{border:j()}],"border-color-x":[{"border-x":j()}],"border-color-y":[{"border-y":j()}],"border-color-s":[{"border-s":j()}],"border-color-e":[{"border-e":j()}],"border-color-t":[{"border-t":j()}],"border-color-r":[{"border-r":j()}],"border-color-b":[{"border-b":j()}],"border-color-l":[{"border-l":j()}],"divide-color":[{divide:j()}],"outline-style":[{outline:[...ot(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ut,k,Z]}],"outline-w":[{outline:["",ut,Xn,kl]}],"outline-color":[{outline:j()}],shadow:[{shadow:["","none",z,ui,ni]}],"shadow-color":[{shadow:j()}],"inset-shadow":[{"inset-shadow":["none",C,ui,ni]}],"inset-shadow-color":[{"inset-shadow":j()}],"ring-w":[{ring:Q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:j()}],"ring-offset-w":[{"ring-offset":[ut,kl]}],"ring-offset-color":[{"ring-offset":j()}],"inset-ring-w":[{"inset-ring":Q()}],"inset-ring-color":[{"inset-ring":j()}],"text-shadow":[{"text-shadow":["none",H,ui,ni]}],"text-shadow-color":[{"text-shadow":j()}],opacity:[{opacity:[ut,k,Z]}],"mix-blend":[{"mix-blend":[...tt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":tt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ut]}],"mask-image-linear-from-pos":[{"mask-linear-from":ht()}],"mask-image-linear-to-pos":[{"mask-linear-to":ht()}],"mask-image-linear-from-color":[{"mask-linear-from":j()}],"mask-image-linear-to-color":[{"mask-linear-to":j()}],"mask-image-t-from-pos":[{"mask-t-from":ht()}],"mask-image-t-to-pos":[{"mask-t-to":ht()}],"mask-image-t-from-color":[{"mask-t-from":j()}],"mask-image-t-to-color":[{"mask-t-to":j()}],"mask-image-r-from-pos":[{"mask-r-from":ht()}],"mask-image-r-to-pos":[{"mask-r-to":ht()}],"mask-image-r-from-color":[{"mask-r-from":j()}],"mask-image-r-to-color":[{"mask-r-to":j()}],"mask-image-b-from-pos":[{"mask-b-from":ht()}],"mask-image-b-to-pos":[{"mask-b-to":ht()}],"mask-image-b-from-color":[{"mask-b-from":j()}],"mask-image-b-to-color":[{"mask-b-to":j()}],"mask-image-l-from-pos":[{"mask-l-from":ht()}],"mask-image-l-to-pos":[{"mask-l-to":ht()}],"mask-image-l-from-color":[{"mask-l-from":j()}],"mask-image-l-to-color":[{"mask-l-to":j()}],"mask-image-x-from-pos":[{"mask-x-from":ht()}],"mask-image-x-to-pos":[{"mask-x-to":ht()}],"mask-image-x-from-color":[{"mask-x-from":j()}],"mask-image-x-to-color":[{"mask-x-to":j()}],"mask-image-y-from-pos":[{"mask-y-from":ht()}],"mask-image-y-to-pos":[{"mask-y-to":ht()}],"mask-image-y-from-color":[{"mask-y-from":j()}],"mask-image-y-to-color":[{"mask-y-to":j()}],"mask-image-radial":[{"mask-radial":[k,Z]}],"mask-image-radial-from-pos":[{"mask-radial-from":ht()}],"mask-image-radial-to-pos":[{"mask-radial-to":ht()}],"mask-image-radial-from-color":[{"mask-radial-from":j()}],"mask-image-radial-to-color":[{"mask-radial-to":j()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":Ot()}],"mask-image-conic-pos":[{"mask-conic":[ut]}],"mask-image-conic-from-pos":[{"mask-conic-from":ht()}],"mask-image-conic-to-pos":[{"mask-conic-to":ht()}],"mask-image-conic-from-color":[{"mask-conic-from":j()}],"mask-image-conic-to-color":[{"mask-conic-to":j()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:gt()}],"mask-repeat":[{mask:h()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",k,Z]}],filter:[{filter:["","none",k,Z]}],blur:[{blur:_t()}],brightness:[{brightness:[ut,k,Z]}],contrast:[{contrast:[ut,k,Z]}],"drop-shadow":[{"drop-shadow":["","none",K,ui,ni]}],"drop-shadow-color":[{"drop-shadow":j()}],grayscale:[{grayscale:["",ut,k,Z]}],"hue-rotate":[{"hue-rotate":[ut,k,Z]}],invert:[{invert:["",ut,k,Z]}],saturate:[{saturate:[ut,k,Z]}],sepia:[{sepia:["",ut,k,Z]}],"backdrop-filter":[{"backdrop-filter":["","none",k,Z]}],"backdrop-blur":[{"backdrop-blur":_t()}],"backdrop-brightness":[{"backdrop-brightness":[ut,k,Z]}],"backdrop-contrast":[{"backdrop-contrast":[ut,k,Z]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ut,k,Z]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ut,k,Z]}],"backdrop-invert":[{"backdrop-invert":["",ut,k,Z]}],"backdrop-opacity":[{"backdrop-opacity":[ut,k,Z]}],"backdrop-saturate":[{"backdrop-saturate":[ut,k,Z]}],"backdrop-sepia":[{"backdrop-sepia":["",ut,k,Z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",k,Z]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ut,"initial",k,Z]}],ease:[{ease:["linear","initial",bt,k,Z]}],delay:[{delay:[ut,k,Z]}],animate:[{animate:["none",jt,k,Z]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[et,k,Z]}],"perspective-origin":[{"perspective-origin":lt()}],rotate:[{rotate:ue()}],"rotate-x":[{"rotate-x":ue()}],"rotate-y":[{"rotate-y":ue()}],"rotate-z":[{"rotate-z":ue()}],scale:[{scale:tl()}],"scale-x":[{"scale-x":tl()}],"scale-y":[{"scale-y":tl()}],"scale-z":[{"scale-z":tl()}],"scale-3d":["scale-3d"],skew:[{skew:el()}],"skew-x":[{"skew-x":el()}],"skew-y":[{"skew-y":el()}],transform:[{transform:[k,Z,"","none","gpu","cpu"]}],"transform-origin":[{origin:lt()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ll()}],"translate-x":[{"translate-x":ll()}],"translate-y":[{"translate-y":ll()}],"translate-z":[{"translate-z":ll()}],"translate-none":["translate-none"],accent:[{accent:j()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:j()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",k,Z]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",k,Z]}],fill:[{fill:["none",...j()]}],"stroke-w":[{stroke:[ut,Xn,kl,wr]}],stroke:[{stroke:["none",...j()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},yy=$0(vy);function Ie(...c){return yy(dm(c))}const gy=mm("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Kl({className:c,variant:f,size:s,asChild:o=!1,...m}){const b=o?fm:"button";return O.jsx(b,{"data-slot":"button",className:Ie(gy({variant:f,size:s,className:c})),...m})}function lm({className:c,type:f,...s}){return O.jsx("input",{type:f,"data-slot":"input",className:Ie("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",c),...s})}function ii({className:c,...f}){return O.jsx("div",{"data-slot":"card",className:Ie("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",c),...f})}function am({className:c,...f}){return O.jsx("div",{"data-slot":"card-header",className:Ie("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",c),...f})}function nm({className:c,...f}){return O.jsx("div",{"data-slot":"card-title",className:Ie("leading-none font-semibold",c),...f})}function ci({className:c,...f}){return O.jsx("div",{"data-slot":"card-content",className:Ie("px-6",c),...f})}const by=mm("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function py({className:c,variant:f,asChild:s=!1,...o}){const m=s?fm:"span";return O.jsx(m,{"data-slot":"badge",className:Ie(by({variant:f}),c),...o})}im();var Sy=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ln=Sy.reduce((c,f)=>{const s=om(`Primitive.${f}`),o=w.forwardRef((m,b)=>{const{asChild:E,...M}=m,p=E?s:f;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),O.jsx(p,{...M,ref:b})});return o.displayName=`Primitive.${f}`,{...c,[f]:o}},{}),Br=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{};function xy(c,f){return w.useReducer((s,o)=>f[s][o]??s,c)}var Qn=c=>{const{present:f,children:s}=c,o=Ay(f),m=typeof s=="function"?s({present:o.isPresent}):w.Children.only(s),b=Fl(o.ref,Ty(m));return typeof s=="function"||o.isPresent?w.cloneElement(m,{ref:b}):null};Qn.displayName="Presence";function Ay(c){const[f,s]=w.useState(),o=w.useRef(null),m=w.useRef(c),b=w.useRef("none"),E=c?"mounted":"unmounted",[M,p]=xy(E,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const y=ri(o.current);b.current=M==="mounted"?y:"none"},[M]),Br(()=>{const y=o.current,z=m.current;if(z!==c){const H=b.current,K=ri(y);c?p("MOUNT"):K==="none"||(y==null?void 0:y.display)==="none"?p("UNMOUNT"):p(z&&H!==K?"ANIMATION_OUT":"UNMOUNT"),m.current=c}},[c,p]),Br(()=>{if(f){let y;const z=f.ownerDocument.defaultView??window,C=K=>{const et=ri(o.current).includes(K.animationName);if(K.target===f&&et&&(p("ANIMATION_END"),!m.current)){const $=f.style.animationFillMode;f.style.animationFillMode="forwards",y=z.setTimeout(()=>{f.style.animationFillMode==="forwards"&&(f.style.animationFillMode=$)})}},H=K=>{K.target===f&&(b.current=ri(o.current))};return f.addEventListener("animationstart",H),f.addEventListener("animationcancel",C),f.addEventListener("animationend",C),()=>{z.clearTimeout(y),f.removeEventListener("animationstart",H),f.removeEventListener("animationcancel",C),f.removeEventListener("animationend",C)}}else p("ANIMATION_END")},[f,p]),{isPresent:["mounted","unmountSuspended"].includes(M),ref:w.useCallback(y=>{o.current=y?getComputedStyle(y):null,s(y)},[])}}function ri(c){return(c==null?void 0:c.animationName)||"none"}function Ty(c){var o,m;let f=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,s=f&&"isReactWarning"in f&&f.isReactWarning;return s?c.ref:(f=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,s=f&&"isReactWarning"in f&&f.isReactWarning,s?c.props.ref:c.props.ref||c.ref)}function Ey(c,f=[]){let s=[];function o(b,E){const M=w.createContext(E),p=s.length;s=[...s,E];const y=C=>{var bt;const{scope:H,children:K,...W}=C,et=((bt=H==null?void 0:H[c])==null?void 0:bt[p])||M,$=w.useMemo(()=>W,Object.values(W));return O.jsx(et.Provider,{value:$,children:K})};y.displayName=b+"Provider";function z(C,H){var et;const K=((et=H==null?void 0:H[c])==null?void 0:et[p])||M,W=w.useContext(K);if(W)return W;if(E!==void 0)return E;throw new Error(`\`${C}\` must be used within \`${b}\``)}return[y,z]}const m=()=>{const b=s.map(E=>w.createContext(E));return function(M){const p=(M==null?void 0:M[c])||b;return w.useMemo(()=>({[`__scope${c}`]:{...M,[c]:p}}),[M,p])}};return m.scopeName=c,[o,zy(m,...f)]}function zy(...c){const f=c[0];if(c.length===1)return f;const s=()=>{const o=c.map(m=>({useScope:m(),scopeName:m.scopeName}));return function(b){const E=o.reduce((M,{useScope:p,scopeName:y})=>{const C=p(b)[`__scope${y}`];return{...M,...C}},{});return w.useMemo(()=>({[`__scope${f.scopeName}`]:E}),[E])}};return s.scopeName=f.scopeName,s}function Jl(c){const f=w.useRef(c);return w.useEffect(()=>{f.current=c}),w.useMemo(()=>(...s)=>{var o;return(o=f.current)==null?void 0:o.call(f,...s)},[])}var _y=w.createContext(void 0);function My(c){const f=w.useContext(_y);return c||f||"ltr"}function Ry(c,[f,s]){return Math.min(s,Math.max(f,c))}function Wl(c,f,{checkForDefaultPrevented:s=!0}={}){return function(m){if(c==null||c(m),s===!1||!m.defaultPrevented)return f==null?void 0:f(m)}}function Oy(c,f){return w.useReducer((s,o)=>f[s][o]??s,c)}var Gr="ScrollArea",[Em,ky]=Ey(Gr),[Ny,ze]=Em(Gr),zm=w.forwardRef((c,f)=>{const{__scopeScrollArea:s,type:o="hover",dir:m,scrollHideDelay:b=600,...E}=c,[M,p]=w.useState(null),[y,z]=w.useState(null),[C,H]=w.useState(null),[K,W]=w.useState(null),[et,$]=w.useState(null),[bt,jt]=w.useState(0),[pt,Ot]=w.useState(0),[lt,St]=w.useState(!1),[X,q]=w.useState(!1),L=Fl(f,mt=>p(mt)),ct=My(m);return O.jsx(Ny,{scope:s,type:o,dir:ct,scrollHideDelay:b,scrollArea:M,viewport:y,onViewportChange:z,content:C,onContentChange:H,scrollbarX:K,onScrollbarXChange:W,scrollbarXEnabled:lt,onScrollbarXEnabledChange:St,scrollbarY:et,onScrollbarYChange:$,scrollbarYEnabled:X,onScrollbarYEnabledChange:q,onCornerWidthChange:jt,onCornerHeightChange:Ot,children:O.jsx(Ln.div,{dir:ct,...E,ref:L,style:{position:"relative","--radix-scroll-area-corner-width":bt+"px","--radix-scroll-area-corner-height":pt+"px",...c.style}})})});zm.displayName=Gr;var _m="ScrollAreaViewport",Mm=w.forwardRef((c,f)=>{const{__scopeScrollArea:s,children:o,nonce:m,...b}=c,E=ze(_m,s),M=w.useRef(null),p=Fl(f,M,E.onViewportChange);return O.jsxs(O.Fragment,{children:[O.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:m}),O.jsx(Ln.div,{"data-radix-scroll-area-viewport":"",...b,ref:p,style:{overflowX:E.scrollbarXEnabled?"scroll":"hidden",overflowY:E.scrollbarYEnabled?"scroll":"hidden",...c.style},children:O.jsx("div",{ref:E.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});Mm.displayName=_m;var He="ScrollAreaScrollbar",Rm=w.forwardRef((c,f)=>{const{forceMount:s,...o}=c,m=ze(He,c.__scopeScrollArea),{onScrollbarXEnabledChange:b,onScrollbarYEnabledChange:E}=m,M=c.orientation==="horizontal";return w.useEffect(()=>(M?b(!0):E(!0),()=>{M?b(!1):E(!1)}),[M,b,E]),m.type==="hover"?O.jsx(Dy,{...o,ref:f,forceMount:s}):m.type==="scroll"?O.jsx(Uy,{...o,ref:f,forceMount:s}):m.type==="auto"?O.jsx(Om,{...o,ref:f,forceMount:s}):m.type==="always"?O.jsx(Xr,{...o,ref:f}):null});Rm.displayName=He;var Dy=w.forwardRef((c,f)=>{const{forceMount:s,...o}=c,m=ze(He,c.__scopeScrollArea),[b,E]=w.useState(!1);return w.useEffect(()=>{const M=m.scrollArea;let p=0;if(M){const y=()=>{window.clearTimeout(p),E(!0)},z=()=>{p=window.setTimeout(()=>E(!1),m.scrollHideDelay)};return M.addEventListener("pointerenter",y),M.addEventListener("pointerleave",z),()=>{window.clearTimeout(p),M.removeEventListener("pointerenter",y),M.removeEventListener("pointerleave",z)}}},[m.scrollArea,m.scrollHideDelay]),O.jsx(Qn,{present:s||b,children:O.jsx(Om,{"data-state":b?"visible":"hidden",...o,ref:f})})}),Uy=w.forwardRef((c,f)=>{const{forceMount:s,...o}=c,m=ze(He,c.__scopeScrollArea),b=c.orientation==="horizontal",E=di(()=>p("SCROLL_END"),100),[M,p]=Oy("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return w.useEffect(()=>{if(M==="idle"){const y=window.setTimeout(()=>p("HIDE"),m.scrollHideDelay);return()=>window.clearTimeout(y)}},[M,m.scrollHideDelay,p]),w.useEffect(()=>{const y=m.viewport,z=b?"scrollLeft":"scrollTop";if(y){let C=y[z];const H=()=>{const K=y[z];C!==K&&(p("SCROLL"),E()),C=K};return y.addEventListener("scroll",H),()=>y.removeEventListener("scroll",H)}},[m.viewport,b,p,E]),O.jsx(Qn,{present:s||M!=="hidden",children:O.jsx(Xr,{"data-state":M==="hidden"?"hidden":"visible",...o,ref:f,onPointerEnter:Wl(c.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:Wl(c.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),Om=w.forwardRef((c,f)=>{const s=ze(He,c.__scopeScrollArea),{forceMount:o,...m}=c,[b,E]=w.useState(!1),M=c.orientation==="horizontal",p=di(()=>{if(s.viewport){const y=s.viewport.offsetWidth<s.viewport.scrollWidth,z=s.viewport.offsetHeight<s.viewport.scrollHeight;E(M?y:z)}},10);return qa(s.viewport,p),qa(s.content,p),O.jsx(Qn,{present:o||b,children:O.jsx(Xr,{"data-state":b?"visible":"hidden",...m,ref:f})})}),Xr=w.forwardRef((c,f)=>{const{orientation:s="vertical",...o}=c,m=ze(He,c.__scopeScrollArea),b=w.useRef(null),E=w.useRef(0),[M,p]=w.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),y=Cm(M.viewport,M.content),z={...o,sizes:M,onSizesChange:p,hasThumb:y>0&&y<1,onThumbChange:H=>b.current=H,onThumbPointerUp:()=>E.current=0,onThumbPointerDown:H=>E.current=H};function C(H,K){return qy(H,E.current,M,K)}return s==="horizontal"?O.jsx(wy,{...z,ref:f,onThumbPositionChange:()=>{if(m.viewport&&b.current){const H=m.viewport.scrollLeft,K=um(H,M,m.dir);b.current.style.transform=`translate3d(${K}px, 0, 0)`}},onWheelScroll:H=>{m.viewport&&(m.viewport.scrollLeft=H)},onDragScroll:H=>{m.viewport&&(m.viewport.scrollLeft=C(H,m.dir))}}):s==="vertical"?O.jsx(Cy,{...z,ref:f,onThumbPositionChange:()=>{if(m.viewport&&b.current){const H=m.viewport.scrollTop,K=um(H,M);b.current.style.transform=`translate3d(0, ${K}px, 0)`}},onWheelScroll:H=>{m.viewport&&(m.viewport.scrollTop=H)},onDragScroll:H=>{m.viewport&&(m.viewport.scrollTop=C(H))}}):null}),wy=w.forwardRef((c,f)=>{const{sizes:s,onSizesChange:o,...m}=c,b=ze(He,c.__scopeScrollArea),[E,M]=w.useState(),p=w.useRef(null),y=Fl(f,p,b.onScrollbarXChange);return w.useEffect(()=>{p.current&&M(getComputedStyle(p.current))},[p]),O.jsx(Dm,{"data-orientation":"horizontal",...m,ref:y,sizes:s,style:{bottom:0,left:b.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:b.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":si(s)+"px",...c.style},onThumbPointerDown:z=>c.onThumbPointerDown(z.x),onDragScroll:z=>c.onDragScroll(z.x),onWheelScroll:(z,C)=>{if(b.viewport){const H=b.viewport.scrollLeft+z.deltaX;c.onWheelScroll(H),Hm(H,C)&&z.preventDefault()}},onResize:()=>{p.current&&b.viewport&&E&&o({content:b.viewport.scrollWidth,viewport:b.viewport.offsetWidth,scrollbar:{size:p.current.clientWidth,paddingStart:fi(E.paddingLeft),paddingEnd:fi(E.paddingRight)}})}})}),Cy=w.forwardRef((c,f)=>{const{sizes:s,onSizesChange:o,...m}=c,b=ze(He,c.__scopeScrollArea),[E,M]=w.useState(),p=w.useRef(null),y=Fl(f,p,b.onScrollbarYChange);return w.useEffect(()=>{p.current&&M(getComputedStyle(p.current))},[p]),O.jsx(Dm,{"data-orientation":"vertical",...m,ref:y,sizes:s,style:{top:0,right:b.dir==="ltr"?0:void 0,left:b.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":si(s)+"px",...c.style},onThumbPointerDown:z=>c.onThumbPointerDown(z.y),onDragScroll:z=>c.onDragScroll(z.y),onWheelScroll:(z,C)=>{if(b.viewport){const H=b.viewport.scrollTop+z.deltaY;c.onWheelScroll(H),Hm(H,C)&&z.preventDefault()}},onResize:()=>{p.current&&b.viewport&&E&&o({content:b.viewport.scrollHeight,viewport:b.viewport.offsetHeight,scrollbar:{size:p.current.clientHeight,paddingStart:fi(E.paddingTop),paddingEnd:fi(E.paddingBottom)}})}})}),[jy,Nm]=Em(He),Dm=w.forwardRef((c,f)=>{const{__scopeScrollArea:s,sizes:o,hasThumb:m,onThumbChange:b,onThumbPointerUp:E,onThumbPointerDown:M,onThumbPositionChange:p,onDragScroll:y,onWheelScroll:z,onResize:C,...H}=c,K=ze(He,s),[W,et]=w.useState(null),$=Fl(f,L=>et(L)),bt=w.useRef(null),jt=w.useRef(""),pt=K.viewport,Ot=o.content-o.viewport,lt=Jl(z),St=Jl(p),X=di(C,10);function q(L){if(bt.current){const ct=L.clientX-bt.current.left,mt=L.clientY-bt.current.top;y({x:ct,y:mt})}}return w.useEffect(()=>{const L=ct=>{const mt=ct.target;(W==null?void 0:W.contains(mt))&&lt(ct,Ot)};return document.addEventListener("wheel",L,{passive:!1}),()=>document.removeEventListener("wheel",L,{passive:!1})},[pt,W,Ot,lt]),w.useEffect(St,[o,St]),qa(W,X),qa(K.content,X),O.jsx(jy,{scope:s,scrollbar:W,hasThumb:m,onThumbChange:Jl(b),onThumbPointerUp:Jl(E),onThumbPositionChange:St,onThumbPointerDown:Jl(M),children:O.jsx(Ln.div,{...H,ref:$,style:{position:"absolute",...H.style},onPointerDown:Wl(c.onPointerDown,L=>{L.button===0&&(L.target.setPointerCapture(L.pointerId),bt.current=W.getBoundingClientRect(),jt.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",K.viewport&&(K.viewport.style.scrollBehavior="auto"),q(L))}),onPointerMove:Wl(c.onPointerMove,q),onPointerUp:Wl(c.onPointerUp,L=>{const ct=L.target;ct.hasPointerCapture(L.pointerId)&&ct.releasePointerCapture(L.pointerId),document.body.style.webkitUserSelect=jt.current,K.viewport&&(K.viewport.style.scrollBehavior=""),bt.current=null})})})}),oi="ScrollAreaThumb",Um=w.forwardRef((c,f)=>{const{forceMount:s,...o}=c,m=Nm(oi,c.__scopeScrollArea);return O.jsx(Qn,{present:s||m.hasThumb,children:O.jsx(Hy,{ref:f,...o})})}),Hy=w.forwardRef((c,f)=>{const{__scopeScrollArea:s,style:o,...m}=c,b=ze(oi,s),E=Nm(oi,s),{onThumbPositionChange:M}=E,p=Fl(f,C=>E.onThumbChange(C)),y=w.useRef(void 0),z=di(()=>{y.current&&(y.current(),y.current=void 0)},100);return w.useEffect(()=>{const C=b.viewport;if(C){const H=()=>{if(z(),!y.current){const K=Yy(C,M);y.current=K,M()}};return M(),C.addEventListener("scroll",H),()=>C.removeEventListener("scroll",H)}},[b.viewport,z,M]),O.jsx(Ln.div,{"data-state":E.hasThumb?"visible":"hidden",...m,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:Wl(c.onPointerDownCapture,C=>{const K=C.target.getBoundingClientRect(),W=C.clientX-K.left,et=C.clientY-K.top;E.onThumbPointerDown({x:W,y:et})}),onPointerUp:Wl(c.onPointerUp,E.onThumbPointerUp)})});Um.displayName=oi;var Lr="ScrollAreaCorner",wm=w.forwardRef((c,f)=>{const s=ze(Lr,c.__scopeScrollArea),o=!!(s.scrollbarX&&s.scrollbarY);return s.type!=="scroll"&&o?O.jsx(By,{...c,ref:f}):null});wm.displayName=Lr;var By=w.forwardRef((c,f)=>{const{__scopeScrollArea:s,...o}=c,m=ze(Lr,s),[b,E]=w.useState(0),[M,p]=w.useState(0),y=!!(b&&M);return qa(m.scrollbarX,()=>{var C;const z=((C=m.scrollbarX)==null?void 0:C.offsetHeight)||0;m.onCornerHeightChange(z),p(z)}),qa(m.scrollbarY,()=>{var C;const z=((C=m.scrollbarY)==null?void 0:C.offsetWidth)||0;m.onCornerWidthChange(z),E(z)}),y?O.jsx(Ln.div,{...o,ref:f,style:{width:b,height:M,position:"absolute",right:m.dir==="ltr"?0:void 0,left:m.dir==="rtl"?0:void 0,bottom:0,...c.style}}):null});function fi(c){return c?parseInt(c,10):0}function Cm(c,f){const s=c/f;return isNaN(s)?0:s}function si(c){const f=Cm(c.viewport,c.content),s=c.scrollbar.paddingStart+c.scrollbar.paddingEnd,o=(c.scrollbar.size-s)*f;return Math.max(o,18)}function qy(c,f,s,o="ltr"){const m=si(s),b=m/2,E=f||b,M=m-E,p=s.scrollbar.paddingStart+E,y=s.scrollbar.size-s.scrollbar.paddingEnd-M,z=s.content-s.viewport,C=o==="ltr"?[0,z]:[z*-1,0];return jm([p,y],C)(c)}function um(c,f,s="ltr"){const o=si(f),m=f.scrollbar.paddingStart+f.scrollbar.paddingEnd,b=f.scrollbar.size-m,E=f.content-f.viewport,M=b-o,p=s==="ltr"?[0,E]:[E*-1,0],y=Ry(c,p);return jm([0,E],[0,M])(y)}function jm(c,f){return s=>{if(c[0]===c[1]||f[0]===f[1])return f[0];const o=(f[1]-f[0])/(c[1]-c[0]);return f[0]+o*(s-c[0])}}function Hm(c,f){return c>0&&c<f}var Yy=(c,f=()=>{})=>{let s={left:c.scrollLeft,top:c.scrollTop},o=0;return function m(){const b={left:c.scrollLeft,top:c.scrollTop},E=s.left!==b.left,M=s.top!==b.top;(E||M)&&f(),s=b,o=window.requestAnimationFrame(m)}(),()=>window.cancelAnimationFrame(o)};function di(c,f){const s=Jl(c),o=w.useRef(0);return w.useEffect(()=>()=>window.clearTimeout(o.current),[]),w.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(s,f)},[s,f])}function qa(c,f){const s=Jl(f);Br(()=>{let o=0;if(c){const m=new ResizeObserver(()=>{cancelAnimationFrame(o),o=window.requestAnimationFrame(s)});return m.observe(c),()=>{window.cancelAnimationFrame(o),m.unobserve(c)}}},[c,s])}var Gy=zm,Xy=Mm,Ly=wm;function Qy({className:c,children:f,...s}){return O.jsxs(Gy,{"data-slot":"scroll-area",className:Ie("relative",c),...s,children:[O.jsx(Xy,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:f}),O.jsx(Vy,{}),O.jsx(Ly,{})]})}function Vy({className:c,orientation:f="vertical",...s}){return O.jsx(Rm,{"data-slot":"scroll-area-scrollbar",orientation:f,className:Ie("flex touch-none p-px transition-colors select-none",f==="vertical"&&"h-full w-2.5 border-l border-l-transparent",f==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",c),...s,children:O.jsx(Um,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function Zy(){const[c,f]=w.useState(""),[s,o]=w.useState("en"),[m,b]=w.useState([]),[E,M]=w.useState(!1),[p,y]=w.useState(""),[z,C]=w.useState([]),[H,K]=w.useState(""),[W,et]=w.useState(!1),$={en:{title:"Book Information Retrieval System",searchPlaceholder:"Enter book name or ask a question...",searchButton:"Search Books",categories:"Categories",downloadPdf:"Download PDF",publicDomain:"Public Domain - Free to Download",noResults:"No books found. Try a different search term.",error:"An error occurred while searching. Please try again.",loading:"Searching for books...",pdfLinks:"PDF Downloads",convert:"Convert",download:"Download",chatTitle:"AI Assistant",chatPlaceholder:"Ask me anything about books...",sendMessage:"Send",llmLoading:"AI is thinking...",relatedBooks:"Related Books",askAboutBook:"Ask AI about this book",askAboutRelated:"Get related books from AI"},ar:{title:"نظام استرجاع معلومات الكتب",searchPlaceholder:"أدخل اسم الكتاب أو اطرح سؤالاً...",searchButton:"البحث عن الكتب",categories:"التصنيفات",downloadPdf:"تحميل PDF",publicDomain:"ملكية عامة - مجاني للتحميل",noResults:"لم يتم العثور على كتب. جرب مصطلح بحث مختلف.",error:"حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.",loading:"البحث عن الكتب...",pdfLinks:"تحميلات PDF",convert:"تحويل",download:"تحميل",chatTitle:"مساعد الذكاء الاصطناعي",chatPlaceholder:"اسألني أي شيء عن الكتب...",sendMessage:"إرسال",llmLoading:"الذكاء الاصطناعي يفكر...",relatedBooks:"كتب ذات صلة",askAboutBook:"اسأل الذكاء الاصطناعي عن هذا الكتاب",askAboutRelated:"احصل على كتب ذات صلة من الذكاء الاصطناعي"}}[s];w.useEffect(()=>{document.documentElement.dir=s==="ar"?"rtl":"ltr"},[s]);const bt=async()=>{if(c.trim()){M(!0),y(""),b([]);try{const X=await fetch("/api/books/enhanced-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:c,lang:s})});if(!X.ok)throw new Error("Search failed");const q=await X.json();b(q.results||[])}catch(X){y("Failed to search books. Please try again."),console.error("Search error:",X)}finally{M(!1)}}},jt=async(X,q)=>{try{const L=await fetch("/api/books/convert-to-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_url:X,output_filename:`converted_book.${q}.pdf`})});if(L.ok){const ct=await L.blob(),mt=window.URL.createObjectURL(ct),rt=document.createElement("a");rt.href=mt,rt.download=`converted_book.${q}.pdf`,document.body.appendChild(rt),rt.click(),window.URL.revokeObjectURL(mt),document.body.removeChild(rt)}else alert("Conversion failed. Please try again.")}catch(L){console.error("Conversion error:",L),alert("Conversion failed. Please try again.")}},pt=async()=>{if(!H.trim())return;const X=H;C(q=>[...q,{sender:"user",text:X}]),K(""),et(!0);try{const q=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:X})});if(!q.ok)throw new Error("LLM chat failed");const L=await q.json();C(ct=>[...ct,{sender:"ai",text:L.response}])}catch(q){console.error("LLM chat error:",q),C(L=>[...L,{sender:"ai",text:$.error}])}finally{et(!1)}},Ot=async(X,q)=>{et(!0),C(L=>[...L,{sender:"user",text:`${$.askAboutRelated}: ${X} by ${q}`}]);try{const L=await fetch("/api/llm/related-books",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:X,author:q})});if(!L.ok)throw new Error("Related books failed");const ct=await L.json();if(ct.related_books&&ct.related_books.length>0){const mt=ct.related_books.map(rt=>`${rt.title} - ${rt.author}`).join(`
`);C(rt=>[...rt,{sender:"ai",text:`${$.relatedBooks}:
${mt}`}])}else C(mt=>[...mt,{sender:"ai",text:"No related books found."}])}catch(L){console.error("Related books error:",L),C(ct=>[...ct,{sender:"ai",text:$.error}])}finally{et(!1)}},lt=async(X,q,L)=>{const ct=`Tell me about the book: ${X} by ${q}. Here is a brief description: ${L}.`;C(mt=>[...mt,{sender:"user",text:`${$.askAboutBook}: ${X}`}]),et(!0);try{const mt=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:ct})});if(!mt.ok)throw new Error("LLM chat failed");const rt=await mt.json();C(ve=>[...ve,{sender:"ai",text:rt.response}])}catch(mt){console.error("LLM chat error:",mt),C(rt=>[...rt,{sender:"ai",text:$.error}])}finally{et(!1)}},St=()=>{o(s==="en"?"ar":"en")};return O.jsx("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${s==="ar"?"rtl":"ltr"}`,children:O.jsxs("div",{className:"container mx-auto px-4 py-8",children:[O.jsxs("div",{className:"text-center mb-8",children:[O.jsxs("div",{className:"flex justify-between items-center mb-4",children:[O.jsxs("div",{className:"flex items-center gap-2",children:[O.jsx(Jd,{className:"h-8 w-8 text-blue-600"}),O.jsx("span",{className:"text-xl font-bold text-blue-600",children:"BookFinder AI"})]}),O.jsxs(Kl,{variant:"outline",size:"sm",onClick:St,className:"flex items-center gap-2",children:[O.jsx(T0,{className:"h-4 w-4"}),s==="en"?"العربية":"English"]})]}),O.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:$.title}),O.jsx("p",{className:"text-lg text-gray-600",children:$.subtitle})]}),O.jsx(ii,{className:"max-w-2xl mx-auto mb-8",children:O.jsx(ci,{className:"p-6",children:O.jsxs("div",{className:"flex gap-4",children:[O.jsx("div",{className:"flex-1",children:O.jsx(lm,{type:"text",placeholder:$.searchPlaceholder,value:c,onChange:X=>f(X.target.value),onKeyPress:X=>X.key==="Enter"&&bt(),className:"text-lg",dir:s==="ar"?"rtl":"ltr"})}),O.jsxs(Kl,{onClick:bt,disabled:E||!c.trim(),className:"px-6",children:[O.jsx(M0,{className:"h-4 w-4 mr-2"}),$.searchButton]})]})})}),E&&O.jsxs("div",{className:"text-center py-8",children:[O.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),O.jsx("p",{className:"text-gray-600",children:$.loading})]}),p&&O.jsx("div",{className:"max-w-2xl mx-auto mb-8",children:O.jsx(ii,{className:"border-red-200 bg-red-50",children:O.jsx(ci,{className:"p-4",children:O.jsx("p",{className:"text-red-600 text-center",children:p})})})}),m.length>0&&O.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:m.map((X,q)=>O.jsxs(ii,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[O.jsx(am,{className:"pb-4",children:O.jsxs("div",{className:"flex gap-4",children:[O.jsx("img",{src:X.thumbnail||"/api/placeholder/120/180",alt:X.title,className:"w-20 h-30 object-cover rounded",onError:L=>{L.target.src="/api/placeholder/120/180"}}),O.jsxs("div",{className:"flex-1",children:[O.jsx(nm,{className:"text-lg mb-2 line-clamp-2",children:X.title}),O.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-2",children:[O.jsx(D0,{className:"h-4 w-4"}),O.jsx("span",{children:X.author})]}),X.description&&O.jsx("p",{className:"text-xs text-gray-500 line-clamp-3",children:X.description})]})]})}),O.jsx(ci,{children:O.jsxs("div",{className:"space-y-3",children:[O.jsxs("div",{children:[O.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[O.jsx(O0,{className:"h-4 w-4 text-gray-500"}),O.jsx("span",{className:"text-sm font-medium",children:$.categories})]}),O.jsx("div",{className:"flex flex-wrap gap-1",children:X.categories.map((L,ct)=>O.jsx(py,{variant:"secondary",className:"text-xs",children:L},ct))})]}),X.pdf_links&&X.pdf_links.length>0&&O.jsx("div",{className:"pt-2",children:O.jsx("div",{className:"space-y-2",children:X.pdf_links.map((L,ct)=>O.jsxs("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded text-sm",children:[O.jsxs("span",{className:"text-gray-600",children:[L.source," ",L.type==="convertible"&&`(${L.format.toUpperCase()})`]}),L.type==="convertible"?O.jsx(Kl,{onClick:()=>jt(L.url,L.format),size:"sm",variant:"outline",className:"text-xs",children:$.convert}):O.jsx(Kl,{asChild:!0,size:"sm",className:"text-xs",children:O.jsx("a",{href:L.url,target:"_blank",rel:"noopener noreferrer",children:$.download})})]},ct))})}),O.jsxs("div",{className:"flex flex-col gap-2 pt-2",children:[O.jsx(Kl,{variant:"outline",size:"sm",onClick:()=>lt(X.title,X.author,X.description),children:$.askAboutBook}),O.jsx(Kl,{variant:"outline",size:"sm",onClick:()=>Ot(X.title,X.author),children:$.askAboutRelated})]})]})})]},X.id||q))}),!E&&!p&&m.length===0&&c&&O.jsxs("div",{className:"text-center py-8",children:[O.jsx(Jd,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),O.jsx("p",{className:"text-gray-600",children:$.noResults})]}),O.jsxs(ii,{className:"fixed bottom-4 right-4 w-80 h-96 flex flex-col shadow-lg",children:[O.jsxs(am,{className:"flex flex-row items-center justify-between space-y-0 p-4 border-b",children:[O.jsx(nm,{className:"text-lg font-semibold",children:$.chatTitle}),O.jsx(z0,{className:"h-5 w-5 text-gray-500"})]}),O.jsx(ci,{className:"flex-1 p-4 overflow-hidden",children:O.jsx(Qy,{className:"h-full pr-4",children:O.jsxs("div",{className:"space-y-4",children:[z.map((X,q)=>O.jsx("div",{className:`flex ${X.sender==="user"?"justify-end":"justify-start"}`,children:O.jsx("div",{className:`max-w-[70%] p-2 rounded-lg ${X.sender==="user"?"bg-blue-500 text-white":"bg-gray-200 text-gray-800"}`,children:X.text})},q)),W&&O.jsx("div",{className:"flex justify-start",children:O.jsx("div",{className:"max-w-[70%] p-2 rounded-lg bg-gray-200 text-gray-800 animate-pulse",children:$.llmLoading})})]})})}),O.jsxs("div",{className:"p-4 border-t flex items-center gap-2",children:[O.jsx(lm,{placeholder:$.chatPlaceholder,value:H,onChange:X=>K(X.target.value),onKeyPress:X=>X.key==="Enter"&&pt(),className:"flex-1",dir:s==="ar"?"rtl":"ltr"}),O.jsx(Kl,{size:"icon",onClick:pt,disabled:W||!H.trim(),children:O.jsx(x0,{className:"h-4 w-4"})})]})]})]})})}m0.createRoot(document.getElementById("root")).render(O.jsx(w.StrictMode,{children:O.jsx(Zy,{})}));
