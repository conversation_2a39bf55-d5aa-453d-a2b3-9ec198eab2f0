(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const m of document.querySelectorAll('link[rel="modulepreload"]'))o(m);new MutationObserver(m=>{for(const b of m)if(b.type==="childList")for(const _ of b.addedNodes)_.tagName==="LINK"&&_.rel==="modulepreload"&&o(_)}).observe(document,{childList:!0,subtree:!0});function f(m){const b={};return m.integrity&&(b.integrity=m.integrity),m.referrerPolicy&&(b.referrerPolicy=m.referrerPolicy),m.crossOrigin==="use-credentials"?b.credentials="include":m.crossOrigin==="anonymous"?b.credentials="omit":b.credentials="same-origin",b}function o(m){if(m.ep)return;m.ep=!0;const b=f(m);fetch(m.href,b)}})();var Nr={exports:{}},Gn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yd;function r0(){if(Yd)return Gn;Yd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function f(o,m,b){var _=null;if(b!==void 0&&(_=""+b),m.key!==void 0&&(_=""+m.key),"key"in m){b={};for(var M in m)M!=="key"&&(b[M]=m[M])}else b=m;return m=b.ref,{$$typeof:c,type:o,key:_,ref:m!==void 0?m:null,props:b}}return Gn.Fragment=s,Gn.jsx=f,Gn.jsxs=f,Gn}var Gd;function o0(){return Gd||(Gd=1,Nr.exports=r0()),Nr.exports}var p=o0(),Mr={exports:{}},ut={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xd;function s0(){if(Xd)return ut;Xd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),m=Symbol.for("react.profiler"),b=Symbol.for("react.consumer"),_=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),N=Symbol.for("react.lazy"),j=Symbol.iterator;function B(h){return h===null||typeof h!="object"?null:(h=j&&h[j]||h["@@iterator"],typeof h=="function"?h:null)}var Z={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},J=Object.assign,et={};function ot(h,U,Y){this.props=h,this.context=U,this.refs=et,this.updater=Y||Z}ot.prototype.isReactComponent={},ot.prototype.setState=function(h,U){if(typeof h!="object"&&typeof h!="function"&&h!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,h,U,"setState")},ot.prototype.forceUpdate=function(h){this.updater.enqueueForceUpdate(this,h,"forceUpdate")};function St(){}St.prototype=ot.prototype;function Ct(h,U,Y){this.props=h,this.context=U,this.refs=et,this.updater=Y||Z}var yt=Ct.prototype=new St;yt.constructor=Ct,J(yt,ot.prototype),yt.isPureReactComponent=!0;var Mt=Array.isArray,lt={H:null,A:null,T:null,S:null,V:null},$=Object.prototype.hasOwnProperty;function Et(h,U,Y,q,G,st){return Y=st.ref,{$$typeof:c,type:h,key:U,ref:Y!==void 0?Y:null,props:st}}function V(h,U){return Et(h.type,U,void 0,void 0,void 0,h.props)}function nt(h){return typeof h=="object"&&h!==null&&h.$$typeof===c}function Dt(h){var U={"=":"=0",":":"=2"};return"$"+h.replace(/[=:]/g,function(Y){return U[Y]})}var Ht=/\/+/g;function Rt(h,U){return typeof h=="object"&&h!==null&&h.key!=null?Dt(""+h.key):U.toString(36)}function ge(){}function k(h){switch(h.status){case"fulfilled":return h.value;case"rejected":throw h.reason;default:switch(typeof h.status=="string"?h.then(ge,ge):(h.status="pending",h.then(function(U){h.status==="pending"&&(h.status="fulfilled",h.value=U)},function(U){h.status==="pending"&&(h.status="rejected",h.reason=U)})),h.status){case"fulfilled":return h.value;case"rejected":throw h.reason}}throw h}function at(h,U,Y,q,G){var st=typeof h;(st==="undefined"||st==="boolean")&&(h=null);var tt=!1;if(h===null)tt=!0;else switch(st){case"bigint":case"string":case"number":tt=!0;break;case"object":switch(h.$$typeof){case c:case s:tt=!0;break;case N:return tt=h._init,at(tt(h._payload),U,Y,q,G)}}if(tt)return G=G(h),tt=q===""?"."+Rt(h,0):q,Mt(G)?(Y="",tt!=null&&(Y=tt.replace(Ht,"$&/")+"/"),at(G,U,Y,"",function(ie){return ie})):G!=null&&(nt(G)&&(G=V(G,Y+(G.key==null||h&&h.key===G.key?"":(""+G.key).replace(Ht,"$&/")+"/")+tt)),U.push(G)),1;tt=0;var ht=q===""?".":q+":";if(Mt(h))for(var zt=0;zt<h.length;zt++)q=h[zt],st=ht+Rt(q,zt),tt+=at(q,U,Y,st,G);else if(zt=B(h),typeof zt=="function")for(h=zt.call(h),zt=0;!(q=h.next()).done;)q=q.value,st=ht+Rt(q,zt++),tt+=at(q,U,Y,st,G);else if(st==="object"){if(typeof h.then=="function")return at(k(h),U,Y,q,G);throw U=String(h),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(h).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return tt}function x(h,U,Y){if(h==null)return h;var q=[],G=0;return at(h,q,"","",function(st){return U.call(Y,st,G++)}),q}function H(h){if(h._status===-1){var U=h._result;U=U(),U.then(function(Y){(h._status===0||h._status===-1)&&(h._status=1,h._result=Y)},function(Y){(h._status===0||h._status===-1)&&(h._status=2,h._result=Y)}),h._status===-1&&(h._status=0,h._result=U)}if(h._status===1)return h._result.default;throw h._result}var D=typeof reportError=="function"?reportError:function(h){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof h=="object"&&h!==null&&typeof h.message=="string"?String(h.message):String(h),error:h});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",h);return}console.error(h)};function P(){}return ut.Children={map:x,forEach:function(h,U,Y){x(h,function(){U.apply(this,arguments)},Y)},count:function(h){var U=0;return x(h,function(){U++}),U},toArray:function(h){return x(h,function(U){return U})||[]},only:function(h){if(!nt(h))throw Error("React.Children.only expected to receive a single React element child.");return h}},ut.Component=ot,ut.Fragment=f,ut.Profiler=m,ut.PureComponent=Ct,ut.StrictMode=o,ut.Suspense=S,ut.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=lt,ut.__COMPILER_RUNTIME={__proto__:null,c:function(h){return lt.H.useMemoCache(h)}},ut.cache=function(h){return function(){return h.apply(null,arguments)}},ut.cloneElement=function(h,U,Y){if(h==null)throw Error("The argument must be a React element, but you passed "+h+".");var q=J({},h.props),G=h.key,st=void 0;if(U!=null)for(tt in U.ref!==void 0&&(st=void 0),U.key!==void 0&&(G=""+U.key),U)!$.call(U,tt)||tt==="key"||tt==="__self"||tt==="__source"||tt==="ref"&&U.ref===void 0||(q[tt]=U[tt]);var tt=arguments.length-2;if(tt===1)q.children=Y;else if(1<tt){for(var ht=Array(tt),zt=0;zt<tt;zt++)ht[zt]=arguments[zt+2];q.children=ht}return Et(h.type,G,void 0,void 0,st,q)},ut.createContext=function(h){return h={$$typeof:_,_currentValue:h,_currentValue2:h,_threadCount:0,Provider:null,Consumer:null},h.Provider=h,h.Consumer={$$typeof:b,_context:h},h},ut.createElement=function(h,U,Y){var q,G={},st=null;if(U!=null)for(q in U.key!==void 0&&(st=""+U.key),U)$.call(U,q)&&q!=="key"&&q!=="__self"&&q!=="__source"&&(G[q]=U[q]);var tt=arguments.length-2;if(tt===1)G.children=Y;else if(1<tt){for(var ht=Array(tt),zt=0;zt<tt;zt++)ht[zt]=arguments[zt+2];G.children=ht}if(h&&h.defaultProps)for(q in tt=h.defaultProps,tt)G[q]===void 0&&(G[q]=tt[q]);return Et(h,st,void 0,void 0,null,G)},ut.createRef=function(){return{current:null}},ut.forwardRef=function(h){return{$$typeof:M,render:h}},ut.isValidElement=nt,ut.lazy=function(h){return{$$typeof:N,_payload:{_status:-1,_result:h},_init:H}},ut.memo=function(h,U){return{$$typeof:g,type:h,compare:U===void 0?null:U}},ut.startTransition=function(h){var U=lt.T,Y={};lt.T=Y;try{var q=h(),G=lt.S;G!==null&&G(Y,q),typeof q=="object"&&q!==null&&typeof q.then=="function"&&q.then(P,D)}catch(st){D(st)}finally{lt.T=U}},ut.unstable_useCacheRefresh=function(){return lt.H.useCacheRefresh()},ut.use=function(h){return lt.H.use(h)},ut.useActionState=function(h,U,Y){return lt.H.useActionState(h,U,Y)},ut.useCallback=function(h,U){return lt.H.useCallback(h,U)},ut.useContext=function(h){return lt.H.useContext(h)},ut.useDebugValue=function(){},ut.useDeferredValue=function(h,U){return lt.H.useDeferredValue(h,U)},ut.useEffect=function(h,U,Y){var q=lt.H;if(typeof Y=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return q.useEffect(h,U)},ut.useId=function(){return lt.H.useId()},ut.useImperativeHandle=function(h,U,Y){return lt.H.useImperativeHandle(h,U,Y)},ut.useInsertionEffect=function(h,U){return lt.H.useInsertionEffect(h,U)},ut.useLayoutEffect=function(h,U){return lt.H.useLayoutEffect(h,U)},ut.useMemo=function(h,U){return lt.H.useMemo(h,U)},ut.useOptimistic=function(h,U){return lt.H.useOptimistic(h,U)},ut.useReducer=function(h,U,Y){return lt.H.useReducer(h,U,Y)},ut.useRef=function(h){return lt.H.useRef(h)},ut.useState=function(h){return lt.H.useState(h)},ut.useSyncExternalStore=function(h,U,Y){return lt.H.useSyncExternalStore(h,U,Y)},ut.useTransition=function(){return lt.H.useTransition()},ut.version="19.1.0",ut}var Ld;function Gr(){return Ld||(Ld=1,Mr.exports=s0()),Mr.exports}var C=Gr(),Rr={exports:{}},Xn={},Or={exports:{}},Dr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function f0(){return Qd||(Qd=1,function(c){function s(x,H){var D=x.length;x.push(H);t:for(;0<D;){var P=D-1>>>1,h=x[P];if(0<m(h,H))x[P]=H,x[D]=h,D=P;else break t}}function f(x){return x.length===0?null:x[0]}function o(x){if(x.length===0)return null;var H=x[0],D=x.pop();if(D!==H){x[0]=D;t:for(var P=0,h=x.length,U=h>>>1;P<U;){var Y=2*(P+1)-1,q=x[Y],G=Y+1,st=x[G];if(0>m(q,D))G<h&&0>m(st,q)?(x[P]=st,x[G]=D,P=G):(x[P]=q,x[Y]=D,P=Y);else if(G<h&&0>m(st,D))x[P]=st,x[G]=D,P=G;else break t}}return H}function m(x,H){var D=x.sortIndex-H.sortIndex;return D!==0?D:x.id-H.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var b=performance;c.unstable_now=function(){return b.now()}}else{var _=Date,M=_.now();c.unstable_now=function(){return _.now()-M}}var S=[],g=[],N=1,j=null,B=3,Z=!1,J=!1,et=!1,ot=!1,St=typeof setTimeout=="function"?setTimeout:null,Ct=typeof clearTimeout=="function"?clearTimeout:null,yt=typeof setImmediate<"u"?setImmediate:null;function Mt(x){for(var H=f(g);H!==null;){if(H.callback===null)o(g);else if(H.startTime<=x)o(g),H.sortIndex=H.expirationTime,s(S,H);else break;H=f(g)}}function lt(x){if(et=!1,Mt(x),!J)if(f(S)!==null)J=!0,$||($=!0,Rt());else{var H=f(g);H!==null&&at(lt,H.startTime-x)}}var $=!1,Et=-1,V=5,nt=-1;function Dt(){return ot?!0:!(c.unstable_now()-nt<V)}function Ht(){if(ot=!1,$){var x=c.unstable_now();nt=x;var H=!0;try{t:{J=!1,et&&(et=!1,Ct(Et),Et=-1),Z=!0;var D=B;try{e:{for(Mt(x),j=f(S);j!==null&&!(j.expirationTime>x&&Dt());){var P=j.callback;if(typeof P=="function"){j.callback=null,B=j.priorityLevel;var h=P(j.expirationTime<=x);if(x=c.unstable_now(),typeof h=="function"){j.callback=h,Mt(x),H=!0;break e}j===f(S)&&o(S),Mt(x)}else o(S);j=f(S)}if(j!==null)H=!0;else{var U=f(g);U!==null&&at(lt,U.startTime-x),H=!1}}break t}finally{j=null,B=D,Z=!1}H=void 0}}finally{H?Rt():$=!1}}}var Rt;if(typeof yt=="function")Rt=function(){yt(Ht)};else if(typeof MessageChannel<"u"){var ge=new MessageChannel,k=ge.port2;ge.port1.onmessage=Ht,Rt=function(){k.postMessage(null)}}else Rt=function(){St(Ht,0)};function at(x,H){Et=St(function(){x(c.unstable_now())},H)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(x){x.callback=null},c.unstable_forceFrameRate=function(x){0>x||125<x?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<x?Math.floor(1e3/x):5},c.unstable_getCurrentPriorityLevel=function(){return B},c.unstable_next=function(x){switch(B){case 1:case 2:case 3:var H=3;break;default:H=B}var D=B;B=H;try{return x()}finally{B=D}},c.unstable_requestPaint=function(){ot=!0},c.unstable_runWithPriority=function(x,H){switch(x){case 1:case 2:case 3:case 4:case 5:break;default:x=3}var D=B;B=x;try{return H()}finally{B=D}},c.unstable_scheduleCallback=function(x,H,D){var P=c.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?P+D:P):D=P,x){case 1:var h=-1;break;case 2:h=250;break;case 5:h=1073741823;break;case 4:h=1e4;break;default:h=5e3}return h=D+h,x={id:N++,callback:H,priorityLevel:x,startTime:D,expirationTime:h,sortIndex:-1},D>P?(x.sortIndex=D,s(g,x),f(S)===null&&x===f(g)&&(et?(Ct(Et),Et=-1):et=!0,at(lt,D-P))):(x.sortIndex=h,s(S,x),J||Z||(J=!0,$||($=!0,Rt()))),x},c.unstable_shouldYield=Dt,c.unstable_wrapCallback=function(x){var H=B;return function(){var D=B;B=H;try{return x.apply(this,arguments)}finally{B=D}}}}(Dr)),Dr}var Vd;function d0(){return Vd||(Vd=1,Or.exports=f0()),Or.exports}var wr={exports:{}},It={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zd;function m0(){if(Zd)return It;Zd=1;var c=Gr();function s(S){var g="https://react.dev/errors/"+S;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var N=2;N<arguments.length;N++)g+="&args[]="+encodeURIComponent(arguments[N])}return"Minified React error #"+S+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var o={d:{f,r:function(){throw Error(s(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},m=Symbol.for("react.portal");function b(S,g,N){var j=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:m,key:j==null?null:""+j,children:S,containerInfo:g,implementation:N}}var _=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function M(S,g){if(S==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return It.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,It.createPortal=function(S,g){var N=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(s(299));return b(S,g,null,N)},It.flushSync=function(S){var g=_.T,N=o.p;try{if(_.T=null,o.p=2,S)return S()}finally{_.T=g,o.p=N,o.d.f()}},It.preconnect=function(S,g){typeof S=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,o.d.C(S,g))},It.prefetchDNS=function(S){typeof S=="string"&&o.d.D(S)},It.preinit=function(S,g){if(typeof S=="string"&&g&&typeof g.as=="string"){var N=g.as,j=M(N,g.crossOrigin),B=typeof g.integrity=="string"?g.integrity:void 0,Z=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;N==="style"?o.d.S(S,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:j,integrity:B,fetchPriority:Z}):N==="script"&&o.d.X(S,{crossOrigin:j,integrity:B,fetchPriority:Z,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},It.preinitModule=function(S,g){if(typeof S=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var N=M(g.as,g.crossOrigin);o.d.M(S,{crossOrigin:N,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&o.d.M(S)},It.preload=function(S,g){if(typeof S=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var N=g.as,j=M(N,g.crossOrigin);o.d.L(S,N,{crossOrigin:j,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},It.preloadModule=function(S,g){if(typeof S=="string")if(g){var N=M(g.as,g.crossOrigin);o.d.m(S,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:N,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else o.d.m(S)},It.requestFormReset=function(S){o.d.r(S)},It.unstable_batchedUpdates=function(S,g){return S(g)},It.useFormState=function(S,g,N){return _.H.useFormState(S,g,N)},It.useFormStatus=function(){return _.H.useHostTransitionStatus()},It.version="19.1.0",It}var kd;function om(){if(kd)return wr.exports;kd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),wr.exports=m0(),wr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kd;function h0(){if(Kd)return Xn;Kd=1;var c=d0(),s=Gr(),f=om();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function m(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function b(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function _(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function M(t){if(b(t)!==t)throw Error(o(188))}function S(t){var e=t.alternate;if(!e){if(e=b(t),e===null)throw Error(o(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return M(n),t;if(u===a)return M(n),e;u=u.sibling}throw Error(o(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,r=n.child;r;){if(r===l){i=!0,l=n,a=u;break}if(r===a){i=!0,a=n,l=u;break}r=r.sibling}if(!i){for(r=u.child;r;){if(r===l){i=!0,l=u,a=n;break}if(r===a){i=!0,a=u,l=n;break}r=r.sibling}if(!i)throw Error(o(189))}}if(l.alternate!==a)throw Error(o(190))}if(l.tag!==3)throw Error(o(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var N=Object.assign,j=Symbol.for("react.element"),B=Symbol.for("react.transitional.element"),Z=Symbol.for("react.portal"),J=Symbol.for("react.fragment"),et=Symbol.for("react.strict_mode"),ot=Symbol.for("react.profiler"),St=Symbol.for("react.provider"),Ct=Symbol.for("react.consumer"),yt=Symbol.for("react.context"),Mt=Symbol.for("react.forward_ref"),lt=Symbol.for("react.suspense"),$=Symbol.for("react.suspense_list"),Et=Symbol.for("react.memo"),V=Symbol.for("react.lazy"),nt=Symbol.for("react.activity"),Dt=Symbol.for("react.memo_cache_sentinel"),Ht=Symbol.iterator;function Rt(t){return t===null||typeof t!="object"?null:(t=Ht&&t[Ht]||t["@@iterator"],typeof t=="function"?t:null)}var ge=Symbol.for("react.client.reference");function k(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ge?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case J:return"Fragment";case ot:return"Profiler";case et:return"StrictMode";case lt:return"Suspense";case $:return"SuspenseList";case nt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case Z:return"Portal";case yt:return(t.displayName||"Context")+".Provider";case Ct:return(t._context.displayName||"Context")+".Consumer";case Mt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Et:return e=t.displayName||null,e!==null?e:k(t.type)||"Memo";case V:e=t._payload,t=t._init;try{return k(t(e))}catch{}}return null}var at=Array.isArray,x=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},P=[],h=-1;function U(t){return{current:t}}function Y(t){0>h||(t.current=P[h],P[h]=null,h--)}function q(t,e){h++,P[h]=t.current,t.current=e}var G=U(null),st=U(null),tt=U(null),ht=U(null);function zt(t,e){switch(q(tt,e),q(st,t),q(G,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?md(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=md(e),t=hd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Y(G),q(G,t)}function ie(){Y(G),Y(st),Y(tt)}function ll(t){t.memoizedState!==null&&q(ht,t);var e=G.current,l=hd(e,t.type);e!==l&&(q(st,t),q(G,l))}function al(t){st.current===t&&(Y(G),Y(st)),ht.current===t&&(Y(ht),jn._currentValue=D)}var nl=Object.prototype.hasOwnProperty,hi=c.unstable_scheduleCallback,vi=c.unstable_cancelCallback,Gm=c.unstable_shouldYield,Xm=c.unstable_requestPaint,we=c.unstable_now,Lm=c.unstable_getCurrentPriorityLevel,Zr=c.unstable_ImmediatePriority,kr=c.unstable_UserBlockingPriority,Zn=c.unstable_NormalPriority,Qm=c.unstable_LowPriority,Kr=c.unstable_IdlePriority,Vm=c.log,Zm=c.unstable_setDisableYieldValue,La=null,ce=null;function ul(t){if(typeof Vm=="function"&&Zm(t),ce&&typeof ce.setStrictMode=="function")try{ce.setStrictMode(La,t)}catch{}}var re=Math.clz32?Math.clz32:Jm,km=Math.log,Km=Math.LN2;function Jm(t){return t>>>=0,t===0?32:31-(km(t)/Km|0)|0}var kn=256,Kn=4194304;function Ol(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Jn(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var r=a&134217727;return r!==0?(a=r&~u,a!==0?n=Ol(a):(i&=r,i!==0?n=Ol(i):l||(l=r&~t,l!==0&&(n=Ol(l))))):(r=a&~u,r!==0?n=Ol(r):i!==0?n=Ol(i):l||(l=a&~t,l!==0&&(n=Ol(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function Qa(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Wm(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jr(){var t=kn;return kn<<=1,(kn&4194048)===0&&(kn=256),t}function Wr(){var t=Kn;return Kn<<=1,(Kn&62914560)===0&&(Kn=4194304),t}function gi(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Va(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function $m(t,e,l,a,n,u){var i=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var r=t.entanglements,d=t.expirationTimes,T=t.hiddenUpdates;for(l=i&~l;0<l;){var R=31-re(l),w=1<<R;r[R]=0,d[R]=-1;var E=T[R];if(E!==null)for(T[R]=null,R=0;R<E.length;R++){var z=E[R];z!==null&&(z.lane&=-536870913)}l&=~w}a!==0&&$r(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~e))}function $r(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-re(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Fr(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-re(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function yi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function bi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Pr(){var t=H.p;return t!==0?t:(t=window.event,t===void 0?32:Cd(t.type))}function Fm(t,e){var l=H.p;try{return H.p=t,e()}finally{H.p=l}}var il=Math.random().toString(36).slice(2),Ft="__reactFiber$"+il,ee="__reactProps$"+il,Pl="__reactContainer$"+il,pi="__reactEvents$"+il,Pm="__reactListeners$"+il,Im="__reactHandles$"+il,Ir="__reactResources$"+il,Za="__reactMarker$"+il;function Si(t){delete t[Ft],delete t[ee],delete t[pi],delete t[Pm],delete t[Im]}function Il(t){var e=t[Ft];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Pl]||l[Ft]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=bd(t);t!==null;){if(l=t[Ft])return l;t=bd(t)}return e}t=l,l=t.parentNode}return null}function ta(t){if(t=t[Ft]||t[Pl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function ka(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function ea(t){var e=t[Ir];return e||(e=t[Ir]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Vt(t){t[Za]=!0}var to=new Set,eo={};function Dl(t,e){la(t,e),la(t+"Capture",e)}function la(t,e){for(eo[t]=e,t=0;t<e.length;t++)to.add(e[t])}var th=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),lo={},ao={};function eh(t){return nl.call(ao,t)?!0:nl.call(lo,t)?!1:th.test(t)?ao[t]=!0:(lo[t]=!0,!1)}function Wn(t,e,l){if(eh(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function $n(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Ye(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var xi,no;function aa(t){if(xi===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);xi=e&&e[1]||"",no=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+xi+t+no}var Ai=!1;function Ti(t,e){if(!t||Ai)return"";Ai=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var w=function(){throw Error()};if(Object.defineProperty(w.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(w,[])}catch(z){var E=z}Reflect.construct(t,[],w)}else{try{w.call()}catch(z){E=z}t.call(w.prototype)}}else{try{throw Error()}catch(z){E=z}(w=t())&&typeof w.catch=="function"&&w.catch(function(){})}}catch(z){if(z&&E&&typeof z.stack=="string")return[z.stack,E.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],r=u[1];if(i&&r){var d=i.split(`
`),T=r.split(`
`);for(n=a=0;a<d.length&&!d[a].includes("DetermineComponentFrameRoot");)a++;for(;n<T.length&&!T[n].includes("DetermineComponentFrameRoot");)n++;if(a===d.length||n===T.length)for(a=d.length-1,n=T.length-1;1<=a&&0<=n&&d[a]!==T[n];)n--;for(;1<=a&&0<=n;a--,n--)if(d[a]!==T[n]){if(a!==1||n!==1)do if(a--,n--,0>n||d[a]!==T[n]){var R=`
`+d[a].replace(" at new "," at ");return t.displayName&&R.includes("<anonymous>")&&(R=R.replace("<anonymous>",t.displayName)),R}while(1<=a&&0<=n);break}}}finally{Ai=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?aa(l):""}function lh(t){switch(t.tag){case 26:case 27:case 5:return aa(t.type);case 16:return aa("Lazy");case 13:return aa("Suspense");case 19:return aa("SuspenseList");case 0:case 15:return Ti(t.type,!1);case 11:return Ti(t.type.render,!1);case 1:return Ti(t.type,!0);case 31:return aa("Activity");default:return""}}function uo(t){try{var e="";do e+=lh(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function ye(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function io(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function ah(t){var e=io(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Fn(t){t._valueTracker||(t._valueTracker=ah(t))}function co(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=io(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Pn(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var nh=/[\n"\\]/g;function be(t){return t.replace(nh,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ei(t,e,l,a,n,u,i,r){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),e!=null?i==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ye(e)):t.value!==""+ye(e)&&(t.value=""+ye(e)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),e!=null?zi(t,i,ye(e)):l!=null?zi(t,i,ye(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?t.name=""+ye(r):t.removeAttribute("name")}function ro(t,e,l,a,n,u,i,r){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+ye(l):"",e=e!=null?""+ye(e):l,r||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=r?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function zi(t,e,l){e==="number"&&Pn(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function na(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+ye(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function oo(t,e,l){if(e!=null&&(e=""+ye(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+ye(l):""}function so(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(o(92));if(at(a)){if(1<a.length)throw Error(o(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=ye(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function ua(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var uh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function fo(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||uh.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function mo(t,e,l){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&fo(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&fo(t,u,e[u])}function _i(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ih=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ch=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function In(t){return ch.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ni=null;function Mi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ia=null,ca=null;function ho(t){var e=ta(t);if(e&&(t=e.stateNode)){var l=t[ee]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ei(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+be(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[ee]||null;if(!n)throw Error(o(90));Ei(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&co(a)}break t;case"textarea":oo(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&na(t,!!l.multiple,e,!1)}}}var Ri=!1;function vo(t,e,l){if(Ri)return t(e,l);Ri=!0;try{var a=t(e);return a}finally{if(Ri=!1,(ia!==null||ca!==null)&&(qu(),ia&&(e=ia,t=ca,ca=ia=null,ho(e),t)))for(e=0;e<t.length;e++)ho(t[e])}}function Ka(t,e){var l=t.stateNode;if(l===null)return null;var a=l[ee]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(o(231,e,typeof l));return l}var Ge=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Oi=!1;if(Ge)try{var Ja={};Object.defineProperty(Ja,"passive",{get:function(){Oi=!0}}),window.addEventListener("test",Ja,Ja),window.removeEventListener("test",Ja,Ja)}catch{Oi=!1}var cl=null,Di=null,tu=null;function go(){if(tu)return tu;var t,e=Di,l=e.length,a,n="value"in cl?cl.value:cl.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var i=l-t;for(a=1;a<=i&&e[l-a]===n[u-a];a++);return tu=n.slice(t,1<a?1-a:void 0)}function eu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function lu(){return!0}function yo(){return!1}function le(t){function e(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var r in t)t.hasOwnProperty(r)&&(l=t[r],this[r]=l?l(u):u[r]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?lu:yo,this.isPropagationStopped=yo,this}return N(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=lu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=lu)},persist:function(){},isPersistent:lu}),e}var wl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},au=le(wl),Wa=N({},wl,{view:0,detail:0}),rh=le(Wa),wi,Ci,$a,nu=N({},Wa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ji,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==$a&&($a&&t.type==="mousemove"?(wi=t.screenX-$a.screenX,Ci=t.screenY-$a.screenY):Ci=wi=0,$a=t),wi)},movementY:function(t){return"movementY"in t?t.movementY:Ci}}),bo=le(nu),oh=N({},nu,{dataTransfer:0}),sh=le(oh),fh=N({},Wa,{relatedTarget:0}),Ui=le(fh),dh=N({},wl,{animationName:0,elapsedTime:0,pseudoElement:0}),mh=le(dh),hh=N({},wl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),vh=le(hh),gh=N({},wl,{data:0}),po=le(gh),yh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},bh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ph={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sh(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=ph[t])?!!e[t]:!1}function ji(){return Sh}var xh=N({},Wa,{key:function(t){if(t.key){var e=yh[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=eu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?bh[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ji,charCode:function(t){return t.type==="keypress"?eu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?eu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Ah=le(xh),Th=N({},nu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),So=le(Th),Eh=N({},Wa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ji}),zh=le(Eh),_h=N({},wl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Nh=le(_h),Mh=N({},nu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Rh=le(Mh),Oh=N({},wl,{newState:0,oldState:0}),Dh=le(Oh),wh=[9,13,27,32],Hi=Ge&&"CompositionEvent"in window,Fa=null;Ge&&"documentMode"in document&&(Fa=document.documentMode);var Ch=Ge&&"TextEvent"in window&&!Fa,xo=Ge&&(!Hi||Fa&&8<Fa&&11>=Fa),Ao=" ",To=!1;function Eo(t,e){switch(t){case"keyup":return wh.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ra=!1;function Uh(t,e){switch(t){case"compositionend":return zo(e);case"keypress":return e.which!==32?null:(To=!0,Ao);case"textInput":return t=e.data,t===Ao&&To?null:t;default:return null}}function jh(t,e){if(ra)return t==="compositionend"||!Hi&&Eo(t,e)?(t=go(),tu=Di=cl=null,ra=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return xo&&e.locale!=="ko"?null:e.data;default:return null}}var Hh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _o(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Hh[t.type]:e==="textarea"}function No(t,e,l,a){ia?ca?ca.push(a):ca=[a]:ia=a,e=Vu(e,"onChange"),0<e.length&&(l=new au("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Pa=null,Ia=null;function Bh(t){rd(t,0)}function uu(t){var e=ka(t);if(co(e))return t}function Mo(t,e){if(t==="change")return e}var Ro=!1;if(Ge){var Bi;if(Ge){var qi="oninput"in document;if(!qi){var Oo=document.createElement("div");Oo.setAttribute("oninput","return;"),qi=typeof Oo.oninput=="function"}Bi=qi}else Bi=!1;Ro=Bi&&(!document.documentMode||9<document.documentMode)}function Do(){Pa&&(Pa.detachEvent("onpropertychange",wo),Ia=Pa=null)}function wo(t){if(t.propertyName==="value"&&uu(Ia)){var e=[];No(e,Ia,t,Mi(t)),vo(Bh,e)}}function qh(t,e,l){t==="focusin"?(Do(),Pa=e,Ia=l,Pa.attachEvent("onpropertychange",wo)):t==="focusout"&&Do()}function Yh(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return uu(Ia)}function Gh(t,e){if(t==="click")return uu(e)}function Xh(t,e){if(t==="input"||t==="change")return uu(e)}function Lh(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var oe=typeof Object.is=="function"?Object.is:Lh;function tn(t,e){if(oe(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!nl.call(e,n)||!oe(t[n],e[n]))return!1}return!0}function Co(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Uo(t,e){var l=Co(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Co(l)}}function jo(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?jo(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Ho(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Pn(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Pn(t.document)}return e}function Yi(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Qh=Ge&&"documentMode"in document&&11>=document.documentMode,oa=null,Gi=null,en=null,Xi=!1;function Bo(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Xi||oa==null||oa!==Pn(a)||(a=oa,"selectionStart"in a&&Yi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),en&&tn(en,a)||(en=a,a=Vu(Gi,"onSelect"),0<a.length&&(e=new au("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=oa)))}function Cl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var sa={animationend:Cl("Animation","AnimationEnd"),animationiteration:Cl("Animation","AnimationIteration"),animationstart:Cl("Animation","AnimationStart"),transitionrun:Cl("Transition","TransitionRun"),transitionstart:Cl("Transition","TransitionStart"),transitioncancel:Cl("Transition","TransitionCancel"),transitionend:Cl("Transition","TransitionEnd")},Li={},qo={};Ge&&(qo=document.createElement("div").style,"AnimationEvent"in window||(delete sa.animationend.animation,delete sa.animationiteration.animation,delete sa.animationstart.animation),"TransitionEvent"in window||delete sa.transitionend.transition);function Ul(t){if(Li[t])return Li[t];if(!sa[t])return t;var e=sa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in qo)return Li[t]=e[l];return t}var Yo=Ul("animationend"),Go=Ul("animationiteration"),Xo=Ul("animationstart"),Vh=Ul("transitionrun"),Zh=Ul("transitionstart"),kh=Ul("transitioncancel"),Lo=Ul("transitionend"),Qo=new Map,Qi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Qi.push("scrollEnd");function Me(t,e){Qo.set(t,e),Dl(e,[t])}var Vo=new WeakMap;function pe(t,e){if(typeof t=="object"&&t!==null){var l=Vo.get(t);return l!==void 0?l:(e={value:t,source:e,stack:uo(e)},Vo.set(t,e),e)}return{value:t,source:e,stack:uo(e)}}var Se=[],fa=0,Vi=0;function iu(){for(var t=fa,e=Vi=fa=0;e<t;){var l=Se[e];Se[e++]=null;var a=Se[e];Se[e++]=null;var n=Se[e];Se[e++]=null;var u=Se[e];if(Se[e++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&Zo(l,n,u)}}function cu(t,e,l,a){Se[fa++]=t,Se[fa++]=e,Se[fa++]=l,Se[fa++]=a,Vi|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Zi(t,e,l,a){return cu(t,e,l,a),ru(t)}function da(t,e){return cu(t,null,null,e),ru(t)}function Zo(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-re(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function ru(t){if(50<Nn)throw Nn=0,Fc=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ma={};function Kh(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function se(t,e,l,a){return new Kh(t,e,l,a)}function ki(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Xe(t,e){var l=t.alternate;return l===null?(l=se(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function ko(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ou(t,e,l,a,n,u){var i=0;if(a=t,typeof t=="function")ki(t)&&(i=1);else if(typeof t=="string")i=Wv(t,l,G.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case nt:return t=se(31,l,e,n),t.elementType=nt,t.lanes=u,t;case J:return jl(l.children,n,u,e);case et:i=8,n|=24;break;case ot:return t=se(12,l,e,n|2),t.elementType=ot,t.lanes=u,t;case lt:return t=se(13,l,e,n),t.elementType=lt,t.lanes=u,t;case $:return t=se(19,l,e,n),t.elementType=$,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case St:case yt:i=10;break t;case Ct:i=9;break t;case Mt:i=11;break t;case Et:i=14;break t;case V:i=16,a=null;break t}i=29,l=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=se(i,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function jl(t,e,l,a){return t=se(7,t,a,e),t.lanes=l,t}function Ki(t,e,l){return t=se(6,t,null,e),t.lanes=l,t}function Ji(t,e,l){return e=se(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ha=[],va=0,su=null,fu=0,xe=[],Ae=0,Hl=null,Le=1,Qe="";function Bl(t,e){ha[va++]=fu,ha[va++]=su,su=t,fu=e}function Ko(t,e,l){xe[Ae++]=Le,xe[Ae++]=Qe,xe[Ae++]=Hl,Hl=t;var a=Le;t=Qe;var n=32-re(a)-1;a&=~(1<<n),l+=1;var u=32-re(e)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Le=1<<32-re(e)+n|l<<n|a,Qe=u+t}else Le=1<<u|l<<n|a,Qe=t}function Wi(t){t.return!==null&&(Bl(t,1),Ko(t,1,0))}function $i(t){for(;t===su;)su=ha[--va],ha[va]=null,fu=ha[--va],ha[va]=null;for(;t===Hl;)Hl=xe[--Ae],xe[Ae]=null,Qe=xe[--Ae],xe[Ae]=null,Le=xe[--Ae],xe[Ae]=null}var te=null,Ut=null,gt=!1,ql=null,Ce=!1,Fi=Error(o(519));function Yl(t){var e=Error(o(418,""));throw nn(pe(e,t)),Fi}function Jo(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[Ft]=t,e[ee]=a,l){case"dialog":dt("cancel",e),dt("close",e);break;case"iframe":case"object":case"embed":dt("load",e);break;case"video":case"audio":for(l=0;l<Rn.length;l++)dt(Rn[l],e);break;case"source":dt("error",e);break;case"img":case"image":case"link":dt("error",e),dt("load",e);break;case"details":dt("toggle",e);break;case"input":dt("invalid",e),ro(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Fn(e);break;case"select":dt("invalid",e);break;case"textarea":dt("invalid",e),so(e,a.value,a.defaultValue,a.children),Fn(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||dd(e.textContent,l)?(a.popover!=null&&(dt("beforetoggle",e),dt("toggle",e)),a.onScroll!=null&&dt("scroll",e),a.onScrollEnd!=null&&dt("scrollend",e),a.onClick!=null&&(e.onclick=Zu),e=!0):e=!1,e||Yl(t)}function Wo(t){for(te=t.return;te;)switch(te.tag){case 5:case 13:Ce=!1;return;case 27:case 3:Ce=!0;return;default:te=te.return}}function ln(t){if(t!==te)return!1;if(!gt)return Wo(t),gt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||mr(t.type,t.memoizedProps)),l=!l),l&&Ut&&Yl(t),Wo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Ut=Oe(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Ut=null}}else e===27?(e=Ut,Tl(t.type)?(t=yr,yr=null,Ut=t):Ut=e):Ut=te?Oe(t.stateNode.nextSibling):null;return!0}function an(){Ut=te=null,gt=!1}function $o(){var t=ql;return t!==null&&(ue===null?ue=t:ue.push.apply(ue,t),ql=null),t}function nn(t){ql===null?ql=[t]:ql.push(t)}var Pi=U(null),Gl=null,Ve=null;function rl(t,e,l){q(Pi,e._currentValue),e._currentValue=l}function Ze(t){t._currentValue=Pi.current,Y(Pi)}function Ii(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function tc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var r=u;u=n;for(var d=0;d<e.length;d++)if(r.context===e[d]){u.lanes|=l,r=u.alternate,r!==null&&(r.lanes|=l),Ii(u.return,l,t),a||(i=null);break t}u=r.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(o(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),Ii(i,l,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function un(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(o(387));if(i=i.memoizedProps,i!==null){var r=n.type;oe(n.pendingProps.value,i.value)||(t!==null?t.push(r):t=[r])}}else if(n===ht.current){if(i=n.alternate,i===null)throw Error(o(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(jn):t=[jn])}n=n.return}t!==null&&tc(e,t,l,a),e.flags|=262144}function du(t){for(t=t.firstContext;t!==null;){if(!oe(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Xl(t){Gl=t,Ve=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Pt(t){return Fo(Gl,t)}function mu(t,e){return Gl===null&&Xl(t),Fo(t,e)}function Fo(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Ve===null){if(t===null)throw Error(o(308));Ve=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ve=Ve.next=e;return l}var Jh=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},Wh=c.unstable_scheduleCallback,$h=c.unstable_NormalPriority,Xt={$$typeof:yt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ec(){return{controller:new Jh,data:new Map,refCount:0}}function cn(t){t.refCount--,t.refCount===0&&Wh($h,function(){t.controller.abort()})}var rn=null,lc=0,ga=0,ya=null;function Fh(t,e){if(rn===null){var l=rn=[];lc=0,ga=nr(),ya={status:"pending",value:void 0,then:function(a){l.push(a)}}}return lc++,e.then(Po,Po),e}function Po(){if(--lc===0&&rn!==null){ya!==null&&(ya.status="fulfilled");var t=rn;rn=null,ga=0,ya=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Ph(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Io=x.S;x.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Fh(t,e),Io!==null&&Io(t,e)};var Ll=U(null);function ac(){var t=Ll.current;return t!==null?t:Nt.pooledCache}function hu(t,e){e===null?q(Ll,Ll.current):q(Ll,e.pool)}function ts(){var t=ac();return t===null?null:{parent:Xt._currentValue,pool:t}}var on=Error(o(460)),es=Error(o(474)),vu=Error(o(542)),nc={then:function(){}};function ls(t){return t=t.status,t==="fulfilled"||t==="rejected"}function gu(){}function as(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(gu,gu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,us(t),t;default:if(typeof e.status=="string")e.then(gu,gu);else{if(t=Nt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,us(t),t}throw sn=e,on}}var sn=null;function ns(){if(sn===null)throw Error(o(459));var t=sn;return sn=null,t}function us(t){if(t===on||t===vu)throw Error(o(483))}var ol=!1;function uc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ic(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function sl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function fl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(bt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=ru(t),Zo(t,null,l),e}return cu(t,a,e,l),ru(t)}function fn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Fr(t,l)}}function cc(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var rc=!1;function dn(){if(rc){var t=ya;if(t!==null)throw t}}function mn(t,e,l,a){rc=!1;var n=t.updateQueue;ol=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,r=n.shared.pending;if(r!==null){n.shared.pending=null;var d=r,T=d.next;d.next=null,i===null?u=T:i.next=T,i=d;var R=t.alternate;R!==null&&(R=R.updateQueue,r=R.lastBaseUpdate,r!==i&&(r===null?R.firstBaseUpdate=T:r.next=T,R.lastBaseUpdate=d))}if(u!==null){var w=n.baseState;i=0,R=T=d=null,r=u;do{var E=r.lane&-536870913,z=E!==r.lane;if(z?(mt&E)===E:(a&E)===E){E!==0&&E===ga&&(rc=!0),R!==null&&(R=R.next={lane:0,tag:r.tag,payload:r.payload,callback:null,next:null});t:{var I=t,W=r;E=e;var Tt=l;switch(W.tag){case 1:if(I=W.payload,typeof I=="function"){w=I.call(Tt,w,E);break t}w=I;break t;case 3:I.flags=I.flags&-65537|128;case 0:if(I=W.payload,E=typeof I=="function"?I.call(Tt,w,E):I,E==null)break t;w=N({},w,E);break t;case 2:ol=!0}}E=r.callback,E!==null&&(t.flags|=64,z&&(t.flags|=8192),z=n.callbacks,z===null?n.callbacks=[E]:z.push(E))}else z={lane:E,tag:r.tag,payload:r.payload,callback:r.callback,next:null},R===null?(T=R=z,d=w):R=R.next=z,i|=E;if(r=r.next,r===null){if(r=n.shared.pending,r===null)break;z=r,r=z.next,z.next=null,n.lastBaseUpdate=z,n.shared.pending=null}}while(!0);R===null&&(d=w),n.baseState=d,n.firstBaseUpdate=T,n.lastBaseUpdate=R,u===null&&(n.shared.lanes=0),pl|=i,t.lanes=i,t.memoizedState=w}}function is(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function cs(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)is(l[t],e)}var ba=U(null),yu=U(0);function rs(t,e){t=Pe,q(yu,t),q(ba,e),Pe=t|e.baseLanes}function oc(){q(yu,Pe),q(ba,ba.current)}function sc(){Pe=yu.current,Y(ba),Y(yu)}var dl=0,it=null,xt=null,Yt=null,bu=!1,pa=!1,Ql=!1,pu=0,hn=0,Sa=null,Ih=0;function Bt(){throw Error(o(321))}function fc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!oe(t[l],e[l]))return!1;return!0}function dc(t,e,l,a,n,u){return dl=u,it=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,x.H=t===null||t.memoizedState===null?Zs:ks,Ql=!1,u=l(a,n),Ql=!1,pa&&(u=ss(e,l,a,n)),os(t),u}function os(t){x.H=zu;var e=xt!==null&&xt.next!==null;if(dl=0,Yt=xt=it=null,bu=!1,hn=0,Sa=null,e)throw Error(o(300));t===null||Zt||(t=t.dependencies,t!==null&&du(t)&&(Zt=!0))}function ss(t,e,l,a){it=t;var n=0;do{if(pa&&(Sa=null),hn=0,pa=!1,25<=n)throw Error(o(301));if(n+=1,Yt=xt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}x.H=iv,u=e(l,a)}while(pa);return u}function tv(){var t=x.H,e=t.useState()[0];return e=typeof e.then=="function"?vn(e):e,t=t.useState()[0],(xt!==null?xt.memoizedState:null)!==t&&(it.flags|=1024),e}function mc(){var t=pu!==0;return pu=0,t}function hc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function vc(t){if(bu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}bu=!1}dl=0,Yt=xt=it=null,pa=!1,hn=pu=0,Sa=null}function ae(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Yt===null?it.memoizedState=Yt=t:Yt=Yt.next=t,Yt}function Gt(){if(xt===null){var t=it.alternate;t=t!==null?t.memoizedState:null}else t=xt.next;var e=Yt===null?it.memoizedState:Yt.next;if(e!==null)Yt=e,xt=t;else{if(t===null)throw it.alternate===null?Error(o(467)):Error(o(310));xt=t,t={memoizedState:xt.memoizedState,baseState:xt.baseState,baseQueue:xt.baseQueue,queue:xt.queue,next:null},Yt===null?it.memoizedState=Yt=t:Yt=Yt.next=t}return Yt}function gc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function vn(t){var e=hn;return hn+=1,Sa===null&&(Sa=[]),t=as(Sa,t,e),e=it,(Yt===null?e.memoizedState:Yt.next)===null&&(e=e.alternate,x.H=e===null||e.memoizedState===null?Zs:ks),t}function Su(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return vn(t);if(t.$$typeof===yt)return Pt(t)}throw Error(o(438,String(t)))}function yc(t){var e=null,l=it.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=it.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=gc(),it.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=Dt;return e.index++,l}function ke(t,e){return typeof e=="function"?e(t):e}function xu(t){var e=Gt();return bc(e,xt,t)}function bc(t,e,l){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var r=i=null,d=null,T=e,R=!1;do{var w=T.lane&-536870913;if(w!==T.lane?(mt&w)===w:(dl&w)===w){var E=T.revertLane;if(E===0)d!==null&&(d=d.next={lane:0,revertLane:0,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null}),w===ga&&(R=!0);else if((dl&E)===E){T=T.next,E===ga&&(R=!0);continue}else w={lane:0,revertLane:T.revertLane,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null},d===null?(r=d=w,i=u):d=d.next=w,it.lanes|=E,pl|=E;w=T.action,Ql&&l(u,w),u=T.hasEagerState?T.eagerState:l(u,w)}else E={lane:w,revertLane:T.revertLane,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null},d===null?(r=d=E,i=u):d=d.next=E,it.lanes|=w,pl|=w;T=T.next}while(T!==null&&T!==e);if(d===null?i=u:d.next=r,!oe(u,t.memoizedState)&&(Zt=!0,R&&(l=ya,l!==null)))throw l;t.memoizedState=u,t.baseState=i,t.baseQueue=d,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function pc(t){var e=Gt(),l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);oe(u,e.memoizedState)||(Zt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function fs(t,e,l){var a=it,n=Gt(),u=gt;if(u){if(l===void 0)throw Error(o(407));l=l()}else l=e();var i=!oe((xt||n).memoizedState,l);i&&(n.memoizedState=l,Zt=!0),n=n.queue;var r=hs.bind(null,a,n,t);if(gn(2048,8,r,[t]),n.getSnapshot!==e||i||Yt!==null&&Yt.memoizedState.tag&1){if(a.flags|=2048,xa(9,Au(),ms.bind(null,a,n,l,e),null),Nt===null)throw Error(o(349));u||(dl&124)!==0||ds(a,e,l)}return l}function ds(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=it.updateQueue,e===null?(e=gc(),it.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function ms(t,e,l,a){e.value=l,e.getSnapshot=a,vs(e)&&gs(t)}function hs(t,e,l){return l(function(){vs(e)&&gs(t)})}function vs(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!oe(t,l)}catch{return!0}}function gs(t){var e=da(t,2);e!==null&&ve(e,t,2)}function Sc(t){var e=ae();if(typeof t=="function"){var l=t;if(t=l(),Ql){ul(!0);try{l()}finally{ul(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:t},e}function ys(t,e,l,a){return t.baseState=l,bc(t,xt,typeof a=="function"?a:ke)}function ev(t,e,l,a,n){if(Eu(t))throw Error(o(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};x.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,bs(e,u)):(u.next=l.next,e.pending=l.next=u)}}function bs(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=x.T,i={};x.T=i;try{var r=l(n,a),d=x.S;d!==null&&d(i,r),ps(t,e,r)}catch(T){xc(t,e,T)}finally{x.T=u}}else try{u=l(n,a),ps(t,e,u)}catch(T){xc(t,e,T)}}function ps(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Ss(t,e,a)},function(a){return xc(t,e,a)}):Ss(t,e,l)}function Ss(t,e,l){e.status="fulfilled",e.value=l,xs(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,bs(t,l)))}function xc(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,xs(e),e=e.next;while(e!==a)}t.action=null}function xs(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function As(t,e){return e}function Ts(t,e){if(gt){var l=Nt.formState;if(l!==null){t:{var a=it;if(gt){if(Ut){e:{for(var n=Ut,u=Ce;n.nodeType!==8;){if(!u){n=null;break e}if(n=Oe(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Ut=Oe(n.nextSibling),a=n.data==="F!";break t}}Yl(a)}a=!1}a&&(e=l[0])}}return l=ae(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:As,lastRenderedState:e},l.queue=a,l=Ls.bind(null,it,a),a.dispatch=l,a=Sc(!1),u=_c.bind(null,it,!1,a.queue),a=ae(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=ev.bind(null,it,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Es(t){var e=Gt();return zs(e,xt,t)}function zs(t,e,l){if(e=bc(t,e,As)[0],t=xu(ke)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=vn(e)}catch(i){throw i===on?vu:i}else a=e;e=Gt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(it.flags|=2048,xa(9,Au(),lv.bind(null,n,l),null)),[a,u,t]}function lv(t,e){t.action=e}function _s(t){var e=Gt(),l=xt;if(l!==null)return zs(e,l,t);Gt(),e=e.memoizedState,l=Gt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function xa(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=it.updateQueue,e===null&&(e=gc(),it.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function Au(){return{destroy:void 0,resource:void 0}}function Ns(){return Gt().memoizedState}function Tu(t,e,l,a){var n=ae();a=a===void 0?null:a,it.flags|=t,n.memoizedState=xa(1|e,Au(),l,a)}function gn(t,e,l,a){var n=Gt();a=a===void 0?null:a;var u=n.memoizedState.inst;xt!==null&&a!==null&&fc(a,xt.memoizedState.deps)?n.memoizedState=xa(e,u,l,a):(it.flags|=t,n.memoizedState=xa(1|e,u,l,a))}function Ms(t,e){Tu(8390656,8,t,e)}function Rs(t,e){gn(2048,8,t,e)}function Os(t,e){return gn(4,2,t,e)}function Ds(t,e){return gn(4,4,t,e)}function ws(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Cs(t,e,l){l=l!=null?l.concat([t]):null,gn(4,4,ws.bind(null,e,t),l)}function Ac(){}function Us(t,e){var l=Gt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&fc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function js(t,e){var l=Gt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&fc(e,a[1]))return a[0];if(a=t(),Ql){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a}function Tc(t,e,l){return l===void 0||(dl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Yf(),it.lanes|=t,pl|=t,l)}function Hs(t,e,l,a){return oe(l,e)?l:ba.current!==null?(t=Tc(t,l,a),oe(t,e)||(Zt=!0),t):(dl&42)===0?(Zt=!0,t.memoizedState=l):(t=Yf(),it.lanes|=t,pl|=t,e)}function Bs(t,e,l,a,n){var u=H.p;H.p=u!==0&&8>u?u:8;var i=x.T,r={};x.T=r,_c(t,!1,e,l);try{var d=n(),T=x.S;if(T!==null&&T(r,d),d!==null&&typeof d=="object"&&typeof d.then=="function"){var R=Ph(d,a);yn(t,e,R,he(t))}else yn(t,e,a,he(t))}catch(w){yn(t,e,{then:function(){},status:"rejected",reason:w},he())}finally{H.p=u,x.T=i}}function av(){}function Ec(t,e,l,a){if(t.tag!==5)throw Error(o(476));var n=qs(t).queue;Bs(t,n,e,D,l===null?av:function(){return Ys(t),l(a)})}function qs(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:D},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Ys(t){var e=qs(t).next.queue;yn(t,e,{},he())}function zc(){return Pt(jn)}function Gs(){return Gt().memoizedState}function Xs(){return Gt().memoizedState}function nv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=he();t=sl(l);var a=fl(e,t,l);a!==null&&(ve(a,e,l),fn(a,e,l)),e={cache:ec()},t.payload=e;return}e=e.return}}function uv(t,e,l){var a=he();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Eu(t)?Qs(e,l):(l=Zi(t,e,l,a),l!==null&&(ve(l,t,a),Vs(l,e,a)))}function Ls(t,e,l){var a=he();yn(t,e,l,a)}function yn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Eu(t))Qs(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var i=e.lastRenderedState,r=u(i,l);if(n.hasEagerState=!0,n.eagerState=r,oe(r,i))return cu(t,e,n,0),Nt===null&&iu(),!1}catch{}finally{}if(l=Zi(t,e,n,a),l!==null)return ve(l,t,a),Vs(l,e,a),!0}return!1}function _c(t,e,l,a){if(a={lane:2,revertLane:nr(),action:a,hasEagerState:!1,eagerState:null,next:null},Eu(t)){if(e)throw Error(o(479))}else e=Zi(t,l,a,2),e!==null&&ve(e,t,2)}function Eu(t){var e=t.alternate;return t===it||e!==null&&e===it}function Qs(t,e){pa=bu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Vs(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Fr(t,l)}}var zu={readContext:Pt,use:Su,useCallback:Bt,useContext:Bt,useEffect:Bt,useImperativeHandle:Bt,useLayoutEffect:Bt,useInsertionEffect:Bt,useMemo:Bt,useReducer:Bt,useRef:Bt,useState:Bt,useDebugValue:Bt,useDeferredValue:Bt,useTransition:Bt,useSyncExternalStore:Bt,useId:Bt,useHostTransitionStatus:Bt,useFormState:Bt,useActionState:Bt,useOptimistic:Bt,useMemoCache:Bt,useCacheRefresh:Bt},Zs={readContext:Pt,use:Su,useCallback:function(t,e){return ae().memoizedState=[t,e===void 0?null:e],t},useContext:Pt,useEffect:Ms,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Tu(4194308,4,ws.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Tu(4194308,4,t,e)},useInsertionEffect:function(t,e){Tu(4,2,t,e)},useMemo:function(t,e){var l=ae();e=e===void 0?null:e;var a=t();if(Ql){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=ae();if(l!==void 0){var n=l(e);if(Ql){ul(!0);try{l(e)}finally{ul(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=uv.bind(null,it,t),[a.memoizedState,t]},useRef:function(t){var e=ae();return t={current:t},e.memoizedState=t},useState:function(t){t=Sc(t);var e=t.queue,l=Ls.bind(null,it,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:Ac,useDeferredValue:function(t,e){var l=ae();return Tc(l,t,e)},useTransition:function(){var t=Sc(!1);return t=Bs.bind(null,it,t.queue,!0,!1),ae().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=it,n=ae();if(gt){if(l===void 0)throw Error(o(407));l=l()}else{if(l=e(),Nt===null)throw Error(o(349));(mt&124)!==0||ds(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,Ms(hs.bind(null,a,u,t),[t]),a.flags|=2048,xa(9,Au(),ms.bind(null,a,u,l,e),null),l},useId:function(){var t=ae(),e=Nt.identifierPrefix;if(gt){var l=Qe,a=Le;l=(a&~(1<<32-re(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=pu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=Ih++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:zc,useFormState:Ts,useActionState:Ts,useOptimistic:function(t){var e=ae();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=_c.bind(null,it,!0,l),l.dispatch=e,[t,e]},useMemoCache:yc,useCacheRefresh:function(){return ae().memoizedState=nv.bind(null,it)}},ks={readContext:Pt,use:Su,useCallback:Us,useContext:Pt,useEffect:Rs,useImperativeHandle:Cs,useInsertionEffect:Os,useLayoutEffect:Ds,useMemo:js,useReducer:xu,useRef:Ns,useState:function(){return xu(ke)},useDebugValue:Ac,useDeferredValue:function(t,e){var l=Gt();return Hs(l,xt.memoizedState,t,e)},useTransition:function(){var t=xu(ke)[0],e=Gt().memoizedState;return[typeof t=="boolean"?t:vn(t),e]},useSyncExternalStore:fs,useId:Gs,useHostTransitionStatus:zc,useFormState:Es,useActionState:Es,useOptimistic:function(t,e){var l=Gt();return ys(l,xt,t,e)},useMemoCache:yc,useCacheRefresh:Xs},iv={readContext:Pt,use:Su,useCallback:Us,useContext:Pt,useEffect:Rs,useImperativeHandle:Cs,useInsertionEffect:Os,useLayoutEffect:Ds,useMemo:js,useReducer:pc,useRef:Ns,useState:function(){return pc(ke)},useDebugValue:Ac,useDeferredValue:function(t,e){var l=Gt();return xt===null?Tc(l,t,e):Hs(l,xt.memoizedState,t,e)},useTransition:function(){var t=pc(ke)[0],e=Gt().memoizedState;return[typeof t=="boolean"?t:vn(t),e]},useSyncExternalStore:fs,useId:Gs,useHostTransitionStatus:zc,useFormState:_s,useActionState:_s,useOptimistic:function(t,e){var l=Gt();return xt!==null?ys(l,xt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:yc,useCacheRefresh:Xs},Aa=null,bn=0;function _u(t){var e=bn;return bn+=1,Aa===null&&(Aa=[]),as(Aa,t,e)}function pn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Nu(t,e){throw e.$$typeof===j?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Ks(t){var e=t._init;return e(t._payload)}function Js(t){function e(y,v){if(t){var A=y.deletions;A===null?(y.deletions=[v],y.flags|=16):A.push(v)}}function l(y,v){if(!t)return null;for(;v!==null;)e(y,v),v=v.sibling;return null}function a(y){for(var v=new Map;y!==null;)y.key!==null?v.set(y.key,y):v.set(y.index,y),y=y.sibling;return v}function n(y,v){return y=Xe(y,v),y.index=0,y.sibling=null,y}function u(y,v,A){return y.index=A,t?(A=y.alternate,A!==null?(A=A.index,A<v?(y.flags|=67108866,v):A):(y.flags|=67108866,v)):(y.flags|=1048576,v)}function i(y){return t&&y.alternate===null&&(y.flags|=67108866),y}function r(y,v,A,O){return v===null||v.tag!==6?(v=Ki(A,y.mode,O),v.return=y,v):(v=n(v,A),v.return=y,v)}function d(y,v,A,O){var X=A.type;return X===J?R(y,v,A.props.children,O,A.key):v!==null&&(v.elementType===X||typeof X=="object"&&X!==null&&X.$$typeof===V&&Ks(X)===v.type)?(v=n(v,A.props),pn(v,A),v.return=y,v):(v=ou(A.type,A.key,A.props,null,y.mode,O),pn(v,A),v.return=y,v)}function T(y,v,A,O){return v===null||v.tag!==4||v.stateNode.containerInfo!==A.containerInfo||v.stateNode.implementation!==A.implementation?(v=Ji(A,y.mode,O),v.return=y,v):(v=n(v,A.children||[]),v.return=y,v)}function R(y,v,A,O,X){return v===null||v.tag!==7?(v=jl(A,y.mode,O,X),v.return=y,v):(v=n(v,A),v.return=y,v)}function w(y,v,A){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=Ki(""+v,y.mode,A),v.return=y,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case B:return A=ou(v.type,v.key,v.props,null,y.mode,A),pn(A,v),A.return=y,A;case Z:return v=Ji(v,y.mode,A),v.return=y,v;case V:var O=v._init;return v=O(v._payload),w(y,v,A)}if(at(v)||Rt(v))return v=jl(v,y.mode,A,null),v.return=y,v;if(typeof v.then=="function")return w(y,_u(v),A);if(v.$$typeof===yt)return w(y,mu(y,v),A);Nu(y,v)}return null}function E(y,v,A,O){var X=v!==null?v.key:null;if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return X!==null?null:r(y,v,""+A,O);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case B:return A.key===X?d(y,v,A,O):null;case Z:return A.key===X?T(y,v,A,O):null;case V:return X=A._init,A=X(A._payload),E(y,v,A,O)}if(at(A)||Rt(A))return X!==null?null:R(y,v,A,O,null);if(typeof A.then=="function")return E(y,v,_u(A),O);if(A.$$typeof===yt)return E(y,v,mu(y,A),O);Nu(y,A)}return null}function z(y,v,A,O,X){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return y=y.get(A)||null,r(v,y,""+O,X);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case B:return y=y.get(O.key===null?A:O.key)||null,d(v,y,O,X);case Z:return y=y.get(O.key===null?A:O.key)||null,T(v,y,O,X);case V:var rt=O._init;return O=rt(O._payload),z(y,v,A,O,X)}if(at(O)||Rt(O))return y=y.get(A)||null,R(v,y,O,X,null);if(typeof O.then=="function")return z(y,v,A,_u(O),X);if(O.$$typeof===yt)return z(y,v,A,mu(v,O),X);Nu(v,O)}return null}function I(y,v,A,O){for(var X=null,rt=null,K=v,F=v=0,Kt=null;K!==null&&F<A.length;F++){K.index>F?(Kt=K,K=null):Kt=K.sibling;var vt=E(y,K,A[F],O);if(vt===null){K===null&&(K=Kt);break}t&&K&&vt.alternate===null&&e(y,K),v=u(vt,v,F),rt===null?X=vt:rt.sibling=vt,rt=vt,K=Kt}if(F===A.length)return l(y,K),gt&&Bl(y,F),X;if(K===null){for(;F<A.length;F++)K=w(y,A[F],O),K!==null&&(v=u(K,v,F),rt===null?X=K:rt.sibling=K,rt=K);return gt&&Bl(y,F),X}for(K=a(K);F<A.length;F++)Kt=z(K,y,F,A[F],O),Kt!==null&&(t&&Kt.alternate!==null&&K.delete(Kt.key===null?F:Kt.key),v=u(Kt,v,F),rt===null?X=Kt:rt.sibling=Kt,rt=Kt);return t&&K.forEach(function(Ml){return e(y,Ml)}),gt&&Bl(y,F),X}function W(y,v,A,O){if(A==null)throw Error(o(151));for(var X=null,rt=null,K=v,F=v=0,Kt=null,vt=A.next();K!==null&&!vt.done;F++,vt=A.next()){K.index>F?(Kt=K,K=null):Kt=K.sibling;var Ml=E(y,K,vt.value,O);if(Ml===null){K===null&&(K=Kt);break}t&&K&&Ml.alternate===null&&e(y,K),v=u(Ml,v,F),rt===null?X=Ml:rt.sibling=Ml,rt=Ml,K=Kt}if(vt.done)return l(y,K),gt&&Bl(y,F),X;if(K===null){for(;!vt.done;F++,vt=A.next())vt=w(y,vt.value,O),vt!==null&&(v=u(vt,v,F),rt===null?X=vt:rt.sibling=vt,rt=vt);return gt&&Bl(y,F),X}for(K=a(K);!vt.done;F++,vt=A.next())vt=z(K,y,F,vt.value,O),vt!==null&&(t&&vt.alternate!==null&&K.delete(vt.key===null?F:vt.key),v=u(vt,v,F),rt===null?X=vt:rt.sibling=vt,rt=vt);return t&&K.forEach(function(c0){return e(y,c0)}),gt&&Bl(y,F),X}function Tt(y,v,A,O){if(typeof A=="object"&&A!==null&&A.type===J&&A.key===null&&(A=A.props.children),typeof A=="object"&&A!==null){switch(A.$$typeof){case B:t:{for(var X=A.key;v!==null;){if(v.key===X){if(X=A.type,X===J){if(v.tag===7){l(y,v.sibling),O=n(v,A.props.children),O.return=y,y=O;break t}}else if(v.elementType===X||typeof X=="object"&&X!==null&&X.$$typeof===V&&Ks(X)===v.type){l(y,v.sibling),O=n(v,A.props),pn(O,A),O.return=y,y=O;break t}l(y,v);break}else e(y,v);v=v.sibling}A.type===J?(O=jl(A.props.children,y.mode,O,A.key),O.return=y,y=O):(O=ou(A.type,A.key,A.props,null,y.mode,O),pn(O,A),O.return=y,y=O)}return i(y);case Z:t:{for(X=A.key;v!==null;){if(v.key===X)if(v.tag===4&&v.stateNode.containerInfo===A.containerInfo&&v.stateNode.implementation===A.implementation){l(y,v.sibling),O=n(v,A.children||[]),O.return=y,y=O;break t}else{l(y,v);break}else e(y,v);v=v.sibling}O=Ji(A,y.mode,O),O.return=y,y=O}return i(y);case V:return X=A._init,A=X(A._payload),Tt(y,v,A,O)}if(at(A))return I(y,v,A,O);if(Rt(A)){if(X=Rt(A),typeof X!="function")throw Error(o(150));return A=X.call(A),W(y,v,A,O)}if(typeof A.then=="function")return Tt(y,v,_u(A),O);if(A.$$typeof===yt)return Tt(y,v,mu(y,A),O);Nu(y,A)}return typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint"?(A=""+A,v!==null&&v.tag===6?(l(y,v.sibling),O=n(v,A),O.return=y,y=O):(l(y,v),O=Ki(A,y.mode,O),O.return=y,y=O),i(y)):l(y,v)}return function(y,v,A,O){try{bn=0;var X=Tt(y,v,A,O);return Aa=null,X}catch(K){if(K===on||K===vu)throw K;var rt=se(29,K,null,y.mode);return rt.lanes=O,rt.return=y,rt}finally{}}}var Ta=Js(!0),Ws=Js(!1),Te=U(null),Ue=null;function ml(t){var e=t.alternate;q(Lt,Lt.current&1),q(Te,t),Ue===null&&(e===null||ba.current!==null||e.memoizedState!==null)&&(Ue=t)}function $s(t){if(t.tag===22){if(q(Lt,Lt.current),q(Te,t),Ue===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ue=t)}}else hl()}function hl(){q(Lt,Lt.current),q(Te,Te.current)}function Ke(t){Y(Te),Ue===t&&(Ue=null),Y(Lt)}var Lt=U(0);function Mu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||gr(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Nc(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:N({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Mc={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=he(),n=sl(a);n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(ve(e,t,a),fn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=he(),n=sl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(ve(e,t,a),fn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=he(),a=sl(l);a.tag=2,e!=null&&(a.callback=e),e=fl(t,a,l),e!==null&&(ve(e,t,l),fn(e,t,l))}};function Fs(t,e,l,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):e.prototype&&e.prototype.isPureReactComponent?!tn(l,a)||!tn(n,u):!0}function Ps(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Mc.enqueueReplaceState(e,e.state,null)}function Vl(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=N({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Ru=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Is(t){Ru(t)}function tf(t){console.error(t)}function ef(t){Ru(t)}function Ou(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function lf(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Rc(t,e,l){return l=sl(l),l.tag=3,l.payload={element:null},l.callback=function(){Ou(t,e)},l}function af(t){return t=sl(t),t.tag=3,t}function nf(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){lf(e,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){lf(e,l,a),typeof n!="function"&&(Sl===null?Sl=new Set([this]):Sl.add(this));var r=a.stack;this.componentDidCatch(a.value,{componentStack:r!==null?r:""})})}function cv(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&un(e,l,n,!0),l=Te.current,l!==null){switch(l.tag){case 13:return Ue===null?Ic():l.alternate===null&&jt===0&&(jt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===nc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),er(t,a,n)),!1;case 22:return l.flags|=65536,a===nc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),er(t,a,n)),!1}throw Error(o(435,l.tag))}return er(t,a,n),Ic(),!1}if(gt)return e=Te.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Fi&&(t=Error(o(422),{cause:a}),nn(pe(t,l)))):(a!==Fi&&(e=Error(o(423),{cause:a}),nn(pe(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=pe(a,l),n=Rc(t.stateNode,a,n),cc(t,n),jt!==4&&(jt=2)),!1;var u=Error(o(520),{cause:a});if(u=pe(u,l),_n===null?_n=[u]:_n.push(u),jt!==4&&(jt=2),e===null)return!0;a=pe(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Rc(l.stateNode,a,t),cc(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Sl===null||!Sl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=af(n),nf(n,t,l,a),cc(l,n),!1}l=l.return}while(l!==null);return!1}var uf=Error(o(461)),Zt=!1;function Jt(t,e,l,a){e.child=t===null?Ws(e,null,l,a):Ta(e,t.child,l,a)}function cf(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var i={};for(var r in a)r!=="ref"&&(i[r]=a[r])}else i=a;return Xl(e),a=dc(t,e,l,i,u,n),r=mc(),t!==null&&!Zt?(hc(t,e,n),Je(t,e,n)):(gt&&r&&Wi(e),e.flags|=1,Jt(t,e,a,n),e.child)}function rf(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!ki(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,of(t,e,u,a,n)):(t=ou(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Bc(t,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:tn,l(i,a)&&t.ref===e.ref)return Je(t,e,n)}return e.flags|=1,t=Xe(u,a),t.ref=e.ref,t.return=e,e.child=t}function of(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(tn(u,a)&&t.ref===e.ref)if(Zt=!1,e.pendingProps=a=u,Bc(t,n))(t.flags&131072)!==0&&(Zt=!0);else return e.lanes=t.lanes,Je(t,e,n)}return Oc(t,e,l,a,n)}function sf(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return ff(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&hu(e,u!==null?u.cachePool:null),u!==null?rs(e,u):oc(),$s(e);else return e.lanes=e.childLanes=536870912,ff(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(hu(e,u.cachePool),rs(e,u),hl(),e.memoizedState=null):(t!==null&&hu(e,null),oc(),hl());return Jt(t,e,n,l),e.child}function ff(t,e,l,a){var n=ac();return n=n===null?null:{parent:Xt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&hu(e,null),oc(),$s(e),t!==null&&un(t,e,a,!0),null}function Du(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(o(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function Oc(t,e,l,a,n){return Xl(e),l=dc(t,e,l,a,void 0,n),a=mc(),t!==null&&!Zt?(hc(t,e,n),Je(t,e,n)):(gt&&a&&Wi(e),e.flags|=1,Jt(t,e,l,n),e.child)}function df(t,e,l,a,n,u){return Xl(e),e.updateQueue=null,l=ss(e,a,l,n),os(t),a=mc(),t!==null&&!Zt?(hc(t,e,u),Je(t,e,u)):(gt&&a&&Wi(e),e.flags|=1,Jt(t,e,l,u),e.child)}function mf(t,e,l,a,n){if(Xl(e),e.stateNode===null){var u=ma,i=l.contextType;typeof i=="object"&&i!==null&&(u=Pt(i)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Mc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},uc(e),i=l.contextType,u.context=typeof i=="object"&&i!==null?Pt(i):ma,u.state=e.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(Nc(e,l,i,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&Mc.enqueueReplaceState(u,u.state,null),mn(e,a,u,n),dn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var r=e.memoizedProps,d=Vl(l,r);u.props=d;var T=u.context,R=l.contextType;i=ma,typeof R=="object"&&R!==null&&(i=Pt(R));var w=l.getDerivedStateFromProps;R=typeof w=="function"||typeof u.getSnapshotBeforeUpdate=="function",r=e.pendingProps!==r,R||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r||T!==i)&&Ps(e,u,a,i),ol=!1;var E=e.memoizedState;u.state=E,mn(e,a,u,n),dn(),T=e.memoizedState,r||E!==T||ol?(typeof w=="function"&&(Nc(e,l,w,a),T=e.memoizedState),(d=ol||Fs(e,l,d,a,E,T,i))?(R||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=T),u.props=a,u.state=T,u.context=i,a=d):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,ic(t,e),i=e.memoizedProps,R=Vl(l,i),u.props=R,w=e.pendingProps,E=u.context,T=l.contextType,d=ma,typeof T=="object"&&T!==null&&(d=Pt(T)),r=l.getDerivedStateFromProps,(T=typeof r=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==w||E!==d)&&Ps(e,u,a,d),ol=!1,E=e.memoizedState,u.state=E,mn(e,a,u,n),dn();var z=e.memoizedState;i!==w||E!==z||ol||t!==null&&t.dependencies!==null&&du(t.dependencies)?(typeof r=="function"&&(Nc(e,l,r,a),z=e.memoizedState),(R=ol||Fs(e,l,R,a,E,z,d)||t!==null&&t.dependencies!==null&&du(t.dependencies))?(T||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,z,d),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,z,d)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=z),u.props=a,u.state=z,u.context=d,a=R):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Du(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Ta(e,t.child,null,n),e.child=Ta(e,null,l,n)):Jt(t,e,l,n),e.memoizedState=u.state,t=e.child):t=Je(t,e,n),t}function hf(t,e,l,a){return an(),e.flags|=256,Jt(t,e,l,a),e.child}var Dc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function wc(t){return{baseLanes:t,cachePool:ts()}}function Cc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Ee),t}function vf(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(Lt.current&2)!==0),i&&(n=!0,e.flags&=-129),i=(e.flags&32)!==0,e.flags&=-33,t===null){if(gt){if(n?ml(e):hl(),gt){var r=Ut,d;if(d=r){t:{for(d=r,r=Ce;d.nodeType!==8;){if(!r){r=null;break t}if(d=Oe(d.nextSibling),d===null){r=null;break t}}r=d}r!==null?(e.memoizedState={dehydrated:r,treeContext:Hl!==null?{id:Le,overflow:Qe}:null,retryLane:536870912,hydrationErrors:null},d=se(18,null,null,0),d.stateNode=r,d.return=e,e.child=d,te=e,Ut=null,d=!0):d=!1}d||Yl(e)}if(r=e.memoizedState,r!==null&&(r=r.dehydrated,r!==null))return gr(r)?e.lanes=32:e.lanes=536870912,null;Ke(e)}return r=a.children,a=a.fallback,n?(hl(),n=e.mode,r=wu({mode:"hidden",children:r},n),a=jl(a,n,l,null),r.return=e,a.return=e,r.sibling=a,e.child=r,n=e.child,n.memoizedState=wc(l),n.childLanes=Cc(t,i,l),e.memoizedState=Dc,a):(ml(e),Uc(e,r))}if(d=t.memoizedState,d!==null&&(r=d.dehydrated,r!==null)){if(u)e.flags&256?(ml(e),e.flags&=-257,e=jc(t,e,l)):e.memoizedState!==null?(hl(),e.child=t.child,e.flags|=128,e=null):(hl(),n=a.fallback,r=e.mode,a=wu({mode:"visible",children:a.children},r),n=jl(n,r,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ta(e,t.child,null,l),a=e.child,a.memoizedState=wc(l),a.childLanes=Cc(t,i,l),e.memoizedState=Dc,e=n);else if(ml(e),gr(r)){if(i=r.nextSibling&&r.nextSibling.dataset,i)var T=i.dgst;i=T,a=Error(o(419)),a.stack="",a.digest=i,nn({value:a,source:null,stack:null}),e=jc(t,e,l)}else if(Zt||un(t,e,l,!1),i=(l&t.childLanes)!==0,Zt||i){if(i=Nt,i!==null&&(a=l&-l,a=(a&42)!==0?1:yi(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==d.retryLane))throw d.retryLane=a,da(t,a),ve(i,t,a),uf;r.data==="$?"||Ic(),e=jc(t,e,l)}else r.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=d.treeContext,Ut=Oe(r.nextSibling),te=e,gt=!0,ql=null,Ce=!1,t!==null&&(xe[Ae++]=Le,xe[Ae++]=Qe,xe[Ae++]=Hl,Le=t.id,Qe=t.overflow,Hl=e),e=Uc(e,a.children),e.flags|=4096);return e}return n?(hl(),n=a.fallback,r=e.mode,d=t.child,T=d.sibling,a=Xe(d,{mode:"hidden",children:a.children}),a.subtreeFlags=d.subtreeFlags&65011712,T!==null?n=Xe(T,n):(n=jl(n,r,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,r=t.child.memoizedState,r===null?r=wc(l):(d=r.cachePool,d!==null?(T=Xt._currentValue,d=d.parent!==T?{parent:T,pool:T}:d):d=ts(),r={baseLanes:r.baseLanes|l,cachePool:d}),n.memoizedState=r,n.childLanes=Cc(t,i,l),e.memoizedState=Dc,a):(ml(e),l=t.child,t=l.sibling,l=Xe(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(i=e.deletions,i===null?(e.deletions=[t],e.flags|=16):i.push(t)),e.child=l,e.memoizedState=null,l)}function Uc(t,e){return e=wu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function wu(t,e){return t=se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function jc(t,e,l){return Ta(e,t.child,null,l),t=Uc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function gf(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Ii(t.return,e,l)}function Hc(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function yf(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(Jt(t,e,a.children,l),a=Lt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&gf(t,l,e);else if(t.tag===19)gf(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(q(Lt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Mu(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Hc(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Mu(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Hc(e,!0,l,null,u);break;case"together":Hc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Je(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),pl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(un(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,l=Xe(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Xe(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Bc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&du(t)))}function rv(t,e,l){switch(e.tag){case 3:zt(e,e.stateNode.containerInfo),rl(e,Xt,t.memoizedState.cache),an();break;case 27:case 5:ll(e);break;case 4:zt(e,e.stateNode.containerInfo);break;case 10:rl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(ml(e),e.flags|=128,null):(l&e.child.childLanes)!==0?vf(t,e,l):(ml(e),t=Je(t,e,l),t!==null?t.sibling:null);ml(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(un(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return yf(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),q(Lt,Lt.current),a)break;return null;case 22:case 23:return e.lanes=0,sf(t,e,l);case 24:rl(e,Xt,t.memoizedState.cache)}return Je(t,e,l)}function bf(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Zt=!0;else{if(!Bc(t,l)&&(e.flags&128)===0)return Zt=!1,rv(t,e,l);Zt=(t.flags&131072)!==0}else Zt=!1,gt&&(e.flags&1048576)!==0&&Ko(e,fu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")ki(a)?(t=Vl(a,t),e.tag=1,e=mf(null,e,a,t,l)):(e.tag=0,e=Oc(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===Mt){e.tag=11,e=cf(null,e,a,t,l);break t}else if(n===Et){e.tag=14,e=rf(null,e,a,t,l);break t}}throw e=k(a)||a,Error(o(306,e,""))}}return e;case 0:return Oc(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Vl(a,e.pendingProps),mf(t,e,a,n,l);case 3:t:{if(zt(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,ic(t,e),mn(e,a,null,l);var i=e.memoizedState;if(a=i.cache,rl(e,Xt,a),a!==u.cache&&tc(e,[Xt],l,!0),dn(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=hf(t,e,a,l);break t}else if(a!==n){n=pe(Error(o(424)),e),nn(n),e=hf(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ut=Oe(t.firstChild),te=e,gt=!0,ql=null,Ce=!0,l=Ws(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(an(),a===n){e=Je(t,e,l);break t}Jt(t,e,a,l)}e=e.child}return e;case 26:return Du(t,e),t===null?(l=Ad(e.type,null,e.pendingProps,null))?e.memoizedState=l:gt||(l=e.type,t=e.pendingProps,a=ku(tt.current).createElement(l),a[Ft]=e,a[ee]=t,$t(a,l,t),Vt(a),e.stateNode=a):e.memoizedState=Ad(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return ll(e),t===null&&gt&&(a=e.stateNode=pd(e.type,e.pendingProps,tt.current),te=e,Ce=!0,n=Ut,Tl(e.type)?(yr=n,Ut=Oe(a.firstChild)):Ut=n),Jt(t,e,e.pendingProps.children,l),Du(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&gt&&((n=a=Ut)&&(a=Hv(a,e.type,e.pendingProps,Ce),a!==null?(e.stateNode=a,te=e,Ut=Oe(a.firstChild),Ce=!1,n=!0):n=!1),n||Yl(e)),ll(e),n=e.type,u=e.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,mr(n,u)?a=null:i!==null&&mr(n,i)&&(e.flags|=32),e.memoizedState!==null&&(n=dc(t,e,tv,null,null,l),jn._currentValue=n),Du(t,e),Jt(t,e,a,l),e.child;case 6:return t===null&&gt&&((t=l=Ut)&&(l=Bv(l,e.pendingProps,Ce),l!==null?(e.stateNode=l,te=e,Ut=null,t=!0):t=!1),t||Yl(e)),null;case 13:return vf(t,e,l);case 4:return zt(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ta(e,null,a,l):Jt(t,e,a,l),e.child;case 11:return cf(t,e,e.type,e.pendingProps,l);case 7:return Jt(t,e,e.pendingProps,l),e.child;case 8:return Jt(t,e,e.pendingProps.children,l),e.child;case 12:return Jt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,rl(e,e.type,a.value),Jt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Xl(e),n=Pt(n),a=a(n),e.flags|=1,Jt(t,e,a,l),e.child;case 14:return rf(t,e,e.type,e.pendingProps,l);case 15:return of(t,e,e.type,e.pendingProps,l);case 19:return yf(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=wu(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Xe(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return sf(t,e,l);case 24:return Xl(e),a=Pt(Xt),t===null?(n=ac(),n===null&&(n=Nt,u=ec(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},uc(e),rl(e,Xt,n)):((t.lanes&l)!==0&&(ic(t,e),mn(e,null,null,l),dn()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),rl(e,Xt,a)):(a=u.cache,rl(e,Xt,a),a!==n.cache&&tc(e,[Xt],l,!0))),Jt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function We(t){t.flags|=4}function pf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Nd(e)){if(e=Te.current,e!==null&&((mt&4194048)===mt?Ue!==null:(mt&62914560)!==mt&&(mt&536870912)===0||e!==Ue))throw sn=nc,es;t.flags|=8192}}function Cu(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Wr():536870912,t.lanes|=e,Na|=e)}function Sn(t,e){if(!gt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function wt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function ov(t,e,l){var a=e.pendingProps;switch($i(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return wt(e),null;case 1:return wt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Ze(Xt),ie(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(ln(e)?We(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,$o())),wt(e),null;case 26:return l=e.memoizedState,t===null?(We(e),l!==null?(wt(e),pf(e,l)):(wt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(We(e),wt(e),pf(e,l)):(wt(e),e.flags&=-16777217):(t.memoizedProps!==a&&We(e),wt(e),e.flags&=-16777217),null;case 27:al(e),l=tt.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return wt(e),null}t=G.current,ln(e)?Jo(e):(t=pd(n,a,l),e.stateNode=t,We(e))}return wt(e),null;case 5:if(al(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return wt(e),null}if(t=G.current,ln(e))Jo(e);else{switch(n=ku(tt.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[Ft]=e,t[ee]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch($t(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&We(e)}}return wt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=tt.current,ln(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=te,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[Ft]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||dd(t.nodeValue,l)),t||Yl(e)}else t=ku(t).createTextNode(a),t[Ft]=e,e.stateNode=t}return wt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=ln(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(o(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(o(317));n[Ft]=e}else an(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;wt(e),n=!1}else n=$o(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ke(e),e):(Ke(e),null)}if(Ke(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Cu(e,e.updateQueue),wt(e),null;case 4:return ie(),t===null&&rr(e.stateNode.containerInfo),wt(e),null;case 10:return Ze(e.type),wt(e),null;case 19:if(Y(Lt),n=e.memoizedState,n===null)return wt(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)Sn(n,!1);else{if(jt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Mu(t),u!==null){for(e.flags|=128,Sn(n,!1),t=u.updateQueue,e.updateQueue=t,Cu(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)ko(l,t),l=l.sibling;return q(Lt,Lt.current&1|2),e.child}t=t.sibling}n.tail!==null&&we()>Hu&&(e.flags|=128,a=!0,Sn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Mu(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,Cu(e,t),Sn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!gt)return wt(e),null}else 2*we()-n.renderingStartTime>Hu&&l!==536870912&&(e.flags|=128,a=!0,Sn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=we(),e.sibling=null,t=Lt.current,q(Lt,a?t&1|2:t&1),e):(wt(e),null);case 22:case 23:return Ke(e),sc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(wt(e),e.subtreeFlags&6&&(e.flags|=8192)):wt(e),l=e.updateQueue,l!==null&&Cu(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Y(Ll),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ze(Xt),wt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function sv(t,e){switch($i(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ze(Xt),ie(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return al(e),null;case 13:if(Ke(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));an()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(Lt),null;case 4:return ie(),null;case 10:return Ze(e.type),null;case 22:case 23:return Ke(e),sc(),t!==null&&Y(Ll),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ze(Xt),null;case 25:return null;default:return null}}function Sf(t,e){switch($i(e),e.tag){case 3:Ze(Xt),ie();break;case 26:case 27:case 5:al(e);break;case 4:ie();break;case 13:Ke(e);break;case 19:Y(Lt);break;case 10:Ze(e.type);break;case 22:case 23:Ke(e),sc(),t!==null&&Y(Ll);break;case 24:Ze(Xt)}}function xn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(r){_t(e,e.return,r)}}function vl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,r=i.destroy;if(r!==void 0){i.destroy=void 0,n=e;var d=l,T=r;try{T()}catch(R){_t(n,d,R)}}}a=a.next}while(a!==u)}}catch(R){_t(e,e.return,R)}}function xf(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{cs(e,l)}catch(a){_t(t,t.return,a)}}}function Af(t,e,l){l.props=Vl(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){_t(t,e,a)}}function An(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){_t(t,e,n)}}function je(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){_t(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){_t(t,e,n)}else l.current=null}function Tf(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){_t(t,t.return,n)}}function qc(t,e,l){try{var a=t.stateNode;Dv(a,t.type,l,e),a[ee]=e}catch(n){_t(t,t.return,n)}}function Ef(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Tl(t.type)||t.tag===4}function Yc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Ef(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Tl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Gc(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Zu));else if(a!==4&&(a===27&&Tl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Gc(t,e,l),t=t.sibling;t!==null;)Gc(t,e,l),t=t.sibling}function Uu(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&Tl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Uu(t,e,l),t=t.sibling;t!==null;)Uu(t,e,l),t=t.sibling}function zf(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);$t(e,a,l),e[Ft]=t,e[ee]=l}catch(u){_t(t,t.return,u)}}var $e=!1,qt=!1,Xc=!1,_f=typeof WeakSet=="function"?WeakSet:Set,kt=null;function fv(t,e){if(t=t.containerInfo,fr=Pu,t=Ho(t),Yi(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var i=0,r=-1,d=-1,T=0,R=0,w=t,E=null;e:for(;;){for(var z;w!==l||n!==0&&w.nodeType!==3||(r=i+n),w!==u||a!==0&&w.nodeType!==3||(d=i+a),w.nodeType===3&&(i+=w.nodeValue.length),(z=w.firstChild)!==null;)E=w,w=z;for(;;){if(w===t)break e;if(E===l&&++T===n&&(r=i),E===u&&++R===a&&(d=i),(z=w.nextSibling)!==null)break;w=E,E=w.parentNode}w=z}l=r===-1||d===-1?null:{start:r,end:d}}else l=null}l=l||{start:0,end:0}}else l=null;for(dr={focusedElem:t,selectionRange:l},Pu=!1,kt=e;kt!==null;)if(e=kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,kt=t;else for(;kt!==null;){switch(e=kt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var I=Vl(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(I,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(W){_t(l,l.return,W)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)vr(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":vr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,kt=t;break}kt=e.return}}function Nf(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:gl(t,l),a&4&&xn(5,l);break;case 1:if(gl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(i){_t(l,l.return,i)}else{var n=Vl(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(i){_t(l,l.return,i)}}a&64&&xf(l),a&512&&An(l,l.return);break;case 3:if(gl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{cs(t,e)}catch(i){_t(l,l.return,i)}}break;case 27:e===null&&a&4&&zf(l);case 26:case 5:gl(t,l),e===null&&a&4&&Tf(l),a&512&&An(l,l.return);break;case 12:gl(t,l);break;case 13:gl(t,l),a&4&&Of(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Sv.bind(null,l),qv(t,l))));break;case 22:if(a=l.memoizedState!==null||$e,!a){e=e!==null&&e.memoizedState!==null||qt,n=$e;var u=qt;$e=a,(qt=e)&&!u?yl(t,l,(l.subtreeFlags&8772)!==0):gl(t,l),$e=n,qt=u}break;case 30:break;default:gl(t,l)}}function Mf(t){var e=t.alternate;e!==null&&(t.alternate=null,Mf(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Si(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ot=null,ne=!1;function Fe(t,e,l){for(l=l.child;l!==null;)Rf(t,e,l),l=l.sibling}function Rf(t,e,l){if(ce&&typeof ce.onCommitFiberUnmount=="function")try{ce.onCommitFiberUnmount(La,l)}catch{}switch(l.tag){case 26:qt||je(l,e),Fe(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:qt||je(l,e);var a=Ot,n=ne;Tl(l.type)&&(Ot=l.stateNode,ne=!1),Fe(t,e,l),Dn(l.stateNode),Ot=a,ne=n;break;case 5:qt||je(l,e);case 6:if(a=Ot,n=ne,Ot=null,Fe(t,e,l),Ot=a,ne=n,Ot!==null)if(ne)try{(Ot.nodeType===9?Ot.body:Ot.nodeName==="HTML"?Ot.ownerDocument.body:Ot).removeChild(l.stateNode)}catch(u){_t(l,e,u)}else try{Ot.removeChild(l.stateNode)}catch(u){_t(l,e,u)}break;case 18:Ot!==null&&(ne?(t=Ot,yd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Yn(t)):yd(Ot,l.stateNode));break;case 4:a=Ot,n=ne,Ot=l.stateNode.containerInfo,ne=!0,Fe(t,e,l),Ot=a,ne=n;break;case 0:case 11:case 14:case 15:qt||vl(2,l,e),qt||vl(4,l,e),Fe(t,e,l);break;case 1:qt||(je(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Af(l,e,a)),Fe(t,e,l);break;case 21:Fe(t,e,l);break;case 22:qt=(a=qt)||l.memoizedState!==null,Fe(t,e,l),qt=a;break;default:Fe(t,e,l)}}function Of(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Yn(t)}catch(l){_t(e,e.return,l)}}function dv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new _f),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new _f),e;default:throw Error(o(435,t.tag))}}function Lc(t,e){var l=dv(t);e.forEach(function(a){var n=xv.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function fe(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,i=e,r=i;t:for(;r!==null;){switch(r.tag){case 27:if(Tl(r.type)){Ot=r.stateNode,ne=!1;break t}break;case 5:Ot=r.stateNode,ne=!1;break t;case 3:case 4:Ot=r.stateNode.containerInfo,ne=!0;break t}r=r.return}if(Ot===null)throw Error(o(160));Rf(u,i,n),Ot=null,ne=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Df(e,t),e=e.sibling}var Re=null;function Df(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:fe(e,t),de(t),a&4&&(vl(3,t,t.return),xn(3,t),vl(5,t,t.return));break;case 1:fe(e,t),de(t),a&512&&(qt||l===null||je(l,l.return)),a&64&&$e&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Re;if(fe(e,t),de(t),a&512&&(qt||l===null||je(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Za]||u[Ft]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),$t(u,a,l),u[Ft]=t,Vt(u),a=u;break t;case"link":var i=zd("link","href",n).get(a+(l.href||""));if(i){for(var r=0;r<i.length;r++)if(u=i[r],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(r,1);break e}}u=n.createElement(a),$t(u,a,l),n.head.appendChild(u);break;case"meta":if(i=zd("meta","content",n).get(a+(l.content||""))){for(r=0;r<i.length;r++)if(u=i[r],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(r,1);break e}}u=n.createElement(a),$t(u,a,l),n.head.appendChild(u);break;default:throw Error(o(468,a))}u[Ft]=t,Vt(u),a=u}t.stateNode=a}else _d(n,t.type,t.stateNode);else t.stateNode=Ed(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?_d(n,t.type,t.stateNode):Ed(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&qc(t,t.memoizedProps,l.memoizedProps)}break;case 27:fe(e,t),de(t),a&512&&(qt||l===null||je(l,l.return)),l!==null&&a&4&&qc(t,t.memoizedProps,l.memoizedProps);break;case 5:if(fe(e,t),de(t),a&512&&(qt||l===null||je(l,l.return)),t.flags&32){n=t.stateNode;try{ua(n,"")}catch(z){_t(t,t.return,z)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,qc(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Xc=!0);break;case 6:if(fe(e,t),de(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(z){_t(t,t.return,z)}}break;case 3:if(Wu=null,n=Re,Re=Ku(e.containerInfo),fe(e,t),Re=n,de(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Yn(e.containerInfo)}catch(z){_t(t,t.return,z)}Xc&&(Xc=!1,wf(t));break;case 4:a=Re,Re=Ku(t.stateNode.containerInfo),fe(e,t),de(t),Re=a;break;case 12:fe(e,t),de(t);break;case 13:fe(e,t),de(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Jc=we()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Lc(t,a)));break;case 22:n=t.memoizedState!==null;var d=l!==null&&l.memoizedState!==null,T=$e,R=qt;if($e=T||n,qt=R||d,fe(e,t),qt=R,$e=T,de(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||d||$e||qt||Zl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){d=l=e;try{if(u=d.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{r=d.stateNode;var w=d.memoizedProps.style,E=w!=null&&w.hasOwnProperty("display")?w.display:null;r.style.display=E==null||typeof E=="boolean"?"":(""+E).trim()}}catch(z){_t(d,d.return,z)}}}else if(e.tag===6){if(l===null){d=e;try{d.stateNode.nodeValue=n?"":d.memoizedProps}catch(z){_t(d,d.return,z)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Lc(t,l))));break;case 19:fe(e,t),de(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Lc(t,a)));break;case 30:break;case 21:break;default:fe(e,t),de(t)}}function de(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Ef(a)){l=a;break}a=a.return}if(l==null)throw Error(o(160));switch(l.tag){case 27:var n=l.stateNode,u=Yc(t);Uu(t,u,n);break;case 5:var i=l.stateNode;l.flags&32&&(ua(i,""),l.flags&=-33);var r=Yc(t);Uu(t,r,i);break;case 3:case 4:var d=l.stateNode.containerInfo,T=Yc(t);Gc(t,T,d);break;default:throw Error(o(161))}}catch(R){_t(t,t.return,R)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function wf(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;wf(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function gl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Nf(t,e.alternate,e),e=e.sibling}function Zl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:vl(4,e,e.return),Zl(e);break;case 1:je(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Af(e,e.return,l),Zl(e);break;case 27:Dn(e.stateNode);case 26:case 5:je(e,e.return),Zl(e);break;case 22:e.memoizedState===null&&Zl(e);break;case 30:Zl(e);break;default:Zl(e)}t=t.sibling}}function yl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,i=u.flags;switch(u.tag){case 0:case 11:case 15:yl(n,u,l),xn(4,u);break;case 1:if(yl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(T){_t(a,a.return,T)}if(a=u,n=a.updateQueue,n!==null){var r=a.stateNode;try{var d=n.shared.hiddenCallbacks;if(d!==null)for(n.shared.hiddenCallbacks=null,n=0;n<d.length;n++)is(d[n],r)}catch(T){_t(a,a.return,T)}}l&&i&64&&xf(u),An(u,u.return);break;case 27:zf(u);case 26:case 5:yl(n,u,l),l&&a===null&&i&4&&Tf(u),An(u,u.return);break;case 12:yl(n,u,l);break;case 13:yl(n,u,l),l&&i&4&&Of(n,u);break;case 22:u.memoizedState===null&&yl(n,u,l),An(u,u.return);break;case 30:break;default:yl(n,u,l)}e=e.sibling}}function Qc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&cn(l))}function Vc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&cn(t))}function He(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Cf(t,e,l,a),e=e.sibling}function Cf(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:He(t,e,l,a),n&2048&&xn(9,e);break;case 1:He(t,e,l,a);break;case 3:He(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&cn(t)));break;case 12:if(n&2048){He(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,i=u.id,r=u.onPostCommit;typeof r=="function"&&r(i,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(d){_t(e,e.return,d)}}else He(t,e,l,a);break;case 13:He(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,i=e.alternate,e.memoizedState!==null?u._visibility&2?He(t,e,l,a):Tn(t,e):u._visibility&2?He(t,e,l,a):(u._visibility|=2,Ea(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Qc(i,e);break;case 24:He(t,e,l,a),n&2048&&Vc(e.alternate,e);break;default:He(t,e,l,a)}}function Ea(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,i=e,r=l,d=a,T=i.flags;switch(i.tag){case 0:case 11:case 15:Ea(u,i,r,d,n),xn(8,i);break;case 23:break;case 22:var R=i.stateNode;i.memoizedState!==null?R._visibility&2?Ea(u,i,r,d,n):Tn(u,i):(R._visibility|=2,Ea(u,i,r,d,n)),n&&T&2048&&Qc(i.alternate,i);break;case 24:Ea(u,i,r,d,n),n&&T&2048&&Vc(i.alternate,i);break;default:Ea(u,i,r,d,n)}e=e.sibling}}function Tn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:Tn(l,a),n&2048&&Qc(a.alternate,a);break;case 24:Tn(l,a),n&2048&&Vc(a.alternate,a);break;default:Tn(l,a)}e=e.sibling}}var En=8192;function za(t){if(t.subtreeFlags&En)for(t=t.child;t!==null;)Uf(t),t=t.sibling}function Uf(t){switch(t.tag){case 26:za(t),t.flags&En&&t.memoizedState!==null&&Fv(Re,t.memoizedState,t.memoizedProps);break;case 5:za(t);break;case 3:case 4:var e=Re;Re=Ku(t.stateNode.containerInfo),za(t),Re=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=En,En=16777216,za(t),En=e):za(t));break;default:za(t)}}function jf(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function zn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];kt=a,Bf(a,t)}jf(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hf(t),t=t.sibling}function Hf(t){switch(t.tag){case 0:case 11:case 15:zn(t),t.flags&2048&&vl(9,t,t.return);break;case 3:zn(t);break;case 12:zn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ju(t)):zn(t);break;default:zn(t)}}function ju(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];kt=a,Bf(a,t)}jf(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:vl(8,e,e.return),ju(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,ju(e));break;default:ju(e)}t=t.sibling}}function Bf(t,e){for(;kt!==null;){var l=kt;switch(l.tag){case 0:case 11:case 15:vl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:cn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,kt=a;else t:for(l=t;kt!==null;){a=kt;var n=a.sibling,u=a.return;if(Mf(a),a===l){kt=null;break t}if(n!==null){n.return=u,kt=n;break t}kt=u}}}var mv={getCacheForType:function(t){var e=Pt(Xt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},hv=typeof WeakMap=="function"?WeakMap:Map,bt=0,Nt=null,ft=null,mt=0,pt=0,me=null,bl=!1,_a=!1,Zc=!1,Pe=0,jt=0,pl=0,kl=0,kc=0,Ee=0,Na=0,_n=null,ue=null,Kc=!1,Jc=0,Hu=1/0,Bu=null,Sl=null,Wt=0,xl=null,Ma=null,Ra=0,Wc=0,$c=null,qf=null,Nn=0,Fc=null;function he(){if((bt&2)!==0&&mt!==0)return mt&-mt;if(x.T!==null){var t=ga;return t!==0?t:nr()}return Pr()}function Yf(){Ee===0&&(Ee=(mt&536870912)===0||gt?Jr():536870912);var t=Te.current;return t!==null&&(t.flags|=32),Ee}function ve(t,e,l){(t===Nt&&(pt===2||pt===9)||t.cancelPendingCommit!==null)&&(Oa(t,0),Al(t,mt,Ee,!1)),Va(t,l),((bt&2)===0||t!==Nt)&&(t===Nt&&((bt&2)===0&&(kl|=l),jt===4&&Al(t,mt,Ee,!1)),Be(t))}function Gf(t,e,l){if((bt&6)!==0)throw Error(o(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Qa(t,e),n=a?yv(t,e):tr(t,e,!0),u=a;do{if(n===0){_a&&!a&&Al(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!vv(l)){n=tr(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){e=i;t:{var r=t;n=_n;var d=r.current.memoizedState.isDehydrated;if(d&&(Oa(r,i).flags|=256),i=tr(r,i,!1),i!==2){if(Zc&&!d){r.errorRecoveryDisabledLanes|=u,kl|=u,n=4;break t}u=ue,ue=n,u!==null&&(ue===null?ue=u:ue.push.apply(ue,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){Oa(t,0),Al(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Al(a,e,Ee,!bl);break t;case 2:ue=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(n=Jc+300-we(),10<n)){if(Al(a,e,Ee,!bl),Jn(a,0,!0)!==0)break t;a.timeoutHandle=vd(Xf.bind(null,a,l,ue,Bu,Kc,e,Ee,kl,Na,bl,u,2,-0,0),n);break t}Xf(a,l,ue,Bu,Kc,e,Ee,kl,Na,bl,u,0,-0,0)}}break}while(!0);Be(t)}function Xf(t,e,l,a,n,u,i,r,d,T,R,w,E,z){if(t.timeoutHandle=-1,w=e.subtreeFlags,(w&8192||(w&16785408)===16785408)&&(Un={stylesheets:null,count:0,unsuspend:$v},Uf(e),w=Pv(),w!==null)){t.cancelPendingCommit=w(Jf.bind(null,t,e,u,l,a,n,i,r,d,R,1,E,z)),Al(t,u,i,!T);return}Jf(t,e,u,l,a,n,i,r,d)}function vv(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!oe(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Al(t,e,l,a){e&=~kc,e&=~kl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-re(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&$r(t,l,e)}function qu(){return(bt&6)===0?(Mn(0),!1):!0}function Pc(){if(ft!==null){if(pt===0)var t=ft.return;else t=ft,Ve=Gl=null,vc(t),Aa=null,bn=0,t=ft;for(;t!==null;)Sf(t.alternate,t),t=t.return;ft=null}}function Oa(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Cv(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Pc(),Nt=t,ft=l=Xe(t.current,null),mt=e,pt=0,me=null,bl=!1,_a=Qa(t,e),Zc=!1,Na=Ee=kc=kl=pl=jt=0,ue=_n=null,Kc=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-re(a),u=1<<n;e|=t[n],a&=~u}return Pe=e,iu(),l}function Lf(t,e){it=null,x.H=zu,e===on||e===vu?(e=ns(),pt=3):e===es?(e=ns(),pt=4):pt=e===uf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,me=e,ft===null&&(jt=1,Ou(t,pe(e,t.current)))}function Qf(){var t=x.H;return x.H=zu,t===null?zu:t}function Vf(){var t=x.A;return x.A=mv,t}function Ic(){jt=4,bl||(mt&4194048)!==mt&&Te.current!==null||(_a=!0),(pl&134217727)===0&&(kl&134217727)===0||Nt===null||Al(Nt,mt,Ee,!1)}function tr(t,e,l){var a=bt;bt|=2;var n=Qf(),u=Vf();(Nt!==t||mt!==e)&&(Bu=null,Oa(t,e)),e=!1;var i=jt;t:do try{if(pt!==0&&ft!==null){var r=ft,d=me;switch(pt){case 8:Pc(),i=6;break t;case 3:case 2:case 9:case 6:Te.current===null&&(e=!0);var T=pt;if(pt=0,me=null,Da(t,r,d,T),l&&_a){i=0;break t}break;default:T=pt,pt=0,me=null,Da(t,r,d,T)}}gv(),i=jt;break}catch(R){Lf(t,R)}while(!0);return e&&t.shellSuspendCounter++,Ve=Gl=null,bt=a,x.H=n,x.A=u,ft===null&&(Nt=null,mt=0,iu()),i}function gv(){for(;ft!==null;)Zf(ft)}function yv(t,e){var l=bt;bt|=2;var a=Qf(),n=Vf();Nt!==t||mt!==e?(Bu=null,Hu=we()+500,Oa(t,e)):_a=Qa(t,e);t:do try{if(pt!==0&&ft!==null){e=ft;var u=me;e:switch(pt){case 1:pt=0,me=null,Da(t,e,u,1);break;case 2:case 9:if(ls(u)){pt=0,me=null,kf(e);break}e=function(){pt!==2&&pt!==9||Nt!==t||(pt=7),Be(t)},u.then(e,e);break t;case 3:pt=7;break t;case 4:pt=5;break t;case 7:ls(u)?(pt=0,me=null,kf(e)):(pt=0,me=null,Da(t,e,u,7));break;case 5:var i=null;switch(ft.tag){case 26:i=ft.memoizedState;case 5:case 27:var r=ft;if(!i||Nd(i)){pt=0,me=null;var d=r.sibling;if(d!==null)ft=d;else{var T=r.return;T!==null?(ft=T,Yu(T)):ft=null}break e}}pt=0,me=null,Da(t,e,u,5);break;case 6:pt=0,me=null,Da(t,e,u,6);break;case 8:Pc(),jt=6;break t;default:throw Error(o(462))}}bv();break}catch(R){Lf(t,R)}while(!0);return Ve=Gl=null,x.H=a,x.A=n,bt=l,ft!==null?0:(Nt=null,mt=0,iu(),jt)}function bv(){for(;ft!==null&&!Gm();)Zf(ft)}function Zf(t){var e=bf(t.alternate,t,Pe);t.memoizedProps=t.pendingProps,e===null?Yu(t):ft=e}function kf(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=df(l,e,e.pendingProps,e.type,void 0,mt);break;case 11:e=df(l,e,e.pendingProps,e.type.render,e.ref,mt);break;case 5:vc(e);default:Sf(l,e),e=ft=ko(e,Pe),e=bf(l,e,Pe)}t.memoizedProps=t.pendingProps,e===null?Yu(t):ft=e}function Da(t,e,l,a){Ve=Gl=null,vc(e),Aa=null,bn=0;var n=e.return;try{if(cv(t,n,e,l,mt)){jt=1,Ou(t,pe(l,t.current)),ft=null;return}}catch(u){if(n!==null)throw ft=n,u;jt=1,Ou(t,pe(l,t.current)),ft=null;return}e.flags&32768?(gt||a===1?t=!0:_a||(mt&536870912)!==0?t=!1:(bl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Te.current,a!==null&&a.tag===13&&(a.flags|=16384))),Kf(e,t)):Yu(e)}function Yu(t){var e=t;do{if((e.flags&32768)!==0){Kf(e,bl);return}t=e.return;var l=ov(e.alternate,e,Pe);if(l!==null){ft=l;return}if(e=e.sibling,e!==null){ft=e;return}ft=e=t}while(e!==null);jt===0&&(jt=5)}function Kf(t,e){do{var l=sv(t.alternate,t);if(l!==null){l.flags&=32767,ft=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ft=t;return}ft=t=l}while(t!==null);jt=6,ft=null}function Jf(t,e,l,a,n,u,i,r,d){t.cancelPendingCommit=null;do Gu();while(Wt!==0);if((bt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(u=e.lanes|e.childLanes,u|=Vi,$m(t,l,u,i,r,d),t===Nt&&(ft=Nt=null,mt=0),Ma=e,xl=t,Ra=l,Wc=u,$c=n,qf=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Av(Zn,function(){return If(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=x.T,x.T=null,n=H.p,H.p=2,i=bt,bt|=4;try{fv(t,e,l)}finally{bt=i,H.p=n,x.T=a}}Wt=1,Wf(),$f(),Ff()}}function Wf(){if(Wt===1){Wt=0;var t=xl,e=Ma,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=x.T,x.T=null;var a=H.p;H.p=2;var n=bt;bt|=4;try{Df(e,t);var u=dr,i=Ho(t.containerInfo),r=u.focusedElem,d=u.selectionRange;if(i!==r&&r&&r.ownerDocument&&jo(r.ownerDocument.documentElement,r)){if(d!==null&&Yi(r)){var T=d.start,R=d.end;if(R===void 0&&(R=T),"selectionStart"in r)r.selectionStart=T,r.selectionEnd=Math.min(R,r.value.length);else{var w=r.ownerDocument||document,E=w&&w.defaultView||window;if(E.getSelection){var z=E.getSelection(),I=r.textContent.length,W=Math.min(d.start,I),Tt=d.end===void 0?W:Math.min(d.end,I);!z.extend&&W>Tt&&(i=Tt,Tt=W,W=i);var y=Uo(r,W),v=Uo(r,Tt);if(y&&v&&(z.rangeCount!==1||z.anchorNode!==y.node||z.anchorOffset!==y.offset||z.focusNode!==v.node||z.focusOffset!==v.offset)){var A=w.createRange();A.setStart(y.node,y.offset),z.removeAllRanges(),W>Tt?(z.addRange(A),z.extend(v.node,v.offset)):(A.setEnd(v.node,v.offset),z.addRange(A))}}}}for(w=[],z=r;z=z.parentNode;)z.nodeType===1&&w.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<w.length;r++){var O=w[r];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}Pu=!!fr,dr=fr=null}finally{bt=n,H.p=a,x.T=l}}t.current=e,Wt=2}}function $f(){if(Wt===2){Wt=0;var t=xl,e=Ma,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=x.T,x.T=null;var a=H.p;H.p=2;var n=bt;bt|=4;try{Nf(t,e.alternate,e)}finally{bt=n,H.p=a,x.T=l}}Wt=3}}function Ff(){if(Wt===4||Wt===3){Wt=0,Xm();var t=xl,e=Ma,l=Ra,a=qf;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Wt=5:(Wt=0,Ma=xl=null,Pf(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Sl=null),bi(l),e=e.stateNode,ce&&typeof ce.onCommitFiberRoot=="function")try{ce.onCommitFiberRoot(La,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=x.T,n=H.p,H.p=2,x.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var r=a[i];u(r.value,{componentStack:r.stack})}}finally{x.T=e,H.p=n}}(Ra&3)!==0&&Gu(),Be(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Fc?Nn++:(Nn=0,Fc=t):Nn=0,Mn(0)}}function Pf(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,cn(e)))}function Gu(t){return Wf(),$f(),Ff(),If()}function If(){if(Wt!==5)return!1;var t=xl,e=Wc;Wc=0;var l=bi(Ra),a=x.T,n=H.p;try{H.p=32>l?32:l,x.T=null,l=$c,$c=null;var u=xl,i=Ra;if(Wt=0,Ma=xl=null,Ra=0,(bt&6)!==0)throw Error(o(331));var r=bt;if(bt|=4,Hf(u.current),Cf(u,u.current,i,l),bt=r,Mn(0,!1),ce&&typeof ce.onPostCommitFiberRoot=="function")try{ce.onPostCommitFiberRoot(La,u)}catch{}return!0}finally{H.p=n,x.T=a,Pf(t,e)}}function td(t,e,l){e=pe(l,e),e=Rc(t.stateNode,e,2),t=fl(t,e,2),t!==null&&(Va(t,2),Be(t))}function _t(t,e,l){if(t.tag===3)td(t,t,l);else for(;e!==null;){if(e.tag===3){td(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Sl===null||!Sl.has(a))){t=pe(l,t),l=af(2),a=fl(e,l,2),a!==null&&(nf(l,a,e,t),Va(a,2),Be(a));break}}e=e.return}}function er(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new hv;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Zc=!0,n.add(l),t=pv.bind(null,t,e,l),e.then(t,t))}function pv(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Nt===t&&(mt&l)===l&&(jt===4||jt===3&&(mt&62914560)===mt&&300>we()-Jc?(bt&2)===0&&Oa(t,0):kc|=l,Na===mt&&(Na=0)),Be(t)}function ed(t,e){e===0&&(e=Wr()),t=da(t,e),t!==null&&(Va(t,e),Be(t))}function Sv(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),ed(t,l)}function xv(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),ed(t,l)}function Av(t,e){return hi(t,e)}var Xu=null,wa=null,lr=!1,Lu=!1,ar=!1,Kl=0;function Be(t){t!==wa&&t.next===null&&(wa===null?Xu=wa=t:wa=wa.next=t),Lu=!0,lr||(lr=!0,Ev())}function Mn(t,e){if(!ar&&Lu){ar=!0;do for(var l=!1,a=Xu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,r=a.pingedLanes;u=(1<<31-re(42|t)+1)-1,u&=n&~(i&~r),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,ud(a,u))}else u=mt,u=Jn(a,a===Nt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||Qa(a,u)||(l=!0,ud(a,u));a=a.next}while(l);ar=!1}}function Tv(){ld()}function ld(){Lu=lr=!1;var t=0;Kl!==0&&(wv()&&(t=Kl),Kl=0);for(var e=we(),l=null,a=Xu;a!==null;){var n=a.next,u=ad(a,e);u===0?(a.next=null,l===null?Xu=n:l.next=n,n===null&&(wa=l)):(l=a,(t!==0||(u&3)!==0)&&(Lu=!0)),a=n}Mn(t)}function ad(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-re(u),r=1<<i,d=n[i];d===-1?((r&l)===0||(r&a)!==0)&&(n[i]=Wm(r,e)):d<=e&&(t.expiredLanes|=r),u&=~r}if(e=Nt,l=mt,l=Jn(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(pt===2||pt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&vi(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Qa(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&vi(a),bi(l)){case 2:case 8:l=kr;break;case 32:l=Zn;break;case 268435456:l=Kr;break;default:l=Zn}return a=nd.bind(null,t),l=hi(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&vi(a),t.callbackPriority=2,t.callbackNode=null,2}function nd(t,e){if(Wt!==0&&Wt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Gu()&&t.callbackNode!==l)return null;var a=mt;return a=Jn(t,t===Nt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Gf(t,a,e),ad(t,we()),t.callbackNode!=null&&t.callbackNode===l?nd.bind(null,t):null)}function ud(t,e){if(Gu())return null;Gf(t,e,!0)}function Ev(){Uv(function(){(bt&6)!==0?hi(Zr,Tv):ld()})}function nr(){return Kl===0&&(Kl=Jr()),Kl}function id(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:In(""+t)}function cd(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function zv(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=id((n[ee]||null).action),i=a.submitter;i&&(e=(e=i[ee]||null)?id(e.formAction):i.getAttribute("formAction"),e!==null&&(u=e,i=null));var r=new au("action","action",null,a,n);t.push({event:r,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Kl!==0){var d=i?cd(n,i):new FormData(n);Ec(l,{pending:!0,data:d,method:n.method,action:u},null,d)}}else typeof u=="function"&&(r.preventDefault(),d=i?cd(n,i):new FormData(n),Ec(l,{pending:!0,data:d,method:n.method,action:u},u,d))},currentTarget:n}]})}}for(var ur=0;ur<Qi.length;ur++){var ir=Qi[ur],_v=ir.toLowerCase(),Nv=ir[0].toUpperCase()+ir.slice(1);Me(_v,"on"+Nv)}Me(Yo,"onAnimationEnd"),Me(Go,"onAnimationIteration"),Me(Xo,"onAnimationStart"),Me("dblclick","onDoubleClick"),Me("focusin","onFocus"),Me("focusout","onBlur"),Me(Vh,"onTransitionRun"),Me(Zh,"onTransitionStart"),Me(kh,"onTransitionCancel"),Me(Lo,"onTransitionEnd"),la("onMouseEnter",["mouseout","mouseover"]),la("onMouseLeave",["mouseout","mouseover"]),la("onPointerEnter",["pointerout","pointerover"]),la("onPointerLeave",["pointerout","pointerover"]),Dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Mv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Rn));function rd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var i=a.length-1;0<=i;i--){var r=a[i],d=r.instance,T=r.currentTarget;if(r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=T;try{u(n)}catch(R){Ru(R)}n.currentTarget=null,u=d}else for(i=0;i<a.length;i++){if(r=a[i],d=r.instance,T=r.currentTarget,r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=T;try{u(n)}catch(R){Ru(R)}n.currentTarget=null,u=d}}}}function dt(t,e){var l=e[pi];l===void 0&&(l=e[pi]=new Set);var a=t+"__bubble";l.has(a)||(od(e,t,2,!1),l.add(a))}function cr(t,e,l){var a=0;e&&(a|=4),od(l,t,a,e)}var Qu="_reactListening"+Math.random().toString(36).slice(2);function rr(t){if(!t[Qu]){t[Qu]=!0,to.forEach(function(l){l!=="selectionchange"&&(Mv.has(l)||cr(l,!1,t),cr(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Qu]||(e[Qu]=!0,cr("selectionchange",!1,e))}}function od(t,e,l,a){switch(Cd(e)){case 2:var n=e0;break;case 8:n=l0;break;default:n=Ar}l=n.bind(null,e,l,t),n=void 0,!Oi||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function or(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var r=a.stateNode.containerInfo;if(r===n)break;if(i===4)for(i=a.return;i!==null;){var d=i.tag;if((d===3||d===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;r!==null;){if(i=Il(r),i===null)return;if(d=i.tag,d===5||d===6||d===26||d===27){a=u=i;continue t}r=r.parentNode}}a=a.return}vo(function(){var T=u,R=Mi(l),w=[];t:{var E=Qo.get(t);if(E!==void 0){var z=au,I=t;switch(t){case"keypress":if(eu(l)===0)break t;case"keydown":case"keyup":z=Ah;break;case"focusin":I="focus",z=Ui;break;case"focusout":I="blur",z=Ui;break;case"beforeblur":case"afterblur":z=Ui;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=bo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=sh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=zh;break;case Yo:case Go:case Xo:z=mh;break;case Lo:z=Nh;break;case"scroll":case"scrollend":z=rh;break;case"wheel":z=Rh;break;case"copy":case"cut":case"paste":z=vh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=So;break;case"toggle":case"beforetoggle":z=Dh}var W=(e&4)!==0,Tt=!W&&(t==="scroll"||t==="scrollend"),y=W?E!==null?E+"Capture":null:E;W=[];for(var v=T,A;v!==null;){var O=v;if(A=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||A===null||y===null||(O=Ka(v,y),O!=null&&W.push(On(v,O,A))),Tt)break;v=v.return}0<W.length&&(E=new z(E,I,null,l,R),w.push({event:E,listeners:W}))}}if((e&7)===0){t:{if(E=t==="mouseover"||t==="pointerover",z=t==="mouseout"||t==="pointerout",E&&l!==Ni&&(I=l.relatedTarget||l.fromElement)&&(Il(I)||I[Pl]))break t;if((z||E)&&(E=R.window===R?R:(E=R.ownerDocument)?E.defaultView||E.parentWindow:window,z?(I=l.relatedTarget||l.toElement,z=T,I=I?Il(I):null,I!==null&&(Tt=b(I),W=I.tag,I!==Tt||W!==5&&W!==27&&W!==6)&&(I=null)):(z=null,I=T),z!==I)){if(W=bo,O="onMouseLeave",y="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(W=So,O="onPointerLeave",y="onPointerEnter",v="pointer"),Tt=z==null?E:ka(z),A=I==null?E:ka(I),E=new W(O,v+"leave",z,l,R),E.target=Tt,E.relatedTarget=A,O=null,Il(R)===T&&(W=new W(y,v+"enter",I,l,R),W.target=A,W.relatedTarget=Tt,O=W),Tt=O,z&&I)e:{for(W=z,y=I,v=0,A=W;A;A=Ca(A))v++;for(A=0,O=y;O;O=Ca(O))A++;for(;0<v-A;)W=Ca(W),v--;for(;0<A-v;)y=Ca(y),A--;for(;v--;){if(W===y||y!==null&&W===y.alternate)break e;W=Ca(W),y=Ca(y)}W=null}else W=null;z!==null&&sd(w,E,z,W,!1),I!==null&&Tt!==null&&sd(w,Tt,I,W,!0)}}t:{if(E=T?ka(T):window,z=E.nodeName&&E.nodeName.toLowerCase(),z==="select"||z==="input"&&E.type==="file")var X=Mo;else if(_o(E))if(Ro)X=Xh;else{X=Yh;var rt=qh}else z=E.nodeName,!z||z.toLowerCase()!=="input"||E.type!=="checkbox"&&E.type!=="radio"?T&&_i(T.elementType)&&(X=Mo):X=Gh;if(X&&(X=X(t,T))){No(w,X,l,R);break t}rt&&rt(t,E,T),t==="focusout"&&T&&E.type==="number"&&T.memoizedProps.value!=null&&zi(E,"number",E.value)}switch(rt=T?ka(T):window,t){case"focusin":(_o(rt)||rt.contentEditable==="true")&&(oa=rt,Gi=T,en=null);break;case"focusout":en=Gi=oa=null;break;case"mousedown":Xi=!0;break;case"contextmenu":case"mouseup":case"dragend":Xi=!1,Bo(w,l,R);break;case"selectionchange":if(Qh)break;case"keydown":case"keyup":Bo(w,l,R)}var K;if(Hi)t:{switch(t){case"compositionstart":var F="onCompositionStart";break t;case"compositionend":F="onCompositionEnd";break t;case"compositionupdate":F="onCompositionUpdate";break t}F=void 0}else ra?Eo(t,l)&&(F="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(F="onCompositionStart");F&&(xo&&l.locale!=="ko"&&(ra||F!=="onCompositionStart"?F==="onCompositionEnd"&&ra&&(K=go()):(cl=R,Di="value"in cl?cl.value:cl.textContent,ra=!0)),rt=Vu(T,F),0<rt.length&&(F=new po(F,t,null,l,R),w.push({event:F,listeners:rt}),K?F.data=K:(K=zo(l),K!==null&&(F.data=K)))),(K=Ch?Uh(t,l):jh(t,l))&&(F=Vu(T,"onBeforeInput"),0<F.length&&(rt=new po("onBeforeInput","beforeinput",null,l,R),w.push({event:rt,listeners:F}),rt.data=K)),zv(w,t,T,l,R)}rd(w,e)})}function On(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Vu(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Ka(t,l),n!=null&&a.unshift(On(t,n,u)),n=Ka(t,e),n!=null&&a.push(On(t,n,u))),t.tag===3)return a;t=t.return}return[]}function Ca(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function sd(t,e,l,a,n){for(var u=e._reactName,i=[];l!==null&&l!==a;){var r=l,d=r.alternate,T=r.stateNode;if(r=r.tag,d!==null&&d===a)break;r!==5&&r!==26&&r!==27||T===null||(d=T,n?(T=Ka(l,u),T!=null&&i.unshift(On(l,T,d))):n||(T=Ka(l,u),T!=null&&i.push(On(l,T,d)))),l=l.return}i.length!==0&&t.push({event:e,listeners:i})}var Rv=/\r\n?/g,Ov=/\u0000|\uFFFD/g;function fd(t){return(typeof t=="string"?t:""+t).replace(Rv,`
`).replace(Ov,"")}function dd(t,e){return e=fd(e),fd(t)===e}function Zu(){}function At(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ua(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ua(t,""+a);break;case"className":$n(t,"class",a);break;case"tabIndex":$n(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":$n(t,l,a);break;case"style":mo(t,a,u);break;case"data":if(e!=="object"){$n(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=In(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&At(t,e,"name",n.name,n,null),At(t,e,"formEncType",n.formEncType,n,null),At(t,e,"formMethod",n.formMethod,n,null),At(t,e,"formTarget",n.formTarget,n,null)):(At(t,e,"encType",n.encType,n,null),At(t,e,"method",n.method,n,null),At(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=In(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=Zu);break;case"onScroll":a!=null&&dt("scroll",t);break;case"onScrollEnd":a!=null&&dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=In(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":dt("beforetoggle",t),dt("toggle",t),Wn(t,"popover",a);break;case"xlinkActuate":Ye(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Ye(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Ye(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Ye(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Ye(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Ye(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Wn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=ih.get(l)||l,Wn(t,l,a))}}function sr(t,e,l,a,n,u){switch(l){case"style":mo(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"children":typeof a=="string"?ua(t,a):(typeof a=="number"||typeof a=="bigint")&&ua(t,""+a);break;case"onScroll":a!=null&&dt("scroll",t);break;case"onScrollEnd":a!=null&&dt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Zu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!eo.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[ee]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Wn(t,l,a)}}}function $t(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":dt("error",t),dt("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:At(t,e,u,i,l,null)}}n&&At(t,e,"srcSet",l.srcSet,l,null),a&&At(t,e,"src",l.src,l,null);return;case"input":dt("invalid",t);var r=u=i=n=null,d=null,T=null;for(a in l)if(l.hasOwnProperty(a)){var R=l[a];if(R!=null)switch(a){case"name":n=R;break;case"type":i=R;break;case"checked":d=R;break;case"defaultChecked":T=R;break;case"value":u=R;break;case"defaultValue":r=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(o(137,e));break;default:At(t,e,a,R,l,null)}}ro(t,u,r,d,T,i,n,!1),Fn(t);return;case"select":dt("invalid",t),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(r=l[n],r!=null))switch(n){case"value":u=r;break;case"defaultValue":i=r;break;case"multiple":a=r;default:At(t,e,n,r,l,null)}e=u,l=i,t.multiple=!!a,e!=null?na(t,!!a,e,!1):l!=null&&na(t,!!a,l,!0);return;case"textarea":dt("invalid",t),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(r=l[i],r!=null))switch(i){case"value":a=r;break;case"defaultValue":n=r;break;case"children":u=r;break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(o(91));break;default:At(t,e,i,r,l,null)}so(t,a,n,u),Fn(t);return;case"option":for(d in l)if(l.hasOwnProperty(d)&&(a=l[d],a!=null))switch(d){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:At(t,e,d,a,l,null)}return;case"dialog":dt("beforetoggle",t),dt("toggle",t),dt("cancel",t),dt("close",t);break;case"iframe":case"object":dt("load",t);break;case"video":case"audio":for(a=0;a<Rn.length;a++)dt(Rn[a],t);break;case"image":dt("error",t),dt("load",t);break;case"details":dt("toggle",t);break;case"embed":case"source":case"link":dt("error",t),dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(T in l)if(l.hasOwnProperty(T)&&(a=l[T],a!=null))switch(T){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:At(t,e,T,a,l,null)}return;default:if(_i(e)){for(R in l)l.hasOwnProperty(R)&&(a=l[R],a!==void 0&&sr(t,e,R,a,l,void 0));return}}for(r in l)l.hasOwnProperty(r)&&(a=l[r],a!=null&&At(t,e,r,a,l,null))}function Dv(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,r=null,d=null,T=null,R=null;for(z in l){var w=l[z];if(l.hasOwnProperty(z)&&w!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":d=w;default:a.hasOwnProperty(z)||At(t,e,z,null,a,w)}}for(var E in a){var z=a[E];if(w=l[E],a.hasOwnProperty(E)&&(z!=null||w!=null))switch(E){case"type":u=z;break;case"name":n=z;break;case"checked":T=z;break;case"defaultChecked":R=z;break;case"value":i=z;break;case"defaultValue":r=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(o(137,e));break;default:z!==w&&At(t,e,E,z,a,w)}}Ei(t,i,r,d,T,R,u,n);return;case"select":z=i=r=E=null;for(u in l)if(d=l[u],l.hasOwnProperty(u)&&d!=null)switch(u){case"value":break;case"multiple":z=d;default:a.hasOwnProperty(u)||At(t,e,u,null,a,d)}for(n in a)if(u=a[n],d=l[n],a.hasOwnProperty(n)&&(u!=null||d!=null))switch(n){case"value":E=u;break;case"defaultValue":r=u;break;case"multiple":i=u;default:u!==d&&At(t,e,n,u,a,d)}e=r,l=i,a=z,E!=null?na(t,!!l,E,!1):!!a!=!!l&&(e!=null?na(t,!!l,e,!0):na(t,!!l,l?[]:"",!1));return;case"textarea":z=E=null;for(r in l)if(n=l[r],l.hasOwnProperty(r)&&n!=null&&!a.hasOwnProperty(r))switch(r){case"value":break;case"children":break;default:At(t,e,r,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":E=n;break;case"defaultValue":z=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(o(91));break;default:n!==u&&At(t,e,i,n,a,u)}oo(t,E,z);return;case"option":for(var I in l)if(E=l[I],l.hasOwnProperty(I)&&E!=null&&!a.hasOwnProperty(I))switch(I){case"selected":t.selected=!1;break;default:At(t,e,I,null,a,E)}for(d in a)if(E=a[d],z=l[d],a.hasOwnProperty(d)&&E!==z&&(E!=null||z!=null))switch(d){case"selected":t.selected=E&&typeof E!="function"&&typeof E!="symbol";break;default:At(t,e,d,E,a,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in l)E=l[W],l.hasOwnProperty(W)&&E!=null&&!a.hasOwnProperty(W)&&At(t,e,W,null,a,E);for(T in a)if(E=a[T],z=l[T],a.hasOwnProperty(T)&&E!==z&&(E!=null||z!=null))switch(T){case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(o(137,e));break;default:At(t,e,T,E,a,z)}return;default:if(_i(e)){for(var Tt in l)E=l[Tt],l.hasOwnProperty(Tt)&&E!==void 0&&!a.hasOwnProperty(Tt)&&sr(t,e,Tt,void 0,a,E);for(R in a)E=a[R],z=l[R],!a.hasOwnProperty(R)||E===z||E===void 0&&z===void 0||sr(t,e,R,E,a,z);return}}for(var y in l)E=l[y],l.hasOwnProperty(y)&&E!=null&&!a.hasOwnProperty(y)&&At(t,e,y,null,a,E);for(w in a)E=a[w],z=l[w],!a.hasOwnProperty(w)||E===z||E==null&&z==null||At(t,e,w,E,a,z)}var fr=null,dr=null;function ku(t){return t.nodeType===9?t:t.ownerDocument}function md(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function hd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function mr(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var hr=null;function wv(){var t=window.event;return t&&t.type==="popstate"?t===hr?!1:(hr=t,!0):(hr=null,!1)}var vd=typeof setTimeout=="function"?setTimeout:void 0,Cv=typeof clearTimeout=="function"?clearTimeout:void 0,gd=typeof Promise=="function"?Promise:void 0,Uv=typeof queueMicrotask=="function"?queueMicrotask:typeof gd<"u"?function(t){return gd.resolve(null).then(t).catch(jv)}:vd;function jv(t){setTimeout(function(){throw t})}function Tl(t){return t==="head"}function yd(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var i=t.ownerDocument;if(l&1&&Dn(i.documentElement),l&2&&Dn(i.body),l&4)for(l=i.head,Dn(l),i=l.firstChild;i;){var r=i.nextSibling,d=i.nodeName;i[Za]||d==="SCRIPT"||d==="STYLE"||d==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=r}}if(n===0){t.removeChild(u),Yn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);Yn(e)}function vr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":vr(l),Si(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Hv(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Za])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Oe(t.nextSibling),t===null)break}return null}function Bv(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Oe(t.nextSibling),t===null))return null;return t}function gr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function qv(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Oe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var yr=null;function bd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function pd(t,e,l){switch(e=ku(l),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Dn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Si(t)}var ze=new Map,Sd=new Set;function Ku(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ie=H.d;H.d={f:Yv,r:Gv,D:Xv,C:Lv,L:Qv,m:Vv,X:kv,S:Zv,M:Kv};function Yv(){var t=Ie.f(),e=qu();return t||e}function Gv(t){var e=ta(t);e!==null&&e.tag===5&&e.type==="form"?Ys(e):Ie.r(t)}var Ua=typeof document>"u"?null:document;function xd(t,e,l){var a=Ua;if(a&&typeof e=="string"&&e){var n=be(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),Sd.has(n)||(Sd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),$t(e,"link",t),Vt(e),a.head.appendChild(e)))}}function Xv(t){Ie.D(t),xd("dns-prefetch",t,null)}function Lv(t,e){Ie.C(t,e),xd("preconnect",t,e)}function Qv(t,e,l){Ie.L(t,e,l);var a=Ua;if(a&&t&&e){var n='link[rel="preload"][as="'+be(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+be(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+be(l.imageSizes)+'"]')):n+='[href="'+be(t)+'"]';var u=n;switch(e){case"style":u=ja(t);break;case"script":u=Ha(t)}ze.has(u)||(t=N({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),ze.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(wn(u))||e==="script"&&a.querySelector(Cn(u))||(e=a.createElement("link"),$t(e,"link",t),Vt(e),a.head.appendChild(e)))}}function Vv(t,e){Ie.m(t,e);var l=Ua;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+be(a)+'"][href="'+be(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ha(t)}if(!ze.has(u)&&(t=N({rel:"modulepreload",href:t},e),ze.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Cn(u)))return}a=l.createElement("link"),$t(a,"link",t),Vt(a),l.head.appendChild(a)}}}function Zv(t,e,l){Ie.S(t,e,l);var a=Ua;if(a&&t){var n=ea(a).hoistableStyles,u=ja(t);e=e||"default";var i=n.get(u);if(!i){var r={loading:0,preload:null};if(i=a.querySelector(wn(u)))r.loading=5;else{t=N({rel:"stylesheet",href:t,"data-precedence":e},l),(l=ze.get(u))&&br(t,l);var d=i=a.createElement("link");Vt(d),$t(d,"link",t),d._p=new Promise(function(T,R){d.onload=T,d.onerror=R}),d.addEventListener("load",function(){r.loading|=1}),d.addEventListener("error",function(){r.loading|=2}),r.loading|=4,Ju(i,e,a)}i={type:"stylesheet",instance:i,count:1,state:r},n.set(u,i)}}}function kv(t,e){Ie.X(t,e);var l=Ua;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(Cn(n)),u||(t=N({src:t,async:!0},e),(e=ze.get(n))&&pr(t,e),u=l.createElement("script"),Vt(u),$t(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Kv(t,e){Ie.M(t,e);var l=Ua;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(Cn(n)),u||(t=N({src:t,async:!0,type:"module"},e),(e=ze.get(n))&&pr(t,e),u=l.createElement("script"),Vt(u),$t(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Ad(t,e,l,a){var n=(n=tt.current)?Ku(n):null;if(!n)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=ja(l.href),l=ea(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=ja(l.href);var u=ea(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(wn(t)))&&!u._p&&(i.instance=u,i.state.loading=5),ze.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ze.set(t,l),u||Jv(n,t,l,i.state))),e&&a===null)throw Error(o(528,""));return i}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ha(l),l=ea(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function ja(t){return'href="'+be(t)+'"'}function wn(t){return'link[rel="stylesheet"]['+t+"]"}function Td(t){return N({},t,{"data-precedence":t.precedence,precedence:null})}function Jv(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),$t(e,"link",l),Vt(e),t.head.appendChild(e))}function Ha(t){return'[src="'+be(t)+'"]'}function Cn(t){return"script[async]"+t}function Ed(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+be(l.href)+'"]');if(a)return e.instance=a,Vt(a),a;var n=N({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Vt(a),$t(a,"style",n),Ju(a,l.precedence,t),e.instance=a;case"stylesheet":n=ja(l.href);var u=t.querySelector(wn(n));if(u)return e.state.loading|=4,e.instance=u,Vt(u),u;a=Td(l),(n=ze.get(n))&&br(a,n),u=(t.ownerDocument||t).createElement("link"),Vt(u);var i=u;return i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),$t(u,"link",a),e.state.loading|=4,Ju(u,l.precedence,t),e.instance=u;case"script":return u=Ha(l.src),(n=t.querySelector(Cn(u)))?(e.instance=n,Vt(n),n):(a=l,(n=ze.get(u))&&(a=N({},l),pr(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Vt(n),$t(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ju(a,l.precedence,t));return e.instance}function Ju(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var r=a[i];if(r.dataset.precedence===e)u=r;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function br(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function pr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Wu=null;function zd(t,e,l){if(Wu===null){var a=new Map,n=Wu=new Map;n.set(l,a)}else n=Wu,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[Za]||u[Ft]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(e)||"";i=t+i;var r=a.get(i);r?r.push(u):a.set(i,[u])}}return a}function _d(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function Wv(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Nd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Un=null;function $v(){}function Fv(t,e,l){if(Un===null)throw Error(o(475));var a=Un;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=ja(l.href),u=t.querySelector(wn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=$u.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,Vt(u);return}u=t.ownerDocument||t,l=Td(l),(n=ze.get(n))&&br(l,n),u=u.createElement("link"),Vt(u);var i=u;i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),$t(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=$u.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function Pv(){if(Un===null)throw Error(o(475));var t=Un;return t.stylesheets&&t.count===0&&Sr(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&Sr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function $u(){if(this.count--,this.count===0){if(this.stylesheets)Sr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Fu=null;function Sr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Fu=new Map,e.forEach(Iv,t),Fu=null,$u.call(t))}function Iv(t,e){if(!(e.state.loading&4)){var l=Fu.get(t);if(l)var a=l.get(null);else{l=new Map,Fu.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=e.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=$u.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var jn={$$typeof:yt,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function t0(t,e,l,a,n,u,i,r){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=gi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gi(0),this.hiddenUpdates=gi(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=r,this.incompleteTransitions=new Map}function Md(t,e,l,a,n,u,i,r,d,T,R,w){return t=new t0(t,e,l,i,r,d,T,w),e=1,u===!0&&(e|=24),u=se(3,null,null,e),t.current=u,u.stateNode=t,e=ec(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},uc(u),t}function Rd(t){return t?(t=ma,t):ma}function Od(t,e,l,a,n,u){n=Rd(n),a.context===null?a.context=n:a.pendingContext=n,a=sl(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=fl(t,a,e),l!==null&&(ve(l,t,e),fn(l,t,e))}function Dd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function xr(t,e){Dd(t,e),(t=t.alternate)&&Dd(t,e)}function wd(t){if(t.tag===13){var e=da(t,67108864);e!==null&&ve(e,t,67108864),xr(t,67108864)}}var Pu=!0;function e0(t,e,l,a){var n=x.T;x.T=null;var u=H.p;try{H.p=2,Ar(t,e,l,a)}finally{H.p=u,x.T=n}}function l0(t,e,l,a){var n=x.T;x.T=null;var u=H.p;try{H.p=8,Ar(t,e,l,a)}finally{H.p=u,x.T=n}}function Ar(t,e,l,a){if(Pu){var n=Tr(a);if(n===null)or(t,e,a,Iu,l),Ud(t,a);else if(n0(n,t,e,l,a))a.stopPropagation();else if(Ud(t,a),e&4&&-1<a0.indexOf(t)){for(;n!==null;){var u=ta(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=Ol(u.pendingLanes);if(i!==0){var r=u;for(r.pendingLanes|=2,r.entangledLanes|=2;i;){var d=1<<31-re(i);r.entanglements[1]|=d,i&=~d}Be(u),(bt&6)===0&&(Hu=we()+500,Mn(0))}}break;case 13:r=da(u,2),r!==null&&ve(r,u,2),qu(),xr(u,2)}if(u=Tr(a),u===null&&or(t,e,a,Iu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else or(t,e,a,null,l)}}function Tr(t){return t=Mi(t),Er(t)}var Iu=null;function Er(t){if(Iu=null,t=Il(t),t!==null){var e=b(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=_(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Iu=t,null}function Cd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Lm()){case Zr:return 2;case kr:return 8;case Zn:case Qm:return 32;case Kr:return 268435456;default:return 32}default:return 32}}var zr=!1,El=null,zl=null,_l=null,Hn=new Map,Bn=new Map,Nl=[],a0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Ud(t,e){switch(t){case"focusin":case"focusout":El=null;break;case"dragenter":case"dragleave":zl=null;break;case"mouseover":case"mouseout":_l=null;break;case"pointerover":case"pointerout":Hn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Bn.delete(e.pointerId)}}function qn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=ta(e),e!==null&&wd(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function n0(t,e,l,a,n){switch(e){case"focusin":return El=qn(El,t,e,l,a,n),!0;case"dragenter":return zl=qn(zl,t,e,l,a,n),!0;case"mouseover":return _l=qn(_l,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return Hn.set(u,qn(Hn.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Bn.set(u,qn(Bn.get(u)||null,t,e,l,a,n)),!0}return!1}function jd(t){var e=Il(t.target);if(e!==null){var l=b(e);if(l!==null){if(e=l.tag,e===13){if(e=_(l),e!==null){t.blockedOn=e,Fm(t.priority,function(){if(l.tag===13){var a=he();a=yi(a);var n=da(l,a);n!==null&&ve(n,l,a),xr(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function ti(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Tr(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Ni=a,l.target.dispatchEvent(a),Ni=null}else return e=ta(l),e!==null&&wd(e),t.blockedOn=l,!1;e.shift()}return!0}function Hd(t,e,l){ti(t)&&l.delete(e)}function u0(){zr=!1,El!==null&&ti(El)&&(El=null),zl!==null&&ti(zl)&&(zl=null),_l!==null&&ti(_l)&&(_l=null),Hn.forEach(Hd),Bn.forEach(Hd)}function ei(t,e){t.blockedOn===e&&(t.blockedOn=null,zr||(zr=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,u0)))}var li=null;function Bd(t){li!==t&&(li=t,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){li===t&&(li=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(Er(a||l)===null)continue;break}var u=ta(l);u!==null&&(t.splice(e,3),e-=3,Ec(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Yn(t){function e(d){return ei(d,t)}El!==null&&ei(El,t),zl!==null&&ei(zl,t),_l!==null&&ei(_l,t),Hn.forEach(e),Bn.forEach(e);for(var l=0;l<Nl.length;l++){var a=Nl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Nl.length&&(l=Nl[0],l.blockedOn===null);)jd(l),l.blockedOn===null&&Nl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[ee]||null;if(typeof u=="function")i||Bd(l);else if(i){var r=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[ee]||null)r=i.formAction;else if(Er(n)!==null)continue}else r=i.action;typeof r=="function"?l[a+1]=r:(l.splice(a,3),a-=3),Bd(l)}}}function _r(t){this._internalRoot=t}ai.prototype.render=_r.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var l=e.current,a=he();Od(l,a,t,e,null,null)},ai.prototype.unmount=_r.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Od(t.current,2,null,t,null,null),qu(),e[Pl]=null}};function ai(t){this._internalRoot=t}ai.prototype.unstable_scheduleHydration=function(t){if(t){var e=Pr();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Nl.length&&e!==0&&e<Nl[l].priority;l++);Nl.splice(l,0,t),l===0&&jd(t)}};var qd=s.version;if(qd!=="19.1.0")throw Error(o(527,qd,"19.1.0"));H.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=S(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var i0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:x,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ni.isDisabled&&ni.supportsFiber)try{La=ni.inject(i0),ce=ni}catch{}}return Xn.createRoot=function(t,e){if(!m(t))throw Error(o(299));var l=!1,a="",n=Is,u=tf,i=ef,r=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(i=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(r=e.unstable_transitionCallbacks)),e=Md(t,1,!1,null,null,l,a,n,u,i,r,null),t[Pl]=e.current,rr(t),new _r(e)},Xn.hydrateRoot=function(t,e,l){if(!m(t))throw Error(o(299));var a=!1,n="",u=Is,i=tf,r=ef,d=null,T=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(r=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(d=l.unstable_transitionCallbacks),l.formState!==void 0&&(T=l.formState)),e=Md(t,1,!0,e,l??null,a,n,u,i,r,d,T),e.context=Rd(null),l=e.current,a=he(),a=yi(a),n=sl(a),n.callback=null,fl(l,n,a),l=a,e.current.lanes=l,Va(e,l),Be(e),t[Pl]=e.current,rr(t),new ai(e)},Xn.version="19.1.0",Xn}var Jd;function v0(){if(Jd)return Rr.exports;Jd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Rr.exports=h0(),Rr.exports}var g0=v0();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),b0=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(s,f,o)=>o?o.toUpperCase():f.toLowerCase()),Wd=c=>{const s=b0(c);return s.charAt(0).toUpperCase()+s.slice(1)},sm=(...c)=>c.filter((s,f,o)=>!!s&&s.trim()!==""&&o.indexOf(s)===f).join(" ").trim(),p0=c=>{for(const s in c)if(s.startsWith("aria-")||s==="role"||s==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var S0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=C.forwardRef(({color:c="currentColor",size:s=24,strokeWidth:f=2,absoluteStrokeWidth:o,className:m="",children:b,iconNode:_,...M},S)=>C.createElement("svg",{ref:S,...S0,width:s,height:s,stroke:c,strokeWidth:o?Number(f)*24/Number(s):f,className:sm("lucide",m),...!b&&!p0(M)&&{"aria-hidden":"true"},...M},[..._.map(([g,N])=>C.createElement(g,N)),...Array.isArray(b)?b:[b]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=(c,s)=>{const f=C.forwardRef(({className:o,...m},b)=>C.createElement(x0,{ref:b,iconNode:s,className:sm(`lucide-${y0(Wd(c))}`,`lucide-${c}`,o),...m}));return f.displayName=Wd(c),f};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A0=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],$d=_e("book",A0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T0=[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]],E0=_e("corner-down-left",T0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z0=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],Ba=_e("download",z0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],N0=_e("globe",_0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M0=[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]],R0=_e("maximize-2",M0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],Cr=_e("message-square",O0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D0=[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]],w0=_e("minimize-2",D0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C0=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],U0=_e("rotate-ccw",C0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],H0=_e("search",j0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B0=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],q0=_e("tag",B0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y0=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],G0=_e("user",Y0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X0=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],L0=_e("x",X0);function Fd(c,s){if(typeof c=="function")return c(s);c!=null&&(c.current=s)}function fm(...c){return s=>{let f=!1;const o=c.map(m=>{const b=Fd(m,s);return!f&&typeof b=="function"&&(f=!0),b});if(f)return()=>{for(let m=0;m<o.length;m++){const b=o[m];typeof b=="function"?b():Fd(c[m],null)}}}}function Fl(...c){return C.useCallback(fm(...c),c)}function dm(c){const s=Q0(c),f=C.forwardRef((o,m)=>{const{children:b,..._}=o,M=C.Children.toArray(b),S=M.find(Z0);if(S){const g=S.props.children,N=M.map(j=>j===S?C.Children.count(g)>1?C.Children.only(null):C.isValidElement(g)?g.props.children:null:j);return p.jsx(s,{..._,ref:m,children:C.isValidElement(g)?C.cloneElement(g,void 0,N):null})}return p.jsx(s,{..._,ref:m,children:b})});return f.displayName=`${c}.Slot`,f}var mm=dm("Slot");function Q0(c){const s=C.forwardRef((f,o)=>{const{children:m,...b}=f;if(C.isValidElement(m)){const _=K0(m),M=k0(b,m.props);return m.type!==C.Fragment&&(M.ref=o?fm(o,_):_),C.cloneElement(m,M)}return C.Children.count(m)>1?C.Children.only(null):null});return s.displayName=`${c}.SlotClone`,s}var V0=Symbol("radix.slottable");function Z0(c){return C.isValidElement(c)&&typeof c.type=="function"&&"__radixId"in c.type&&c.type.__radixId===V0}function k0(c,s){const f={...s};for(const o in s){const m=c[o],b=s[o];/^on[A-Z]/.test(o)?m&&b?f[o]=(...M)=>{const S=b(...M);return m(...M),S}:m&&(f[o]=m):o==="style"?f[o]={...m,...b}:o==="className"&&(f[o]=[m,b].filter(Boolean).join(" "))}return{...c,...f}}function K0(c){var o,m;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,f=s&&"isReactWarning"in s&&s.isReactWarning;return f?c.ref:(s=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,f=s&&"isReactWarning"in s&&s.isReactWarning,f?c.props.ref:c.props.ref||c.ref)}function hm(c){var s,f,o="";if(typeof c=="string"||typeof c=="number")o+=c;else if(typeof c=="object")if(Array.isArray(c)){var m=c.length;for(s=0;s<m;s++)c[s]&&(f=hm(c[s]))&&(o&&(o+=" "),o+=f)}else for(f in c)c[f]&&(o&&(o+=" "),o+=f);return o}function vm(){for(var c,s,f=0,o="",m=arguments.length;f<m;f++)(c=arguments[f])&&(s=hm(c))&&(o&&(o+=" "),o+=s);return o}const Pd=c=>typeof c=="boolean"?`${c}`:c===0?"0":c,Id=vm,gm=(c,s)=>f=>{var o;if((s==null?void 0:s.variants)==null)return Id(c,f==null?void 0:f.class,f==null?void 0:f.className);const{variants:m,defaultVariants:b}=s,_=Object.keys(m).map(g=>{const N=f==null?void 0:f[g],j=b==null?void 0:b[g];if(N===null)return null;const B=Pd(N)||Pd(j);return m[g][B]}),M=f&&Object.entries(f).reduce((g,N)=>{let[j,B]=N;return B===void 0||(g[j]=B),g},{}),S=s==null||(o=s.compoundVariants)===null||o===void 0?void 0:o.reduce((g,N)=>{let{class:j,className:B,...Z}=N;return Object.entries(Z).every(J=>{let[et,ot]=J;return Array.isArray(ot)?ot.includes({...b,...M}[et]):{...b,...M}[et]===ot})?[...g,j,B]:g},[]);return Id(c,_,S,f==null?void 0:f.class,f==null?void 0:f.className)},Xr="-",J0=c=>{const s=$0(c),{conflictingClassGroups:f,conflictingClassGroupModifiers:o}=c;return{getClassGroupId:_=>{const M=_.split(Xr);return M[0]===""&&M.length!==1&&M.shift(),ym(M,s)||W0(_)},getConflictingClassGroupIds:(_,M)=>{const S=f[_]||[];return M&&o[_]?[...S,...o[_]]:S}}},ym=(c,s)=>{var _;if(c.length===0)return s.classGroupId;const f=c[0],o=s.nextPart.get(f),m=o?ym(c.slice(1),o):void 0;if(m)return m;if(s.validators.length===0)return;const b=c.join(Xr);return(_=s.validators.find(({validator:M})=>M(b)))==null?void 0:_.classGroupId},tm=/^\[(.+)\]$/,W0=c=>{if(tm.test(c)){const s=tm.exec(c)[1],f=s==null?void 0:s.substring(0,s.indexOf(":"));if(f)return"arbitrary.."+f}},$0=c=>{const{theme:s,classGroups:f}=c,o={nextPart:new Map,validators:[]};for(const m in f)Hr(f[m],o,m,s);return o},Hr=(c,s,f,o)=>{c.forEach(m=>{if(typeof m=="string"){const b=m===""?s:em(s,m);b.classGroupId=f;return}if(typeof m=="function"){if(F0(m)){Hr(m(o),s,f,o);return}s.validators.push({validator:m,classGroupId:f});return}Object.entries(m).forEach(([b,_])=>{Hr(_,em(s,b),f,o)})})},em=(c,s)=>{let f=c;return s.split(Xr).forEach(o=>{f.nextPart.has(o)||f.nextPart.set(o,{nextPart:new Map,validators:[]}),f=f.nextPart.get(o)}),f},F0=c=>c.isThemeGetter,P0=c=>{if(c<1)return{get:()=>{},set:()=>{}};let s=0,f=new Map,o=new Map;const m=(b,_)=>{f.set(b,_),s++,s>c&&(s=0,o=f,f=new Map)};return{get(b){let _=f.get(b);if(_!==void 0)return _;if((_=o.get(b))!==void 0)return m(b,_),_},set(b,_){f.has(b)?f.set(b,_):m(b,_)}}},Br="!",qr=":",I0=qr.length,tg=c=>{const{prefix:s,experimentalParseClassName:f}=c;let o=m=>{const b=[];let _=0,M=0,S=0,g;for(let J=0;J<m.length;J++){let et=m[J];if(_===0&&M===0){if(et===qr){b.push(m.slice(S,J)),S=J+I0;continue}if(et==="/"){g=J;continue}}et==="["?_++:et==="]"?_--:et==="("?M++:et===")"&&M--}const N=b.length===0?m:m.substring(S),j=eg(N),B=j!==N,Z=g&&g>S?g-S:void 0;return{modifiers:b,hasImportantModifier:B,baseClassName:j,maybePostfixModifierPosition:Z}};if(s){const m=s+qr,b=o;o=_=>_.startsWith(m)?b(_.substring(m.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:_,maybePostfixModifierPosition:void 0}}if(f){const m=o;o=b=>f({className:b,parseClassName:m})}return o},eg=c=>c.endsWith(Br)?c.substring(0,c.length-1):c.startsWith(Br)?c.substring(1):c,lg=c=>{const s=Object.fromEntries(c.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const m=[];let b=[];return o.forEach(_=>{_[0]==="["||s[_]?(m.push(...b.sort(),_),b=[]):b.push(_)}),m.push(...b.sort()),m}},ag=c=>({cache:P0(c.cacheSize),parseClassName:tg(c),sortModifiers:lg(c),...J0(c)}),ng=/\s+/,ug=(c,s)=>{const{parseClassName:f,getClassGroupId:o,getConflictingClassGroupIds:m,sortModifiers:b}=s,_=[],M=c.trim().split(ng);let S="";for(let g=M.length-1;g>=0;g-=1){const N=M[g],{isExternal:j,modifiers:B,hasImportantModifier:Z,baseClassName:J,maybePostfixModifierPosition:et}=f(N);if(j){S=N+(S.length>0?" "+S:S);continue}let ot=!!et,St=o(ot?J.substring(0,et):J);if(!St){if(!ot){S=N+(S.length>0?" "+S:S);continue}if(St=o(J),!St){S=N+(S.length>0?" "+S:S);continue}ot=!1}const Ct=b(B).join(":"),yt=Z?Ct+Br:Ct,Mt=yt+St;if(_.includes(Mt))continue;_.push(Mt);const lt=m(St,ot);for(let $=0;$<lt.length;++$){const Et=lt[$];_.push(yt+Et)}S=N+(S.length>0?" "+S:S)}return S};function ig(){let c=0,s,f,o="";for(;c<arguments.length;)(s=arguments[c++])&&(f=bm(s))&&(o&&(o+=" "),o+=f);return o}const bm=c=>{if(typeof c=="string")return c;let s,f="";for(let o=0;o<c.length;o++)c[o]&&(s=bm(c[o]))&&(f&&(f+=" "),f+=s);return f};function cg(c,...s){let f,o,m,b=_;function _(S){const g=s.reduce((N,j)=>j(N),c());return f=ag(g),o=f.cache.get,m=f.cache.set,b=M,M(S)}function M(S){const g=o(S);if(g)return g;const N=ug(S,f);return m(S,N),N}return function(){return b(ig.apply(null,arguments))}}const Qt=c=>{const s=f=>f[c]||[];return s.isThemeGetter=!0,s},pm=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Sm=/^\((?:(\w[\w-]*):)?(.+)\)$/i,rg=/^\d+\/\d+$/,og=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,sg=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,fg=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,dg=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,mg=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,qa=c=>rg.test(c),ct=c=>!!c&&!Number.isNaN(Number(c)),Rl=c=>!!c&&Number.isInteger(Number(c)),Ur=c=>c.endsWith("%")&&ct(c.slice(0,-1)),tl=c=>og.test(c),hg=()=>!0,vg=c=>sg.test(c)&&!fg.test(c),xm=()=>!1,gg=c=>dg.test(c),yg=c=>mg.test(c),bg=c=>!L(c)&&!Q(c),pg=c=>Ga(c,Em,xm),L=c=>pm.test(c),Jl=c=>Ga(c,zm,vg),jr=c=>Ga(c,Eg,ct),lm=c=>Ga(c,Am,xm),Sg=c=>Ga(c,Tm,yg),ui=c=>Ga(c,_m,gg),Q=c=>Sm.test(c),Ln=c=>Xa(c,zm),xg=c=>Xa(c,zg),am=c=>Xa(c,Am),Ag=c=>Xa(c,Em),Tg=c=>Xa(c,Tm),ii=c=>Xa(c,_m,!0),Ga=(c,s,f)=>{const o=pm.exec(c);return o?o[1]?s(o[1]):f(o[2]):!1},Xa=(c,s,f=!1)=>{const o=Sm.exec(c);return o?o[1]?s(o[1]):f:!1},Am=c=>c==="position"||c==="percentage",Tm=c=>c==="image"||c==="url",Em=c=>c==="length"||c==="size"||c==="bg-size",zm=c=>c==="length",Eg=c=>c==="number",zg=c=>c==="family-name",_m=c=>c==="shadow",_g=()=>{const c=Qt("color"),s=Qt("font"),f=Qt("text"),o=Qt("font-weight"),m=Qt("tracking"),b=Qt("leading"),_=Qt("breakpoint"),M=Qt("container"),S=Qt("spacing"),g=Qt("radius"),N=Qt("shadow"),j=Qt("inset-shadow"),B=Qt("text-shadow"),Z=Qt("drop-shadow"),J=Qt("blur"),et=Qt("perspective"),ot=Qt("aspect"),St=Qt("ease"),Ct=Qt("animate"),yt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Mt=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],lt=()=>[...Mt(),Q,L],$=()=>["auto","hidden","clip","visible","scroll"],Et=()=>["auto","contain","none"],V=()=>[Q,L,S],nt=()=>[qa,"full","auto",...V()],Dt=()=>[Rl,"none","subgrid",Q,L],Ht=()=>["auto",{span:["full",Rl,Q,L]},Rl,Q,L],Rt=()=>[Rl,"auto",Q,L],ge=()=>["auto","min","max","fr",Q,L],k=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],at=()=>["start","end","center","stretch","center-safe","end-safe"],x=()=>["auto",...V()],H=()=>[qa,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...V()],D=()=>[c,Q,L],P=()=>[...Mt(),am,lm,{position:[Q,L]}],h=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",Ag,pg,{size:[Q,L]}],Y=()=>[Ur,Ln,Jl],q=()=>["","none","full",g,Q,L],G=()=>["",ct,Ln,Jl],st=()=>["solid","dashed","dotted","double"],tt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ht=()=>[ct,Ur,am,lm],zt=()=>["","none",J,Q,L],ie=()=>["none",ct,Q,L],ll=()=>["none",ct,Q,L],al=()=>[ct,Q,L],nl=()=>[qa,"full",...V()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[tl],breakpoint:[tl],color:[hg],container:[tl],"drop-shadow":[tl],ease:["in","out","in-out"],font:[bg],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[tl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[tl],shadow:[tl],spacing:["px",ct],text:[tl],"text-shadow":[tl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",qa,L,Q,ot]}],container:["container"],columns:[{columns:[ct,L,Q,M]}],"break-after":[{"break-after":yt()}],"break-before":[{"break-before":yt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:lt()}],overflow:[{overflow:$()}],"overflow-x":[{"overflow-x":$()}],"overflow-y":[{"overflow-y":$()}],overscroll:[{overscroll:Et()}],"overscroll-x":[{"overscroll-x":Et()}],"overscroll-y":[{"overscroll-y":Et()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:nt()}],"inset-x":[{"inset-x":nt()}],"inset-y":[{"inset-y":nt()}],start:[{start:nt()}],end:[{end:nt()}],top:[{top:nt()}],right:[{right:nt()}],bottom:[{bottom:nt()}],left:[{left:nt()}],visibility:["visible","invisible","collapse"],z:[{z:[Rl,"auto",Q,L]}],basis:[{basis:[qa,"full","auto",M,...V()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ct,qa,"auto","initial","none",L]}],grow:[{grow:["",ct,Q,L]}],shrink:[{shrink:["",ct,Q,L]}],order:[{order:[Rl,"first","last","none",Q,L]}],"grid-cols":[{"grid-cols":Dt()}],"col-start-end":[{col:Ht()}],"col-start":[{"col-start":Rt()}],"col-end":[{"col-end":Rt()}],"grid-rows":[{"grid-rows":Dt()}],"row-start-end":[{row:Ht()}],"row-start":[{"row-start":Rt()}],"row-end":[{"row-end":Rt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ge()}],"auto-rows":[{"auto-rows":ge()}],gap:[{gap:V()}],"gap-x":[{"gap-x":V()}],"gap-y":[{"gap-y":V()}],"justify-content":[{justify:[...k(),"normal"]}],"justify-items":[{"justify-items":[...at(),"normal"]}],"justify-self":[{"justify-self":["auto",...at()]}],"align-content":[{content:["normal",...k()]}],"align-items":[{items:[...at(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...at(),{baseline:["","last"]}]}],"place-content":[{"place-content":k()}],"place-items":[{"place-items":[...at(),"baseline"]}],"place-self":[{"place-self":["auto",...at()]}],p:[{p:V()}],px:[{px:V()}],py:[{py:V()}],ps:[{ps:V()}],pe:[{pe:V()}],pt:[{pt:V()}],pr:[{pr:V()}],pb:[{pb:V()}],pl:[{pl:V()}],m:[{m:x()}],mx:[{mx:x()}],my:[{my:x()}],ms:[{ms:x()}],me:[{me:x()}],mt:[{mt:x()}],mr:[{mr:x()}],mb:[{mb:x()}],ml:[{ml:x()}],"space-x":[{"space-x":V()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":V()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[M,"screen",...H()]}],"min-w":[{"min-w":[M,"screen","none",...H()]}],"max-w":[{"max-w":[M,"screen","none","prose",{screen:[_]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",f,Ln,Jl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,Q,jr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ur,L]}],"font-family":[{font:[xg,L,s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[m,Q,L]}],"line-clamp":[{"line-clamp":[ct,"none",Q,jr]}],leading:[{leading:[b,...V()]}],"list-image":[{"list-image":["none",Q,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Q,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...st(),"wavy"]}],"text-decoration-thickness":[{decoration:[ct,"from-font","auto",Q,Jl]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[ct,"auto",Q,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:P()}],"bg-repeat":[{bg:h()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Rl,Q,L],radial:["",Q,L],conic:[Rl,Q,L]},Tg,Sg]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:q()}],"rounded-s":[{"rounded-s":q()}],"rounded-e":[{"rounded-e":q()}],"rounded-t":[{"rounded-t":q()}],"rounded-r":[{"rounded-r":q()}],"rounded-b":[{"rounded-b":q()}],"rounded-l":[{"rounded-l":q()}],"rounded-ss":[{"rounded-ss":q()}],"rounded-se":[{"rounded-se":q()}],"rounded-ee":[{"rounded-ee":q()}],"rounded-es":[{"rounded-es":q()}],"rounded-tl":[{"rounded-tl":q()}],"rounded-tr":[{"rounded-tr":q()}],"rounded-br":[{"rounded-br":q()}],"rounded-bl":[{"rounded-bl":q()}],"border-w":[{border:G()}],"border-w-x":[{"border-x":G()}],"border-w-y":[{"border-y":G()}],"border-w-s":[{"border-s":G()}],"border-w-e":[{"border-e":G()}],"border-w-t":[{"border-t":G()}],"border-w-r":[{"border-r":G()}],"border-w-b":[{"border-b":G()}],"border-w-l":[{"border-l":G()}],"divide-x":[{"divide-x":G()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":G()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...st(),"hidden","none"]}],"divide-style":[{divide:[...st(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[...st(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ct,Q,L]}],"outline-w":[{outline:["",ct,Ln,Jl]}],"outline-color":[{outline:D()}],shadow:[{shadow:["","none",N,ii,ui]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",j,ii,ui]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:G()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[ct,Jl]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":G()}],"inset-ring-color":[{"inset-ring":D()}],"text-shadow":[{"text-shadow":["none",B,ii,ui]}],"text-shadow-color":[{"text-shadow":D()}],opacity:[{opacity:[ct,Q,L]}],"mix-blend":[{"mix-blend":[...tt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":tt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ct]}],"mask-image-linear-from-pos":[{"mask-linear-from":ht()}],"mask-image-linear-to-pos":[{"mask-linear-to":ht()}],"mask-image-linear-from-color":[{"mask-linear-from":D()}],"mask-image-linear-to-color":[{"mask-linear-to":D()}],"mask-image-t-from-pos":[{"mask-t-from":ht()}],"mask-image-t-to-pos":[{"mask-t-to":ht()}],"mask-image-t-from-color":[{"mask-t-from":D()}],"mask-image-t-to-color":[{"mask-t-to":D()}],"mask-image-r-from-pos":[{"mask-r-from":ht()}],"mask-image-r-to-pos":[{"mask-r-to":ht()}],"mask-image-r-from-color":[{"mask-r-from":D()}],"mask-image-r-to-color":[{"mask-r-to":D()}],"mask-image-b-from-pos":[{"mask-b-from":ht()}],"mask-image-b-to-pos":[{"mask-b-to":ht()}],"mask-image-b-from-color":[{"mask-b-from":D()}],"mask-image-b-to-color":[{"mask-b-to":D()}],"mask-image-l-from-pos":[{"mask-l-from":ht()}],"mask-image-l-to-pos":[{"mask-l-to":ht()}],"mask-image-l-from-color":[{"mask-l-from":D()}],"mask-image-l-to-color":[{"mask-l-to":D()}],"mask-image-x-from-pos":[{"mask-x-from":ht()}],"mask-image-x-to-pos":[{"mask-x-to":ht()}],"mask-image-x-from-color":[{"mask-x-from":D()}],"mask-image-x-to-color":[{"mask-x-to":D()}],"mask-image-y-from-pos":[{"mask-y-from":ht()}],"mask-image-y-to-pos":[{"mask-y-to":ht()}],"mask-image-y-from-color":[{"mask-y-from":D()}],"mask-image-y-to-color":[{"mask-y-to":D()}],"mask-image-radial":[{"mask-radial":[Q,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":ht()}],"mask-image-radial-to-pos":[{"mask-radial-to":ht()}],"mask-image-radial-from-color":[{"mask-radial-from":D()}],"mask-image-radial-to-color":[{"mask-radial-to":D()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":Mt()}],"mask-image-conic-pos":[{"mask-conic":[ct]}],"mask-image-conic-from-pos":[{"mask-conic-from":ht()}],"mask-image-conic-to-pos":[{"mask-conic-to":ht()}],"mask-image-conic-from-color":[{"mask-conic-from":D()}],"mask-image-conic-to-color":[{"mask-conic-to":D()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:P()}],"mask-repeat":[{mask:h()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Q,L]}],filter:[{filter:["","none",Q,L]}],blur:[{blur:zt()}],brightness:[{brightness:[ct,Q,L]}],contrast:[{contrast:[ct,Q,L]}],"drop-shadow":[{"drop-shadow":["","none",Z,ii,ui]}],"drop-shadow-color":[{"drop-shadow":D()}],grayscale:[{grayscale:["",ct,Q,L]}],"hue-rotate":[{"hue-rotate":[ct,Q,L]}],invert:[{invert:["",ct,Q,L]}],saturate:[{saturate:[ct,Q,L]}],sepia:[{sepia:["",ct,Q,L]}],"backdrop-filter":[{"backdrop-filter":["","none",Q,L]}],"backdrop-blur":[{"backdrop-blur":zt()}],"backdrop-brightness":[{"backdrop-brightness":[ct,Q,L]}],"backdrop-contrast":[{"backdrop-contrast":[ct,Q,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ct,Q,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ct,Q,L]}],"backdrop-invert":[{"backdrop-invert":["",ct,Q,L]}],"backdrop-opacity":[{"backdrop-opacity":[ct,Q,L]}],"backdrop-saturate":[{"backdrop-saturate":[ct,Q,L]}],"backdrop-sepia":[{"backdrop-sepia":["",ct,Q,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":V()}],"border-spacing-x":[{"border-spacing-x":V()}],"border-spacing-y":[{"border-spacing-y":V()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Q,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ct,"initial",Q,L]}],ease:[{ease:["linear","initial",St,Q,L]}],delay:[{delay:[ct,Q,L]}],animate:[{animate:["none",Ct,Q,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[et,Q,L]}],"perspective-origin":[{"perspective-origin":lt()}],rotate:[{rotate:ie()}],"rotate-x":[{"rotate-x":ie()}],"rotate-y":[{"rotate-y":ie()}],"rotate-z":[{"rotate-z":ie()}],scale:[{scale:ll()}],"scale-x":[{"scale-x":ll()}],"scale-y":[{"scale-y":ll()}],"scale-z":[{"scale-z":ll()}],"scale-3d":["scale-3d"],skew:[{skew:al()}],"skew-x":[{"skew-x":al()}],"skew-y":[{"skew-y":al()}],transform:[{transform:[Q,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:lt()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:nl()}],"translate-x":[{"translate-x":nl()}],"translate-y":[{"translate-y":nl()}],"translate-z":[{"translate-z":nl()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q,L]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[ct,Ln,Jl,jr]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ng=cg(_g);function el(...c){return Ng(vm(c))}const Mg=gm("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function De({className:c,variant:s,size:f,asChild:o=!1,...m}){const b=o?mm:"button";return p.jsx(b,{"data-slot":"button",className:el(Mg({variant:s,size:f,className:c})),...m})}function nm({className:c,type:s,...f}){return p.jsx("input",{type:s,"data-slot":"input",className:el("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",c),...f})}function ci({className:c,...s}){return p.jsx("div",{"data-slot":"card",className:el("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",c),...s})}function um({className:c,...s}){return p.jsx("div",{"data-slot":"card-header",className:el("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",c),...s})}function im({className:c,...s}){return p.jsx("div",{"data-slot":"card-title",className:el("leading-none font-semibold",c),...s})}function ri({className:c,...s}){return p.jsx("div",{"data-slot":"card-content",className:el("px-6",c),...s})}const Rg=gm("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function cm({className:c,variant:s,asChild:f=!1,...o}){const m=f?mm:"span";return p.jsx(m,{"data-slot":"badge",className:el(Rg({variant:s}),c),...o})}om();var Og=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Qn=Og.reduce((c,s)=>{const f=dm(`Primitive.${s}`),o=C.forwardRef((m,b)=>{const{asChild:_,...M}=m,S=_?f:s;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),p.jsx(S,{...M,ref:b})});return o.displayName=`Primitive.${s}`,{...c,[s]:o}},{}),Yr=globalThis!=null&&globalThis.document?C.useLayoutEffect:()=>{};function Dg(c,s){return C.useReducer((f,o)=>s[f][o]??f,c)}var Vn=c=>{const{present:s,children:f}=c,o=wg(s),m=typeof f=="function"?f({present:o.isPresent}):C.Children.only(f),b=Fl(o.ref,Cg(m));return typeof f=="function"||o.isPresent?C.cloneElement(m,{ref:b}):null};Vn.displayName="Presence";function wg(c){const[s,f]=C.useState(),o=C.useRef(null),m=C.useRef(c),b=C.useRef("none"),_=c?"mounted":"unmounted",[M,S]=Dg(_,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return C.useEffect(()=>{const g=oi(o.current);b.current=M==="mounted"?g:"none"},[M]),Yr(()=>{const g=o.current,N=m.current;if(N!==c){const B=b.current,Z=oi(g);c?S("MOUNT"):Z==="none"||(g==null?void 0:g.display)==="none"?S("UNMOUNT"):S(N&&B!==Z?"ANIMATION_OUT":"UNMOUNT"),m.current=c}},[c,S]),Yr(()=>{if(s){let g;const N=s.ownerDocument.defaultView??window,j=Z=>{const et=oi(o.current).includes(Z.animationName);if(Z.target===s&&et&&(S("ANIMATION_END"),!m.current)){const ot=s.style.animationFillMode;s.style.animationFillMode="forwards",g=N.setTimeout(()=>{s.style.animationFillMode==="forwards"&&(s.style.animationFillMode=ot)})}},B=Z=>{Z.target===s&&(b.current=oi(o.current))};return s.addEventListener("animationstart",B),s.addEventListener("animationcancel",j),s.addEventListener("animationend",j),()=>{N.clearTimeout(g),s.removeEventListener("animationstart",B),s.removeEventListener("animationcancel",j),s.removeEventListener("animationend",j)}}else S("ANIMATION_END")},[s,S]),{isPresent:["mounted","unmountSuspended"].includes(M),ref:C.useCallback(g=>{o.current=g?getComputedStyle(g):null,f(g)},[])}}function oi(c){return(c==null?void 0:c.animationName)||"none"}function Cg(c){var o,m;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,f=s&&"isReactWarning"in s&&s.isReactWarning;return f?c.ref:(s=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,f=s&&"isReactWarning"in s&&s.isReactWarning,f?c.props.ref:c.props.ref||c.ref)}function Ug(c,s=[]){let f=[];function o(b,_){const M=C.createContext(_),S=f.length;f=[...f,_];const g=j=>{var St;const{scope:B,children:Z,...J}=j,et=((St=B==null?void 0:B[c])==null?void 0:St[S])||M,ot=C.useMemo(()=>J,Object.values(J));return p.jsx(et.Provider,{value:ot,children:Z})};g.displayName=b+"Provider";function N(j,B){var et;const Z=((et=B==null?void 0:B[c])==null?void 0:et[S])||M,J=C.useContext(Z);if(J)return J;if(_!==void 0)return _;throw new Error(`\`${j}\` must be used within \`${b}\``)}return[g,N]}const m=()=>{const b=f.map(_=>C.createContext(_));return function(M){const S=(M==null?void 0:M[c])||b;return C.useMemo(()=>({[`__scope${c}`]:{...M,[c]:S}}),[M,S])}};return m.scopeName=c,[o,jg(m,...s)]}function jg(...c){const s=c[0];if(c.length===1)return s;const f=()=>{const o=c.map(m=>({useScope:m(),scopeName:m.scopeName}));return function(b){const _=o.reduce((M,{useScope:S,scopeName:g})=>{const j=S(b)[`__scope${g}`];return{...M,...j}},{});return C.useMemo(()=>({[`__scope${s.scopeName}`]:_}),[_])}};return f.scopeName=s.scopeName,f}function Wl(c){const s=C.useRef(c);return C.useEffect(()=>{s.current=c}),C.useMemo(()=>(...f)=>{var o;return(o=s.current)==null?void 0:o.call(s,...f)},[])}var Hg=C.createContext(void 0);function Bg(c){const s=C.useContext(Hg);return c||s||"ltr"}function qg(c,[s,f]){return Math.min(f,Math.max(s,c))}function $l(c,s,{checkForDefaultPrevented:f=!0}={}){return function(m){if(c==null||c(m),f===!1||!m.defaultPrevented)return s==null?void 0:s(m)}}function Yg(c,s){return C.useReducer((f,o)=>s[f][o]??f,c)}var Lr="ScrollArea",[Nm,ly]=Ug(Lr),[Gg,Ne]=Nm(Lr),Mm=C.forwardRef((c,s)=>{const{__scopeScrollArea:f,type:o="hover",dir:m,scrollHideDelay:b=600,..._}=c,[M,S]=C.useState(null),[g,N]=C.useState(null),[j,B]=C.useState(null),[Z,J]=C.useState(null),[et,ot]=C.useState(null),[St,Ct]=C.useState(0),[yt,Mt]=C.useState(0),[lt,$]=C.useState(!1),[Et,V]=C.useState(!1),nt=Fl(s,Ht=>S(Ht)),Dt=Bg(m);return p.jsx(Gg,{scope:f,type:o,dir:Dt,scrollHideDelay:b,scrollArea:M,viewport:g,onViewportChange:N,content:j,onContentChange:B,scrollbarX:Z,onScrollbarXChange:J,scrollbarXEnabled:lt,onScrollbarXEnabledChange:$,scrollbarY:et,onScrollbarYChange:ot,scrollbarYEnabled:Et,onScrollbarYEnabledChange:V,onCornerWidthChange:Ct,onCornerHeightChange:Mt,children:p.jsx(Qn.div,{dir:Dt,..._,ref:nt,style:{position:"relative","--radix-scroll-area-corner-width":St+"px","--radix-scroll-area-corner-height":yt+"px",...c.style}})})});Mm.displayName=Lr;var Rm="ScrollAreaViewport",Om=C.forwardRef((c,s)=>{const{__scopeScrollArea:f,children:o,nonce:m,...b}=c,_=Ne(Rm,f),M=C.useRef(null),S=Fl(s,M,_.onViewportChange);return p.jsxs(p.Fragment,{children:[p.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:m}),p.jsx(Qn.div,{"data-radix-scroll-area-viewport":"",...b,ref:S,style:{overflowX:_.scrollbarXEnabled?"scroll":"hidden",overflowY:_.scrollbarYEnabled?"scroll":"hidden",...c.style},children:p.jsx("div",{ref:_.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});Om.displayName=Rm;var qe="ScrollAreaScrollbar",Dm=C.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=Ne(qe,c.__scopeScrollArea),{onScrollbarXEnabledChange:b,onScrollbarYEnabledChange:_}=m,M=c.orientation==="horizontal";return C.useEffect(()=>(M?b(!0):_(!0),()=>{M?b(!1):_(!1)}),[M,b,_]),m.type==="hover"?p.jsx(Xg,{...o,ref:s,forceMount:f}):m.type==="scroll"?p.jsx(Lg,{...o,ref:s,forceMount:f}):m.type==="auto"?p.jsx(wm,{...o,ref:s,forceMount:f}):m.type==="always"?p.jsx(Qr,{...o,ref:s}):null});Dm.displayName=qe;var Xg=C.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=Ne(qe,c.__scopeScrollArea),[b,_]=C.useState(!1);return C.useEffect(()=>{const M=m.scrollArea;let S=0;if(M){const g=()=>{window.clearTimeout(S),_(!0)},N=()=>{S=window.setTimeout(()=>_(!1),m.scrollHideDelay)};return M.addEventListener("pointerenter",g),M.addEventListener("pointerleave",N),()=>{window.clearTimeout(S),M.removeEventListener("pointerenter",g),M.removeEventListener("pointerleave",N)}}},[m.scrollArea,m.scrollHideDelay]),p.jsx(Vn,{present:f||b,children:p.jsx(wm,{"data-state":b?"visible":"hidden",...o,ref:s})})}),Lg=C.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=Ne(qe,c.__scopeScrollArea),b=c.orientation==="horizontal",_=mi(()=>S("SCROLL_END"),100),[M,S]=Yg("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return C.useEffect(()=>{if(M==="idle"){const g=window.setTimeout(()=>S("HIDE"),m.scrollHideDelay);return()=>window.clearTimeout(g)}},[M,m.scrollHideDelay,S]),C.useEffect(()=>{const g=m.viewport,N=b?"scrollLeft":"scrollTop";if(g){let j=g[N];const B=()=>{const Z=g[N];j!==Z&&(S("SCROLL"),_()),j=Z};return g.addEventListener("scroll",B),()=>g.removeEventListener("scroll",B)}},[m.viewport,b,S,_]),p.jsx(Vn,{present:f||M!=="hidden",children:p.jsx(Qr,{"data-state":M==="hidden"?"hidden":"visible",...o,ref:s,onPointerEnter:$l(c.onPointerEnter,()=>S("POINTER_ENTER")),onPointerLeave:$l(c.onPointerLeave,()=>S("POINTER_LEAVE"))})})}),wm=C.forwardRef((c,s)=>{const f=Ne(qe,c.__scopeScrollArea),{forceMount:o,...m}=c,[b,_]=C.useState(!1),M=c.orientation==="horizontal",S=mi(()=>{if(f.viewport){const g=f.viewport.offsetWidth<f.viewport.scrollWidth,N=f.viewport.offsetHeight<f.viewport.scrollHeight;_(M?g:N)}},10);return Ya(f.viewport,S),Ya(f.content,S),p.jsx(Vn,{present:o||b,children:p.jsx(Qr,{"data-state":b?"visible":"hidden",...m,ref:s})})}),Qr=C.forwardRef((c,s)=>{const{orientation:f="vertical",...o}=c,m=Ne(qe,c.__scopeScrollArea),b=C.useRef(null),_=C.useRef(0),[M,S]=C.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),g=Bm(M.viewport,M.content),N={...o,sizes:M,onSizesChange:S,hasThumb:g>0&&g<1,onThumbChange:B=>b.current=B,onThumbPointerUp:()=>_.current=0,onThumbPointerDown:B=>_.current=B};function j(B,Z){return Jg(B,_.current,M,Z)}return f==="horizontal"?p.jsx(Qg,{...N,ref:s,onThumbPositionChange:()=>{if(m.viewport&&b.current){const B=m.viewport.scrollLeft,Z=rm(B,M,m.dir);b.current.style.transform=`translate3d(${Z}px, 0, 0)`}},onWheelScroll:B=>{m.viewport&&(m.viewport.scrollLeft=B)},onDragScroll:B=>{m.viewport&&(m.viewport.scrollLeft=j(B,m.dir))}}):f==="vertical"?p.jsx(Vg,{...N,ref:s,onThumbPositionChange:()=>{if(m.viewport&&b.current){const B=m.viewport.scrollTop,Z=rm(B,M);b.current.style.transform=`translate3d(0, ${Z}px, 0)`}},onWheelScroll:B=>{m.viewport&&(m.viewport.scrollTop=B)},onDragScroll:B=>{m.viewport&&(m.viewport.scrollTop=j(B))}}):null}),Qg=C.forwardRef((c,s)=>{const{sizes:f,onSizesChange:o,...m}=c,b=Ne(qe,c.__scopeScrollArea),[_,M]=C.useState(),S=C.useRef(null),g=Fl(s,S,b.onScrollbarXChange);return C.useEffect(()=>{S.current&&M(getComputedStyle(S.current))},[S]),p.jsx(Um,{"data-orientation":"horizontal",...m,ref:g,sizes:f,style:{bottom:0,left:b.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:b.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":di(f)+"px",...c.style},onThumbPointerDown:N=>c.onThumbPointerDown(N.x),onDragScroll:N=>c.onDragScroll(N.x),onWheelScroll:(N,j)=>{if(b.viewport){const B=b.viewport.scrollLeft+N.deltaX;c.onWheelScroll(B),Ym(B,j)&&N.preventDefault()}},onResize:()=>{S.current&&b.viewport&&_&&o({content:b.viewport.scrollWidth,viewport:b.viewport.offsetWidth,scrollbar:{size:S.current.clientWidth,paddingStart:fi(_.paddingLeft),paddingEnd:fi(_.paddingRight)}})}})}),Vg=C.forwardRef((c,s)=>{const{sizes:f,onSizesChange:o,...m}=c,b=Ne(qe,c.__scopeScrollArea),[_,M]=C.useState(),S=C.useRef(null),g=Fl(s,S,b.onScrollbarYChange);return C.useEffect(()=>{S.current&&M(getComputedStyle(S.current))},[S]),p.jsx(Um,{"data-orientation":"vertical",...m,ref:g,sizes:f,style:{top:0,right:b.dir==="ltr"?0:void 0,left:b.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":di(f)+"px",...c.style},onThumbPointerDown:N=>c.onThumbPointerDown(N.y),onDragScroll:N=>c.onDragScroll(N.y),onWheelScroll:(N,j)=>{if(b.viewport){const B=b.viewport.scrollTop+N.deltaY;c.onWheelScroll(B),Ym(B,j)&&N.preventDefault()}},onResize:()=>{S.current&&b.viewport&&_&&o({content:b.viewport.scrollHeight,viewport:b.viewport.offsetHeight,scrollbar:{size:S.current.clientHeight,paddingStart:fi(_.paddingTop),paddingEnd:fi(_.paddingBottom)}})}})}),[Zg,Cm]=Nm(qe),Um=C.forwardRef((c,s)=>{const{__scopeScrollArea:f,sizes:o,hasThumb:m,onThumbChange:b,onThumbPointerUp:_,onThumbPointerDown:M,onThumbPositionChange:S,onDragScroll:g,onWheelScroll:N,onResize:j,...B}=c,Z=Ne(qe,f),[J,et]=C.useState(null),ot=Fl(s,nt=>et(nt)),St=C.useRef(null),Ct=C.useRef(""),yt=Z.viewport,Mt=o.content-o.viewport,lt=Wl(N),$=Wl(S),Et=mi(j,10);function V(nt){if(St.current){const Dt=nt.clientX-St.current.left,Ht=nt.clientY-St.current.top;g({x:Dt,y:Ht})}}return C.useEffect(()=>{const nt=Dt=>{const Ht=Dt.target;(J==null?void 0:J.contains(Ht))&&lt(Dt,Mt)};return document.addEventListener("wheel",nt,{passive:!1}),()=>document.removeEventListener("wheel",nt,{passive:!1})},[yt,J,Mt,lt]),C.useEffect($,[o,$]),Ya(J,Et),Ya(Z.content,Et),p.jsx(Zg,{scope:f,scrollbar:J,hasThumb:m,onThumbChange:Wl(b),onThumbPointerUp:Wl(_),onThumbPositionChange:$,onThumbPointerDown:Wl(M),children:p.jsx(Qn.div,{...B,ref:ot,style:{position:"absolute",...B.style},onPointerDown:$l(c.onPointerDown,nt=>{nt.button===0&&(nt.target.setPointerCapture(nt.pointerId),St.current=J.getBoundingClientRect(),Ct.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",Z.viewport&&(Z.viewport.style.scrollBehavior="auto"),V(nt))}),onPointerMove:$l(c.onPointerMove,V),onPointerUp:$l(c.onPointerUp,nt=>{const Dt=nt.target;Dt.hasPointerCapture(nt.pointerId)&&Dt.releasePointerCapture(nt.pointerId),document.body.style.webkitUserSelect=Ct.current,Z.viewport&&(Z.viewport.style.scrollBehavior=""),St.current=null})})})}),si="ScrollAreaThumb",jm=C.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=Cm(si,c.__scopeScrollArea);return p.jsx(Vn,{present:f||m.hasThumb,children:p.jsx(kg,{ref:s,...o})})}),kg=C.forwardRef((c,s)=>{const{__scopeScrollArea:f,style:o,...m}=c,b=Ne(si,f),_=Cm(si,f),{onThumbPositionChange:M}=_,S=Fl(s,j=>_.onThumbChange(j)),g=C.useRef(void 0),N=mi(()=>{g.current&&(g.current(),g.current=void 0)},100);return C.useEffect(()=>{const j=b.viewport;if(j){const B=()=>{if(N(),!g.current){const Z=Wg(j,M);g.current=Z,M()}};return M(),j.addEventListener("scroll",B),()=>j.removeEventListener("scroll",B)}},[b.viewport,N,M]),p.jsx(Qn.div,{"data-state":_.hasThumb?"visible":"hidden",...m,ref:S,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:$l(c.onPointerDownCapture,j=>{const Z=j.target.getBoundingClientRect(),J=j.clientX-Z.left,et=j.clientY-Z.top;_.onThumbPointerDown({x:J,y:et})}),onPointerUp:$l(c.onPointerUp,_.onThumbPointerUp)})});jm.displayName=si;var Vr="ScrollAreaCorner",Hm=C.forwardRef((c,s)=>{const f=Ne(Vr,c.__scopeScrollArea),o=!!(f.scrollbarX&&f.scrollbarY);return f.type!=="scroll"&&o?p.jsx(Kg,{...c,ref:s}):null});Hm.displayName=Vr;var Kg=C.forwardRef((c,s)=>{const{__scopeScrollArea:f,...o}=c,m=Ne(Vr,f),[b,_]=C.useState(0),[M,S]=C.useState(0),g=!!(b&&M);return Ya(m.scrollbarX,()=>{var j;const N=((j=m.scrollbarX)==null?void 0:j.offsetHeight)||0;m.onCornerHeightChange(N),S(N)}),Ya(m.scrollbarY,()=>{var j;const N=((j=m.scrollbarY)==null?void 0:j.offsetWidth)||0;m.onCornerWidthChange(N),_(N)}),g?p.jsx(Qn.div,{...o,ref:s,style:{width:b,height:M,position:"absolute",right:m.dir==="ltr"?0:void 0,left:m.dir==="rtl"?0:void 0,bottom:0,...c.style}}):null});function fi(c){return c?parseInt(c,10):0}function Bm(c,s){const f=c/s;return isNaN(f)?0:f}function di(c){const s=Bm(c.viewport,c.content),f=c.scrollbar.paddingStart+c.scrollbar.paddingEnd,o=(c.scrollbar.size-f)*s;return Math.max(o,18)}function Jg(c,s,f,o="ltr"){const m=di(f),b=m/2,_=s||b,M=m-_,S=f.scrollbar.paddingStart+_,g=f.scrollbar.size-f.scrollbar.paddingEnd-M,N=f.content-f.viewport,j=o==="ltr"?[0,N]:[N*-1,0];return qm([S,g],j)(c)}function rm(c,s,f="ltr"){const o=di(s),m=s.scrollbar.paddingStart+s.scrollbar.paddingEnd,b=s.scrollbar.size-m,_=s.content-s.viewport,M=b-o,S=f==="ltr"?[0,_]:[_*-1,0],g=qg(c,S);return qm([0,_],[0,M])(g)}function qm(c,s){return f=>{if(c[0]===c[1]||s[0]===s[1])return s[0];const o=(s[1]-s[0])/(c[1]-c[0]);return s[0]+o*(f-c[0])}}function Ym(c,s){return c>0&&c<s}var Wg=(c,s=()=>{})=>{let f={left:c.scrollLeft,top:c.scrollTop},o=0;return function m(){const b={left:c.scrollLeft,top:c.scrollTop},_=f.left!==b.left,M=f.top!==b.top;(_||M)&&s(),f=b,o=window.requestAnimationFrame(m)}(),()=>window.cancelAnimationFrame(o)};function mi(c,s){const f=Wl(c),o=C.useRef(0);return C.useEffect(()=>()=>window.clearTimeout(o.current),[]),C.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(f,s)},[f,s])}function Ya(c,s){const f=Wl(s);Yr(()=>{let o=0;if(c){const m=new ResizeObserver(()=>{cancelAnimationFrame(o),o=window.requestAnimationFrame(f)});return m.observe(c),()=>{window.cancelAnimationFrame(o),m.unobserve(c)}}},[c,f])}var $g=Mm,Fg=Om,Pg=Hm;function Ig({className:c,children:s,...f}){return p.jsxs($g,{"data-slot":"scroll-area",className:el("relative",c),...f,children:[p.jsx(Fg,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:s}),p.jsx(ty,{}),p.jsx(Pg,{})]})}function ty({className:c,orientation:s="vertical",...f}){return p.jsx(Dm,{"data-slot":"scroll-area-scrollbar",orientation:s,className:el("flex touch-none p-px transition-colors select-none",s==="vertical"&&"h-full w-2.5 border-l border-l-transparent",s==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",c),...f,children:p.jsx(jm,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function ey(){const[c,s]=C.useState(""),[f,o]=C.useState("en"),[m,b]=C.useState([]),[_,M]=C.useState(!1),[S,g]=C.useState(""),[N,j]=C.useState([]),[B,Z]=C.useState(""),[J,et]=C.useState(!1),[ot,St]=C.useState(!1),[Ct,yt]=C.useState(!0),[Mt,lt]=C.useState(()=>"session_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)),$={en:{title:"Book Information Retrieval System",searchPlaceholder:"Enter book name or ask a question...",searchButton:"Search Books",categories:"Categories",downloadPdf:"Download PDF",publicDomain:"Public Domain - Free to Download",noResults:"No books found. Try a different search term.",error:"An error occurred while searching. Please try again.",loading:"Searching for books...",pdfLinks:"PDF Downloads",convert:"Convert",download:"Download",chatTitle:"AI Assistant",chatPlaceholder:"Ask me anything about books...",sendMessage:"Send",llmLoading:"AI is thinking...",relatedBooks:"Related Books",askAboutBook:"Ask AI about this book",askAboutRelated:"Get related books from AI"},ar:{title:"نظام استرجاع معلومات الكتب",searchPlaceholder:"أدخل اسم الكتاب أو اطرح سؤالاً...",searchButton:"البحث عن الكتب",categories:"التصنيفات",downloadPdf:"تحميل PDF",publicDomain:"ملكية عامة - مجاني للتحميل",noResults:"لم يتم العثور على كتب. جرب مصطلح بحث مختلف.",error:"حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.",loading:"البحث عن الكتب...",pdfLinks:"تحميلات PDF",convert:"تحويل",download:"تحميل",chatTitle:"مساعد الذكاء الاصطناعي",chatPlaceholder:"اسألني أي شيء عن الكتب...",sendMessage:"إرسال",llmLoading:"الذكاء الاصطناعي يفكر...",relatedBooks:"كتب ذات صلة",askAboutBook:"اسأل الذكاء الاصطناعي عن هذا الكتاب",askAboutRelated:"احصل على كتب ذات صلة من الذكاء الاصطناعي"}}[f];C.useEffect(()=>{document.documentElement.dir=f==="ar"?"rtl":"ltr"},[f]);const Et=async()=>{if(c.trim()){M(!0),g(""),b([]);try{const k=await fetch("/api/books/pdf-priority-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:c,lang:f})});if(!k.ok)throw new Error("Search failed");const at=await k.json();b(at.results||[])}catch(k){g("Failed to search books. Please try again."),console.error("Search error:",k)}finally{M(!1)}}},V=async(k,at)=>{try{const x=await fetch("/api/books/convert-to-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_url:k,output_filename:`converted_book.${at}.pdf`})});if(x.ok){const H=await x.blob(),D=window.URL.createObjectURL(H),P=document.createElement("a");P.href=D,P.download=`converted_book.${at}.pdf`,document.body.appendChild(P),P.click(),window.URL.revokeObjectURL(D),document.body.removeChild(P)}else alert("Conversion failed. Please try again.")}catch(x){console.error("Conversion error:",x),alert("Conversion failed. Please try again.")}},nt=async()=>{if(!B.trim())return;const k=B;j(at=>[...at,{sender:"user",text:k}]),Z(""),et(!0);try{const at=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:k,session_id:Mt})});if(!at.ok)throw new Error("LLM chat failed");const x=await at.json();j(H=>[...H,{sender:"ai",text:x.response,pdfResults:x.pdf_results||[]}])}catch(at){console.error("LLM chat error:",at),j(x=>[...x,{sender:"ai",text:$.error}])}finally{et(!1)}},Dt=()=>{j([]),lt("session_"+Date.now()+"_"+Math.random().toString(36).substring(2,9))},Ht=async(k,at)=>{et(!0),j(x=>[...x,{sender:"user",text:`${$.askAboutRelated}: ${k} by ${at}`}]);try{const x=await fetch("/api/llm/related-books",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:k,author:at})});if(!x.ok)throw new Error("Related books failed");const H=await x.json();if(H.related_books&&H.related_books.length>0){const D=H.related_books.map(P=>`${P.title} - ${P.author}`).join(`
`);j(P=>[...P,{sender:"ai",text:`${$.relatedBooks}:
${D}`}])}else j(D=>[...D,{sender:"ai",text:"No related books found."}])}catch(x){console.error("Related books error:",x),j(H=>[...H,{sender:"ai",text:$.error}])}finally{et(!1)}},Rt=async(k,at,x)=>{const H=`Tell me about the book: ${k} by ${at}. Here is a brief description: ${x}.`;j(D=>[...D,{sender:"user",text:`${$.askAboutBook}: ${k}`}]),et(!0);try{const D=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:H})});if(!D.ok)throw new Error("LLM chat failed");const P=await D.json();j(h=>[...h,{sender:"ai",text:P.response}])}catch(D){console.error("LLM chat error:",D),j(P=>[...P,{sender:"ai",text:$.error}])}finally{et(!1)}},ge=()=>{o(f==="en"?"ar":"en")};return p.jsx("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${f==="ar"?"rtl":"ltr"}`,children:p.jsxs("div",{className:"container mx-auto px-4 py-8",children:[p.jsxs("div",{className:"text-center mb-8",children:[p.jsxs("div",{className:"flex justify-between items-center mb-4",children:[p.jsxs("div",{className:"flex items-center gap-2",children:[p.jsx($d,{className:"h-8 w-8 text-blue-600"}),p.jsx("span",{className:"text-xl font-bold text-blue-600",children:"BookFinder AI"})]}),p.jsxs(De,{variant:"outline",size:"sm",onClick:ge,className:"flex items-center gap-2",children:[p.jsx(N0,{className:"h-4 w-4"}),f==="en"?"العربية":"English"]})]}),p.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:$.title}),p.jsx("p",{className:"text-lg text-gray-600",children:$.subtitle})]}),p.jsx(ci,{className:"max-w-2xl mx-auto mb-8",children:p.jsx(ri,{className:"p-6",children:p.jsxs("div",{className:"flex gap-4",children:[p.jsx("div",{className:"flex-1",children:p.jsx(nm,{type:"text",placeholder:$.searchPlaceholder,value:c,onChange:k=>s(k.target.value),onKeyPress:k=>k.key==="Enter"&&Et(),className:"text-lg",dir:f==="ar"?"rtl":"ltr"})}),p.jsxs(De,{onClick:Et,disabled:_||!c.trim(),className:"px-6",children:[p.jsx(H0,{className:"h-4 w-4 mr-2"}),$.searchButton]})]})})}),_&&p.jsxs("div",{className:"text-center py-8",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),p.jsx("p",{className:"text-gray-600",children:$.loading})]}),S&&p.jsx("div",{className:"max-w-2xl mx-auto mb-8",children:p.jsx(ci,{className:"border-red-200 bg-red-50",children:p.jsx(ri,{className:"p-4",children:p.jsx("p",{className:"text-red-600 text-center",children:S})})})}),m.length>0&&p.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:m.map((k,at)=>p.jsxs(ci,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[p.jsx(um,{className:"pb-4",children:p.jsxs("div",{className:"flex gap-4",children:[p.jsx("img",{src:k.thumbnail||"/api/placeholder/120/180",alt:k.title,className:"w-20 h-30 object-cover rounded",onError:x=>{x.target.src="/api/placeholder/120/180"}}),p.jsxs("div",{className:"flex-1",children:[p.jsx(im,{className:"text-lg mb-2 line-clamp-2",children:k.title}),p.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-2",children:[p.jsx(G0,{className:"h-4 w-4"}),p.jsx("span",{children:k.author})]}),k.description&&p.jsx("p",{className:"text-xs text-gray-500 line-clamp-3",children:k.description})]})]})}),p.jsx(ri,{children:p.jsxs("div",{className:"space-y-3",children:[p.jsxs("div",{children:[p.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[p.jsx(q0,{className:"h-4 w-4 text-gray-500"}),p.jsx("span",{className:"text-sm font-medium",children:$.categories})]}),p.jsx("div",{className:"flex flex-wrap gap-1",children:k.categories.map((x,H)=>p.jsx(cm,{variant:"secondary",className:"text-xs",children:x},H))})]}),k.pdf_links&&k.pdf_links.length>0&&p.jsxs("div",{className:"pt-2",children:[p.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[p.jsx(Ba,{className:"h-4 w-4 text-green-600"}),p.jsx("span",{className:"text-sm font-medium text-green-700",children:$.pdfLinks}),p.jsxs(cm,{variant:"secondary",className:"text-xs bg-green-100 text-green-800",children:[k.pdf_links.length," PDF",k.pdf_links.length>1?"s":""]})]}),p.jsx("div",{className:"space-y-2",children:k.pdf_links.map((x,H)=>p.jsxs("div",{className:"flex items-center justify-between bg-green-50 border border-green-200 p-3 rounded-lg",children:[p.jsxs("div",{className:"flex items-center gap-2",children:[p.jsx(Ba,{className:"h-4 w-4 text-green-600"}),p.jsxs("div",{children:[p.jsx("div",{className:"text-sm font-medium text-green-800",children:x.source}),p.jsx("div",{className:"text-xs text-green-600",children:$.publicDomain})]})]}),x.type==="convertible"?p.jsxs(De,{onClick:()=>V(x.url,x.format),size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",children:[p.jsx(Ba,{className:"h-3 w-3 mr-1"}),$.convert]}):p.jsx(De,{asChild:!0,size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",children:p.jsxs("a",{href:x.url,target:"_blank",rel:"noopener noreferrer",children:[p.jsx(Ba,{className:"h-3 w-3 mr-1"}),$.downloadPdf]})})]},H))})]}),p.jsxs("div",{className:"flex flex-col gap-2 pt-2",children:[p.jsx(De,{variant:"outline",size:"sm",onClick:()=>Rt(k.title,k.author,k.description),children:$.askAboutBook}),p.jsx(De,{variant:"outline",size:"sm",onClick:()=>Ht(k.title,k.author),children:$.askAboutRelated})]})]})})]},k.id||at))}),!_&&!S&&m.length===0&&c&&p.jsxs("div",{className:"text-center py-8",children:[p.jsx($d,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),p.jsx("p",{className:"text-gray-600",children:$.noResults})]}),Ct&&p.jsxs(ci,{className:`fixed ${ot?"inset-4":"bottom-4 right-4 w-96 h-[500px]"} flex flex-col shadow-xl z-50 transition-all duration-300`,children:[p.jsxs(um,{className:"flex flex-row items-center justify-between space-y-0 p-4 border-b bg-blue-50",children:[p.jsxs("div",{className:"flex items-center gap-2",children:[p.jsx(Cr,{className:"h-5 w-5 text-blue-600"}),p.jsx(im,{className:"text-lg font-semibold text-blue-800",children:$.chatTitle}),p.jsx("div",{className:"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded",children:"Continuous Chat"})]}),p.jsxs("div",{className:"flex items-center gap-1",children:[p.jsx(De,{size:"icon",variant:"ghost",onClick:Dt,className:"h-8 w-8 hover:bg-yellow-100",title:"Clear chat history",children:p.jsx(U0,{className:"h-4 w-4"})}),p.jsx(De,{size:"icon",variant:"ghost",onClick:()=>St(!ot),className:"h-8 w-8 hover:bg-blue-100",children:ot?p.jsx(w0,{className:"h-4 w-4"}):p.jsx(R0,{className:"h-4 w-4"})}),p.jsx(De,{size:"icon",variant:"ghost",onClick:()=>yt(!1),className:"h-8 w-8 hover:bg-red-100",children:p.jsx(L0,{className:"h-4 w-4"})})]})]}),p.jsx(ri,{className:"flex-1 p-4 overflow-hidden bg-white",children:p.jsx(Ig,{className:"h-full pr-4",children:p.jsxs("div",{className:"space-y-4",children:[N.length===0&&p.jsxs("div",{className:"text-center text-gray-500 py-8",children:[p.jsx(Cr,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),p.jsx("p",{className:"text-sm font-medium",children:"AI Book Assistant - Continuous Chat"}),p.jsx("p",{className:"text-xs mt-2",children:"I can help you find books and provide PDF downloads!"}),p.jsxs("div",{className:"mt-3 text-xs space-y-1",children:[p.jsx("p",{children:'• "Find me Rich Dad Poor Dad PDF"'}),p.jsx("p",{children:'• "Recommend finance books"'}),p.jsx("p",{children:'• "Tell me about [book name]"'}),p.jsx("p",{children:'• "Get me PDF of [book name]"'})]}),p.jsx("p",{className:"text-xs mt-3 text-blue-600",children:"💡 I remember our conversation!"})]}),N.map((k,at)=>p.jsx("div",{className:`flex ${k.sender==="user"?"justify-end":"justify-start"}`,children:p.jsxs("div",{className:`max-w-[85%] p-3 rounded-lg shadow-sm ${k.sender==="user"?"bg-blue-500 text-white":"bg-gray-100 text-gray-800 border"}`,children:[p.jsx("div",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:k.text}),k.pdfResults&&k.pdfResults.length>0&&p.jsxs("div",{className:"mt-3 space-y-2",children:[p.jsxs("div",{className:"text-xs font-medium text-green-700 flex items-center gap-1",children:[p.jsx(Ba,{className:"h-3 w-3"}),"PDF Downloads Found:"]}),k.pdfResults.map((x,H)=>{var D;return p.jsxs("div",{className:"bg-green-50 border border-green-200 p-2 rounded text-xs",children:[p.jsx("div",{className:"font-medium text-green-800",children:x.title}),x.author&&p.jsxs("div",{className:"text-green-600 text-xs",children:["by ",x.author]}),p.jsx("div",{className:"mt-1 space-y-1",children:(D=x.pdf_links)==null?void 0:D.map((P,h)=>p.jsxs("a",{href:P.url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-1 text-green-700 hover:text-green-900 underline text-xs",children:[p.jsx(Ba,{className:"h-3 w-3"}),"Download PDF (",P.source,")"]},h))})]},H)})]})]})},at)),J&&p.jsx("div",{className:"flex justify-start",children:p.jsx("div",{className:"max-w-[85%] p-3 rounded-lg bg-gray-100 text-gray-800 border animate-pulse",children:p.jsxs("div",{className:"flex items-center gap-2",children:[p.jsxs("div",{className:"flex space-x-1",children:[p.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),p.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),p.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),p.jsx("span",{className:"text-sm",children:$.llmLoading})]})})})]})})}),p.jsxs("div",{className:"p-4 border-t bg-gray-50 flex items-center gap-2",children:[p.jsx(nm,{placeholder:$.chatPlaceholder,value:B,onChange:k=>Z(k.target.value),onKeyPress:k=>k.key==="Enter"&&nt(),className:"flex-1 bg-white",dir:f==="ar"?"rtl":"ltr"}),p.jsx(De,{size:"icon",onClick:nt,disabled:J||!B.trim(),className:"bg-blue-600 hover:bg-blue-700",children:p.jsx(E0,{className:"h-4 w-4"})})]})]}),!Ct&&p.jsx(De,{onClick:()=>yt(!0),className:"fixed bottom-4 right-4 h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg z-50",size:"icon",children:p.jsx(Cr,{className:"h-6 w-6"})})]})})}g0.createRoot(document.getElementById("root")).render(p.jsx(C.StrictMode,{children:p.jsx(ey,{})}));
