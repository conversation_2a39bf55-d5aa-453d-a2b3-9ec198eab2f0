(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const m of document.querySelectorAll('link[rel="modulepreload"]'))o(m);new MutationObserver(m=>{for(const b of m)if(b.type==="childList")for(const E of b.addedNodes)E.tagName==="LINK"&&E.rel==="modulepreload"&&o(E)}).observe(document,{childList:!0,subtree:!0});function f(m){const b={};return m.integrity&&(b.integrity=m.integrity),m.referrerPolicy&&(b.referrerPolicy=m.referrerPolicy),m.crossOrigin==="use-credentials"?b.credentials="include":m.crossOrigin==="anonymous"?b.credentials="omit":b.credentials="same-origin",b}function o(m){if(m.ep)return;m.ep=!0;const b=f(m);fetch(m.href,b)}})();var _r={exports:{}},Yn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qd;function i0(){if(qd)return Yn;qd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function f(o,m,b){var E=null;if(b!==void 0&&(E=""+b),m.key!==void 0&&(E=""+m.key),"key"in m){b={};for(var N in m)N!=="key"&&(b[N]=m[N])}else b=m;return m=b.ref,{$$typeof:c,type:o,key:E,ref:m!==void 0?m:null,props:b}}return Yn.Fragment=s,Yn.jsx=f,Yn.jsxs=f,Yn}var Yd;function c0(){return Yd||(Yd=1,_r.exports=i0()),_r.exports}var _=c0(),Mr={exports:{}},nt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function r0(){if(Gd)return nt;Gd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),m=Symbol.for("react.profiler"),b=Symbol.for("react.consumer"),E=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),M=Symbol.for("react.lazy"),C=Symbol.iterator;function H(h){return h===null||typeof h!="object"?null:(h=C&&h[C]||h["@@iterator"],typeof h=="function"?h:null)}var k={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},J=Object.assign,tt={};function ot(h,U,Y){this.props=h,this.context=U,this.refs=tt,this.updater=Y||k}ot.prototype.isReactComponent={},ot.prototype.setState=function(h,U){if(typeof h!="object"&&typeof h!="function"&&h!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,h,U,"setState")},ot.prototype.forceUpdate=function(h){this.updater.enqueueForceUpdate(this,h,"forceUpdate")};function Tt(){}Tt.prototype=ot.prototype;function jt(h,U,Y){this.props=h,this.context=U,this.refs=tt,this.updater=Y||k}var pt=jt.prototype=new Tt;pt.constructor=jt,J(pt,ot.prototype),pt.isPureReactComponent=!0;var et=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},St=Object.prototype.hasOwnProperty;function Mt(h,U,Y,B,G,ft){return Y=ft.ref,{$$typeof:c,type:h,key:U,ref:Y!==void 0?Y:null,props:ft}}function Z(h,U){return Mt(h.type,U,void 0,void 0,void 0,h.props)}function at(h){return typeof h=="object"&&h!==null&&h.$$typeof===c}function Ut(h){var U={"=":"=0",":":"=2"};return"$"+h.replace(/[=:]/g,function(Y){return U[Y]})}var X=/\/+/g;function lt(h,U){return typeof h=="object"&&h!==null&&h.key!=null?Ut(""+h.key):U.toString(36)}function ut(){}function Nt(h){switch(h.status){case"fulfilled":return h.value;case"rejected":throw h.reason;default:switch(typeof h.status=="string"?h.then(ut,ut):(h.status="pending",h.then(function(U){h.status==="pending"&&(h.status="fulfilled",h.value=U)},function(U){h.status==="pending"&&(h.status="rejected",h.reason=U)})),h.status){case"fulfilled":return h.value;case"rejected":throw h.reason}}throw h}function st(h,U,Y,B,G){var ft=typeof h;(ft==="undefined"||ft==="boolean")&&(h=null);var I=!1;if(h===null)I=!0;else switch(ft){case"bigint":case"string":case"number":I=!0;break;case"object":switch(h.$$typeof){case c:case s:I=!0;break;case M:return I=h._init,st(I(h._payload),U,Y,B,G)}}if(I)return G=G(h),I=B===""?"."+lt(h,0):B,et(G)?(Y="",I!=null&&(Y=I.replace(X,"$&/")+"/"),st(G,U,Y,"",function(ce){return ce})):G!=null&&(at(G)&&(G=Z(G,Y+(G.key==null||h&&h.key===G.key?"":(""+G.key).replace(X,"$&/")+"/")+I)),U.push(G)),1;I=0;var vt=B===""?".":B+":";if(et(h))for(var Rt=0;Rt<h.length;Rt++)B=h[Rt],ft=vt+lt(B,Rt),I+=st(B,U,Y,ft,G);else if(Rt=H(h),typeof Rt=="function")for(h=Rt.call(h),Rt=0;!(B=h.next()).done;)B=B.value,ft=vt+lt(B,Rt++),I+=st(B,U,Y,ft,G);else if(ft==="object"){if(typeof h.then=="function")return st(Nt(h),U,Y,B,G);throw U=String(h),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(h).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return I}function z(h,U,Y){if(h==null)return h;var B=[],G=0;return st(h,B,"","",function(ft){return U.call(Y,ft,G++)}),B}function q(h){if(h._status===-1){var U=h._result;U=U(),U.then(function(Y){(h._status===0||h._status===-1)&&(h._status=1,h._result=Y)},function(Y){(h._status===0||h._status===-1)&&(h._status=2,h._result=Y)}),h._status===-1&&(h._status=0,h._result=U)}if(h._status===1)return h._result.default;throw h._result}var j=typeof reportError=="function"?reportError:function(h){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof h=="object"&&h!==null&&typeof h.message=="string"?String(h.message):String(h),error:h});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",h);return}console.error(h)};function bt(){}return nt.Children={map:z,forEach:function(h,U,Y){z(h,function(){U.apply(this,arguments)},Y)},count:function(h){var U=0;return z(h,function(){U++}),U},toArray:function(h){return z(h,function(U){return U})||[]},only:function(h){if(!at(h))throw Error("React.Children.only expected to receive a single React element child.");return h}},nt.Component=ot,nt.Fragment=f,nt.Profiler=m,nt.PureComponent=jt,nt.StrictMode=o,nt.Suspense=p,nt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,nt.__COMPILER_RUNTIME={__proto__:null,c:function(h){return P.H.useMemoCache(h)}},nt.cache=function(h){return function(){return h.apply(null,arguments)}},nt.cloneElement=function(h,U,Y){if(h==null)throw Error("The argument must be a React element, but you passed "+h+".");var B=J({},h.props),G=h.key,ft=void 0;if(U!=null)for(I in U.ref!==void 0&&(ft=void 0),U.key!==void 0&&(G=""+U.key),U)!St.call(U,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&U.ref===void 0||(B[I]=U[I]);var I=arguments.length-2;if(I===1)B.children=Y;else if(1<I){for(var vt=Array(I),Rt=0;Rt<I;Rt++)vt[Rt]=arguments[Rt+2];B.children=vt}return Mt(h.type,G,void 0,void 0,ft,B)},nt.createContext=function(h){return h={$$typeof:E,_currentValue:h,_currentValue2:h,_threadCount:0,Provider:null,Consumer:null},h.Provider=h,h.Consumer={$$typeof:b,_context:h},h},nt.createElement=function(h,U,Y){var B,G={},ft=null;if(U!=null)for(B in U.key!==void 0&&(ft=""+U.key),U)St.call(U,B)&&B!=="key"&&B!=="__self"&&B!=="__source"&&(G[B]=U[B]);var I=arguments.length-2;if(I===1)G.children=Y;else if(1<I){for(var vt=Array(I),Rt=0;Rt<I;Rt++)vt[Rt]=arguments[Rt+2];G.children=vt}if(h&&h.defaultProps)for(B in I=h.defaultProps,I)G[B]===void 0&&(G[B]=I[B]);return Mt(h,ft,void 0,void 0,null,G)},nt.createRef=function(){return{current:null}},nt.forwardRef=function(h){return{$$typeof:N,render:h}},nt.isValidElement=at,nt.lazy=function(h){return{$$typeof:M,_payload:{_status:-1,_result:h},_init:q}},nt.memo=function(h,U){return{$$typeof:y,type:h,compare:U===void 0?null:U}},nt.startTransition=function(h){var U=P.T,Y={};P.T=Y;try{var B=h(),G=P.S;G!==null&&G(Y,B),typeof B=="object"&&B!==null&&typeof B.then=="function"&&B.then(bt,j)}catch(ft){j(ft)}finally{P.T=U}},nt.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},nt.use=function(h){return P.H.use(h)},nt.useActionState=function(h,U,Y){return P.H.useActionState(h,U,Y)},nt.useCallback=function(h,U){return P.H.useCallback(h,U)},nt.useContext=function(h){return P.H.useContext(h)},nt.useDebugValue=function(){},nt.useDeferredValue=function(h,U){return P.H.useDeferredValue(h,U)},nt.useEffect=function(h,U,Y){var B=P.H;if(typeof Y=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return B.useEffect(h,U)},nt.useId=function(){return P.H.useId()},nt.useImperativeHandle=function(h,U,Y){return P.H.useImperativeHandle(h,U,Y)},nt.useInsertionEffect=function(h,U){return P.H.useInsertionEffect(h,U)},nt.useLayoutEffect=function(h,U){return P.H.useLayoutEffect(h,U)},nt.useMemo=function(h,U){return P.H.useMemo(h,U)},nt.useOptimistic=function(h,U){return P.H.useOptimistic(h,U)},nt.useReducer=function(h,U,Y){return P.H.useReducer(h,U,Y)},nt.useRef=function(h){return P.H.useRef(h)},nt.useState=function(h){return P.H.useState(h)},nt.useSyncExternalStore=function(h,U,Y){return P.H.useSyncExternalStore(h,U,Y)},nt.useTransition=function(){return P.H.useTransition()},nt.version="19.1.0",nt}var Xd;function Yr(){return Xd||(Xd=1,Mr.exports=r0()),Mr.exports}var w=Yr(),Nr={exports:{}},Gn={},Rr={exports:{}},Or={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ld;function o0(){return Ld||(Ld=1,function(c){function s(z,q){var j=z.length;z.push(q);t:for(;0<j;){var bt=j-1>>>1,h=z[bt];if(0<m(h,q))z[bt]=q,z[j]=h,j=bt;else break t}}function f(z){return z.length===0?null:z[0]}function o(z){if(z.length===0)return null;var q=z[0],j=z.pop();if(j!==q){z[0]=j;t:for(var bt=0,h=z.length,U=h>>>1;bt<U;){var Y=2*(bt+1)-1,B=z[Y],G=Y+1,ft=z[G];if(0>m(B,j))G<h&&0>m(ft,B)?(z[bt]=ft,z[G]=j,bt=G):(z[bt]=B,z[Y]=j,bt=Y);else if(G<h&&0>m(ft,j))z[bt]=ft,z[G]=j,bt=G;else break t}}return q}function m(z,q){var j=z.sortIndex-q.sortIndex;return j!==0?j:z.id-q.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var b=performance;c.unstable_now=function(){return b.now()}}else{var E=Date,N=E.now();c.unstable_now=function(){return E.now()-N}}var p=[],y=[],M=1,C=null,H=3,k=!1,J=!1,tt=!1,ot=!1,Tt=typeof setTimeout=="function"?setTimeout:null,jt=typeof clearTimeout=="function"?clearTimeout:null,pt=typeof setImmediate<"u"?setImmediate:null;function et(z){for(var q=f(y);q!==null;){if(q.callback===null)o(y);else if(q.startTime<=z)o(y),q.sortIndex=q.expirationTime,s(p,q);else break;q=f(y)}}function P(z){if(tt=!1,et(z),!J)if(f(p)!==null)J=!0,St||(St=!0,lt());else{var q=f(y);q!==null&&st(P,q.startTime-z)}}var St=!1,Mt=-1,Z=5,at=-1;function Ut(){return ot?!0:!(c.unstable_now()-at<Z)}function X(){if(ot=!1,St){var z=c.unstable_now();at=z;var q=!0;try{t:{J=!1,tt&&(tt=!1,jt(Mt),Mt=-1),k=!0;var j=H;try{e:{for(et(z),C=f(p);C!==null&&!(C.expirationTime>z&&Ut());){var bt=C.callback;if(typeof bt=="function"){C.callback=null,H=C.priorityLevel;var h=bt(C.expirationTime<=z);if(z=c.unstable_now(),typeof h=="function"){C.callback=h,et(z),q=!0;break e}C===f(p)&&o(p),et(z)}else o(p);C=f(p)}if(C!==null)q=!0;else{var U=f(y);U!==null&&st(P,U.startTime-z),q=!1}}break t}finally{C=null,H=j,k=!1}q=void 0}}finally{q?lt():St=!1}}}var lt;if(typeof pt=="function")lt=function(){pt(X)};else if(typeof MessageChannel<"u"){var ut=new MessageChannel,Nt=ut.port2;ut.port1.onmessage=X,lt=function(){Nt.postMessage(null)}}else lt=function(){Tt(X,0)};function st(z,q){Mt=Tt(function(){z(c.unstable_now())},q)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(z){z.callback=null},c.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Z=0<z?Math.floor(1e3/z):5},c.unstable_getCurrentPriorityLevel=function(){return H},c.unstable_next=function(z){switch(H){case 1:case 2:case 3:var q=3;break;default:q=H}var j=H;H=q;try{return z()}finally{H=j}},c.unstable_requestPaint=function(){ot=!0},c.unstable_runWithPriority=function(z,q){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var j=H;H=z;try{return q()}finally{H=j}},c.unstable_scheduleCallback=function(z,q,j){var bt=c.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?bt+j:bt):j=bt,z){case 1:var h=-1;break;case 2:h=250;break;case 5:h=1073741823;break;case 4:h=1e4;break;default:h=5e3}return h=j+h,z={id:M++,callback:q,priorityLevel:z,startTime:j,expirationTime:h,sortIndex:-1},j>bt?(z.sortIndex=j,s(y,z),f(p)===null&&z===f(y)&&(tt?(jt(Mt),Mt=-1):tt=!0,st(P,j-bt))):(z.sortIndex=h,s(p,z),J||k||(J=!0,St||(St=!0,lt()))),z},c.unstable_shouldYield=Ut,c.unstable_wrapCallback=function(z){var q=H;return function(){var j=H;H=q;try{return z.apply(this,arguments)}finally{H=j}}}}(Or)),Or}var Qd;function s0(){return Qd||(Qd=1,Rr.exports=o0()),Rr.exports}var Dr={exports:{}},te={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vd;function f0(){if(Vd)return te;Vd=1;var c=Yr();function s(p){var y="https://react.dev/errors/"+p;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var M=2;M<arguments.length;M++)y+="&args[]="+encodeURIComponent(arguments[M])}return"Minified React error #"+p+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var o={d:{f,r:function(){throw Error(s(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},m=Symbol.for("react.portal");function b(p,y,M){var C=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:m,key:C==null?null:""+C,children:p,containerInfo:y,implementation:M}}var E=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function N(p,y){if(p==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,te.createPortal=function(p,y){var M=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(s(299));return b(p,y,null,M)},te.flushSync=function(p){var y=E.T,M=o.p;try{if(E.T=null,o.p=2,p)return p()}finally{E.T=y,o.p=M,o.d.f()}},te.preconnect=function(p,y){typeof p=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,o.d.C(p,y))},te.prefetchDNS=function(p){typeof p=="string"&&o.d.D(p)},te.preinit=function(p,y){if(typeof p=="string"&&y&&typeof y.as=="string"){var M=y.as,C=N(M,y.crossOrigin),H=typeof y.integrity=="string"?y.integrity:void 0,k=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;M==="style"?o.d.S(p,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:C,integrity:H,fetchPriority:k}):M==="script"&&o.d.X(p,{crossOrigin:C,integrity:H,fetchPriority:k,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},te.preinitModule=function(p,y){if(typeof p=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var M=N(y.as,y.crossOrigin);o.d.M(p,{crossOrigin:M,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&o.d.M(p)},te.preload=function(p,y){if(typeof p=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var M=y.as,C=N(M,y.crossOrigin);o.d.L(p,M,{crossOrigin:C,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},te.preloadModule=function(p,y){if(typeof p=="string")if(y){var M=N(y.as,y.crossOrigin);o.d.m(p,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:M,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else o.d.m(p)},te.requestFormReset=function(p){o.d.r(p)},te.unstable_batchedUpdates=function(p,y){return p(y)},te.useFormState=function(p,y,M){return E.H.useFormState(p,y,M)},te.useFormStatus=function(){return E.H.useHostTransitionStatus()},te.version="19.1.0",te}var Zd;function cm(){if(Zd)return Dr.exports;Zd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Dr.exports=f0(),Dr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kd;function d0(){if(kd)return Gn;kd=1;var c=s0(),s=Yr(),f=cm();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function m(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function b(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function E(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function N(t){if(b(t)!==t)throw Error(o(188))}function p(t){var e=t.alternate;if(!e){if(e=b(t),e===null)throw Error(o(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return N(n),t;if(u===a)return N(n),e;u=u.sibling}throw Error(o(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,r=n.child;r;){if(r===l){i=!0,l=n,a=u;break}if(r===a){i=!0,a=n,l=u;break}r=r.sibling}if(!i){for(r=u.child;r;){if(r===l){i=!0,l=u,a=n;break}if(r===a){i=!0,a=u,l=n;break}r=r.sibling}if(!i)throw Error(o(189))}}if(l.alternate!==a)throw Error(o(190))}if(l.tag!==3)throw Error(o(188));return l.stateNode.current===l?t:e}function y(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=y(t),e!==null)return e;t=t.sibling}return null}var M=Object.assign,C=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),k=Symbol.for("react.portal"),J=Symbol.for("react.fragment"),tt=Symbol.for("react.strict_mode"),ot=Symbol.for("react.profiler"),Tt=Symbol.for("react.provider"),jt=Symbol.for("react.consumer"),pt=Symbol.for("react.context"),et=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),St=Symbol.for("react.suspense_list"),Mt=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),at=Symbol.for("react.activity"),Ut=Symbol.for("react.memo_cache_sentinel"),X=Symbol.iterator;function lt(t){return t===null||typeof t!="object"?null:(t=X&&t[X]||t["@@iterator"],typeof t=="function"?t:null)}var ut=Symbol.for("react.client.reference");function Nt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ut?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case J:return"Fragment";case ot:return"Profiler";case tt:return"StrictMode";case P:return"Suspense";case St:return"SuspenseList";case at:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case k:return"Portal";case pt:return(t.displayName||"Context")+".Provider";case jt:return(t._context.displayName||"Context")+".Consumer";case et:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Mt:return e=t.displayName||null,e!==null?e:Nt(t.type)||"Memo";case Z:e=t._payload,t=t._init;try{return Nt(t(e))}catch{}}return null}var st=Array.isArray,z=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j={pending:!1,data:null,method:null,action:null},bt=[],h=-1;function U(t){return{current:t}}function Y(t){0>h||(t.current=bt[h],bt[h]=null,h--)}function B(t,e){h++,bt[h]=t.current,t.current=e}var G=U(null),ft=U(null),I=U(null),vt=U(null);function Rt(t,e){switch(B(I,e),B(ft,t),B(G,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?dd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=dd(e),t=md(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Y(G),B(G,t)}function ce(){Y(G),Y(ft),Y(I)}function ll(t){t.memoizedState!==null&&B(vt,t);var e=G.current,l=md(e,t.type);e!==l&&(B(ft,t),B(G,l))}function al(t){ft.current===t&&(Y(G),Y(ft)),vt.current===t&&(Y(vt),Cn._currentValue=j)}var nl=Object.prototype.hasOwnProperty,mi=c.unstable_scheduleCallback,hi=c.unstable_cancelCallback,qm=c.unstable_shouldYield,Ym=c.unstable_requestPaint,Oe=c.unstable_now,Gm=c.unstable_getCurrentPriorityLevel,Vr=c.unstable_ImmediatePriority,Zr=c.unstable_UserBlockingPriority,Vn=c.unstable_NormalPriority,Xm=c.unstable_LowPriority,kr=c.unstable_IdlePriority,Lm=c.log,Qm=c.unstable_setDisableYieldValue,Xa=null,re=null;function ul(t){if(typeof Lm=="function"&&Qm(t),re&&typeof re.setStrictMode=="function")try{re.setStrictMode(Xa,t)}catch{}}var oe=Math.clz32?Math.clz32:km,Vm=Math.log,Zm=Math.LN2;function km(t){return t>>>=0,t===0?32:31-(Vm(t)/Zm|0)|0}var Zn=256,kn=4194304;function Ol(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Kn(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var r=a&134217727;return r!==0?(a=r&~u,a!==0?n=Ol(a):(i&=r,i!==0?n=Ol(i):l||(l=r&~t,l!==0&&(n=Ol(l))))):(r=a&~u,r!==0?n=Ol(r):i!==0?n=Ol(i):l||(l=a&~t,l!==0&&(n=Ol(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function La(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Km(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Kr(){var t=Zn;return Zn<<=1,(Zn&4194048)===0&&(Zn=256),t}function Jr(){var t=kn;return kn<<=1,(kn&62914560)===0&&(kn=4194304),t}function vi(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Qa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Jm(t,e,l,a,n,u){var i=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var r=t.entanglements,d=t.expirationTimes,x=t.hiddenUpdates;for(l=i&~l;0<l;){var R=31-oe(l),D=1<<R;r[R]=0,d[R]=-1;var A=x[R];if(A!==null)for(x[R]=null,R=0;R<A.length;R++){var T=A[R];T!==null&&(T.lane&=-536870913)}l&=~D}a!==0&&Wr(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~e))}function Wr(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-oe(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function $r(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-oe(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function yi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function gi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Fr(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:wd(t.type))}function Wm(t,e){var l=q.p;try{return q.p=t,e()}finally{q.p=l}}var il=Math.random().toString(36).slice(2),Pt="__reactFiber$"+il,le="__reactProps$"+il,Pl="__reactContainer$"+il,bi="__reactEvents$"+il,$m="__reactListeners$"+il,Fm="__reactHandles$"+il,Pr="__reactResources$"+il,Va="__reactMarker$"+il;function pi(t){delete t[Pt],delete t[le],delete t[bi],delete t[$m],delete t[Fm]}function Il(t){var e=t[Pt];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Pl]||l[Pt]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=gd(t);t!==null;){if(l=t[Pt])return l;t=gd(t)}return e}t=l,l=t.parentNode}return null}function ta(t){if(t=t[Pt]||t[Pl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Za(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function ea(t){var e=t[Pr];return e||(e=t[Pr]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[Va]=!0}var Ir=new Set,to={};function Dl(t,e){la(t,e),la(t+"Capture",e)}function la(t,e){for(to[t]=e,t=0;t<e.length;t++)Ir.add(e[t])}var Pm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),eo={},lo={};function Im(t){return nl.call(lo,t)?!0:nl.call(eo,t)?!1:Pm.test(t)?lo[t]=!0:(eo[t]=!0,!1)}function Jn(t,e,l){if(Im(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Wn(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Ye(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var Si,ao;function aa(t){if(Si===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);Si=e&&e[1]||"",ao=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Si+t+ao}var xi=!1;function Ai(t,e){if(!t||xi)return"";xi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var D=function(){throw Error()};if(Object.defineProperty(D.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(D,[])}catch(T){var A=T}Reflect.construct(t,[],D)}else{try{D.call()}catch(T){A=T}t.call(D.prototype)}}else{try{throw Error()}catch(T){A=T}(D=t())&&typeof D.catch=="function"&&D.catch(function(){})}}catch(T){if(T&&A&&typeof T.stack=="string")return[T.stack,A.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],r=u[1];if(i&&r){var d=i.split(`
`),x=r.split(`
`);for(n=a=0;a<d.length&&!d[a].includes("DetermineComponentFrameRoot");)a++;for(;n<x.length&&!x[n].includes("DetermineComponentFrameRoot");)n++;if(a===d.length||n===x.length)for(a=d.length-1,n=x.length-1;1<=a&&0<=n&&d[a]!==x[n];)n--;for(;1<=a&&0<=n;a--,n--)if(d[a]!==x[n]){if(a!==1||n!==1)do if(a--,n--,0>n||d[a]!==x[n]){var R=`
`+d[a].replace(" at new "," at ");return t.displayName&&R.includes("<anonymous>")&&(R=R.replace("<anonymous>",t.displayName)),R}while(1<=a&&0<=n);break}}}finally{xi=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?aa(l):""}function th(t){switch(t.tag){case 26:case 27:case 5:return aa(t.type);case 16:return aa("Lazy");case 13:return aa("Suspense");case 19:return aa("SuspenseList");case 0:case 15:return Ai(t.type,!1);case 11:return Ai(t.type.render,!1);case 1:return Ai(t.type,!0);case 31:return aa("Activity");default:return""}}function no(t){try{var e="";do e+=th(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function ge(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function uo(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function eh(t){var e=uo(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function $n(t){t._valueTracker||(t._valueTracker=eh(t))}function io(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=uo(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Fn(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var lh=/[\n"\\]/g;function be(t){return t.replace(lh,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ti(t,e,l,a,n,u,i,r){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),e!=null?i==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ge(e)):t.value!==""+ge(e)&&(t.value=""+ge(e)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),e!=null?Ei(t,i,ge(e)):l!=null?Ei(t,i,ge(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?t.name=""+ge(r):t.removeAttribute("name")}function co(t,e,l,a,n,u,i,r){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+ge(l):"",e=e!=null?""+ge(e):l,r||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=r?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function Ei(t,e,l){e==="number"&&Fn(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function na(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+ge(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function ro(t,e,l){if(e!=null&&(e=""+ge(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+ge(l):""}function oo(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(o(92));if(st(a)){if(1<a.length)throw Error(o(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=ge(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function ua(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var ah=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function so(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||ah.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function fo(t,e,l){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&so(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&so(t,u,e[u])}function zi(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var nh=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),uh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pn(t){return uh.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var _i=null;function Mi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ia=null,ca=null;function mo(t){var e=ta(t);if(e&&(t=e.stateNode)){var l=t[le]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ti(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+be(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[le]||null;if(!n)throw Error(o(90));Ti(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&io(a)}break t;case"textarea":ro(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&na(t,!!l.multiple,e,!1)}}}var Ni=!1;function ho(t,e,l){if(Ni)return t(e,l);Ni=!0;try{var a=t(e);return a}finally{if(Ni=!1,(ia!==null||ca!==null)&&(Bu(),ia&&(e=ia,t=ca,ca=ia=null,mo(e),t)))for(e=0;e<t.length;e++)mo(t[e])}}function ka(t,e){var l=t.stateNode;if(l===null)return null;var a=l[le]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(o(231,e,typeof l));return l}var Ge=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ri=!1;if(Ge)try{var Ka={};Object.defineProperty(Ka,"passive",{get:function(){Ri=!0}}),window.addEventListener("test",Ka,Ka),window.removeEventListener("test",Ka,Ka)}catch{Ri=!1}var cl=null,Oi=null,In=null;function vo(){if(In)return In;var t,e=Oi,l=e.length,a,n="value"in cl?cl.value:cl.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var i=l-t;for(a=1;a<=i&&e[l-a]===n[u-a];a++);return In=n.slice(t,1<a?1-a:void 0)}function tu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function eu(){return!0}function yo(){return!1}function ae(t){function e(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var r in t)t.hasOwnProperty(r)&&(l=t[r],this[r]=l?l(u):u[r]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?eu:yo,this.isPropagationStopped=yo,this}return M(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=eu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=eu)},persist:function(){},isPersistent:eu}),e}var wl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},lu=ae(wl),Ja=M({},wl,{view:0,detail:0}),ih=ae(Ja),Di,wi,Wa,au=M({},Ja,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ci,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Wa&&(Wa&&t.type==="mousemove"?(Di=t.screenX-Wa.screenX,wi=t.screenY-Wa.screenY):wi=Di=0,Wa=t),Di)},movementY:function(t){return"movementY"in t?t.movementY:wi}}),go=ae(au),ch=M({},au,{dataTransfer:0}),rh=ae(ch),oh=M({},Ja,{relatedTarget:0}),Ui=ae(oh),sh=M({},wl,{animationName:0,elapsedTime:0,pseudoElement:0}),fh=ae(sh),dh=M({},wl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),mh=ae(dh),hh=M({},wl,{data:0}),bo=ae(hh),vh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},yh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bh(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=gh[t])?!!e[t]:!1}function Ci(){return bh}var ph=M({},Ja,{key:function(t){if(t.key){var e=vh[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=tu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?yh[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ci,charCode:function(t){return t.type==="keypress"?tu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?tu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Sh=ae(ph),xh=M({},au,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),po=ae(xh),Ah=M({},Ja,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ci}),Th=ae(Ah),Eh=M({},wl,{propertyName:0,elapsedTime:0,pseudoElement:0}),zh=ae(Eh),_h=M({},au,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Mh=ae(_h),Nh=M({},wl,{newState:0,oldState:0}),Rh=ae(Nh),Oh=[9,13,27,32],ji=Ge&&"CompositionEvent"in window,$a=null;Ge&&"documentMode"in document&&($a=document.documentMode);var Dh=Ge&&"TextEvent"in window&&!$a,So=Ge&&(!ji||$a&&8<$a&&11>=$a),xo=" ",Ao=!1;function To(t,e){switch(t){case"keyup":return Oh.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Eo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ra=!1;function wh(t,e){switch(t){case"compositionend":return Eo(e);case"keypress":return e.which!==32?null:(Ao=!0,xo);case"textInput":return t=e.data,t===xo&&Ao?null:t;default:return null}}function Uh(t,e){if(ra)return t==="compositionend"||!ji&&To(t,e)?(t=vo(),In=Oi=cl=null,ra=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return So&&e.locale!=="ko"?null:e.data;default:return null}}var Ch={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Ch[t.type]:e==="textarea"}function _o(t,e,l,a){ia?ca?ca.push(a):ca=[a]:ia=a,e=Qu(e,"onChange"),0<e.length&&(l=new lu("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Fa=null,Pa=null;function jh(t){cd(t,0)}function nu(t){var e=Za(t);if(io(e))return t}function Mo(t,e){if(t==="change")return e}var No=!1;if(Ge){var Hi;if(Ge){var Bi="oninput"in document;if(!Bi){var Ro=document.createElement("div");Ro.setAttribute("oninput","return;"),Bi=typeof Ro.oninput=="function"}Hi=Bi}else Hi=!1;No=Hi&&(!document.documentMode||9<document.documentMode)}function Oo(){Fa&&(Fa.detachEvent("onpropertychange",Do),Pa=Fa=null)}function Do(t){if(t.propertyName==="value"&&nu(Pa)){var e=[];_o(e,Pa,t,Mi(t)),ho(jh,e)}}function Hh(t,e,l){t==="focusin"?(Oo(),Fa=e,Pa=l,Fa.attachEvent("onpropertychange",Do)):t==="focusout"&&Oo()}function Bh(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return nu(Pa)}function qh(t,e){if(t==="click")return nu(e)}function Yh(t,e){if(t==="input"||t==="change")return nu(e)}function Gh(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var se=typeof Object.is=="function"?Object.is:Gh;function Ia(t,e){if(se(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!nl.call(e,n)||!se(t[n],e[n]))return!1}return!0}function wo(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Uo(t,e){var l=wo(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=wo(l)}}function Co(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Co(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function jo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Fn(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Fn(t.document)}return e}function qi(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Xh=Ge&&"documentMode"in document&&11>=document.documentMode,oa=null,Yi=null,tn=null,Gi=!1;function Ho(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Gi||oa==null||oa!==Fn(a)||(a=oa,"selectionStart"in a&&qi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),tn&&Ia(tn,a)||(tn=a,a=Qu(Yi,"onSelect"),0<a.length&&(e=new lu("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=oa)))}function Ul(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var sa={animationend:Ul("Animation","AnimationEnd"),animationiteration:Ul("Animation","AnimationIteration"),animationstart:Ul("Animation","AnimationStart"),transitionrun:Ul("Transition","TransitionRun"),transitionstart:Ul("Transition","TransitionStart"),transitioncancel:Ul("Transition","TransitionCancel"),transitionend:Ul("Transition","TransitionEnd")},Xi={},Bo={};Ge&&(Bo=document.createElement("div").style,"AnimationEvent"in window||(delete sa.animationend.animation,delete sa.animationiteration.animation,delete sa.animationstart.animation),"TransitionEvent"in window||delete sa.transitionend.transition);function Cl(t){if(Xi[t])return Xi[t];if(!sa[t])return t;var e=sa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Bo)return Xi[t]=e[l];return t}var qo=Cl("animationend"),Yo=Cl("animationiteration"),Go=Cl("animationstart"),Lh=Cl("transitionrun"),Qh=Cl("transitionstart"),Vh=Cl("transitioncancel"),Xo=Cl("transitionend"),Lo=new Map,Li="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Li.push("scrollEnd");function Me(t,e){Lo.set(t,e),Dl(e,[t])}var Qo=new WeakMap;function pe(t,e){if(typeof t=="object"&&t!==null){var l=Qo.get(t);return l!==void 0?l:(e={value:t,source:e,stack:no(e)},Qo.set(t,e),e)}return{value:t,source:e,stack:no(e)}}var Se=[],fa=0,Qi=0;function uu(){for(var t=fa,e=Qi=fa=0;e<t;){var l=Se[e];Se[e++]=null;var a=Se[e];Se[e++]=null;var n=Se[e];Se[e++]=null;var u=Se[e];if(Se[e++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&Vo(l,n,u)}}function iu(t,e,l,a){Se[fa++]=t,Se[fa++]=e,Se[fa++]=l,Se[fa++]=a,Qi|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Vi(t,e,l,a){return iu(t,e,l,a),cu(t)}function da(t,e){return iu(t,null,null,e),cu(t)}function Vo(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-oe(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function cu(t){if(50<_n)throw _n=0,$c=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ma={};function Zh(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function fe(t,e,l,a){return new Zh(t,e,l,a)}function Zi(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Xe(t,e){var l=t.alternate;return l===null?(l=fe(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Zo(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ru(t,e,l,a,n,u){var i=0;if(a=t,typeof t=="function")Zi(t)&&(i=1);else if(typeof t=="string")i=Kv(t,l,G.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case at:return t=fe(31,l,e,n),t.elementType=at,t.lanes=u,t;case J:return jl(l.children,n,u,e);case tt:i=8,n|=24;break;case ot:return t=fe(12,l,e,n|2),t.elementType=ot,t.lanes=u,t;case P:return t=fe(13,l,e,n),t.elementType=P,t.lanes=u,t;case St:return t=fe(19,l,e,n),t.elementType=St,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Tt:case pt:i=10;break t;case jt:i=9;break t;case et:i=11;break t;case Mt:i=14;break t;case Z:i=16,a=null;break t}i=29,l=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=fe(i,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function jl(t,e,l,a){return t=fe(7,t,a,e),t.lanes=l,t}function ki(t,e,l){return t=fe(6,t,null,e),t.lanes=l,t}function Ki(t,e,l){return e=fe(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ha=[],va=0,ou=null,su=0,xe=[],Ae=0,Hl=null,Le=1,Qe="";function Bl(t,e){ha[va++]=su,ha[va++]=ou,ou=t,su=e}function ko(t,e,l){xe[Ae++]=Le,xe[Ae++]=Qe,xe[Ae++]=Hl,Hl=t;var a=Le;t=Qe;var n=32-oe(a)-1;a&=~(1<<n),l+=1;var u=32-oe(e)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Le=1<<32-oe(e)+n|l<<n|a,Qe=u+t}else Le=1<<u|l<<n|a,Qe=t}function Ji(t){t.return!==null&&(Bl(t,1),ko(t,1,0))}function Wi(t){for(;t===ou;)ou=ha[--va],ha[va]=null,su=ha[--va],ha[va]=null;for(;t===Hl;)Hl=xe[--Ae],xe[Ae]=null,Qe=xe[--Ae],xe[Ae]=null,Le=xe[--Ae],xe[Ae]=null}var ee=null,Ht=null,gt=!1,ql=null,De=!1,$i=Error(o(519));function Yl(t){var e=Error(o(418,""));throw an(pe(e,t)),$i}function Ko(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[Pt]=t,e[le]=a,l){case"dialog":mt("cancel",e),mt("close",e);break;case"iframe":case"object":case"embed":mt("load",e);break;case"video":case"audio":for(l=0;l<Nn.length;l++)mt(Nn[l],e);break;case"source":mt("error",e);break;case"img":case"image":case"link":mt("error",e),mt("load",e);break;case"details":mt("toggle",e);break;case"input":mt("invalid",e),co(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),$n(e);break;case"select":mt("invalid",e);break;case"textarea":mt("invalid",e),oo(e,a.value,a.defaultValue,a.children),$n(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||fd(e.textContent,l)?(a.popover!=null&&(mt("beforetoggle",e),mt("toggle",e)),a.onScroll!=null&&mt("scroll",e),a.onScrollEnd!=null&&mt("scrollend",e),a.onClick!=null&&(e.onclick=Vu),e=!0):e=!1,e||Yl(t)}function Jo(t){for(ee=t.return;ee;)switch(ee.tag){case 5:case 13:De=!1;return;case 27:case 3:De=!0;return;default:ee=ee.return}}function en(t){if(t!==ee)return!1;if(!gt)return Jo(t),gt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||dr(t.type,t.memoizedProps)),l=!l),l&&Ht&&Yl(t),Jo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Ht=Re(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Ht=null}}else e===27?(e=Ht,Tl(t.type)?(t=yr,yr=null,Ht=t):Ht=e):Ht=ee?Re(t.stateNode.nextSibling):null;return!0}function ln(){Ht=ee=null,gt=!1}function Wo(){var t=ql;return t!==null&&(ie===null?ie=t:ie.push.apply(ie,t),ql=null),t}function an(t){ql===null?ql=[t]:ql.push(t)}var Fi=U(null),Gl=null,Ve=null;function rl(t,e,l){B(Fi,e._currentValue),e._currentValue=l}function Ze(t){t._currentValue=Fi.current,Y(Fi)}function Pi(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Ii(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var r=u;u=n;for(var d=0;d<e.length;d++)if(r.context===e[d]){u.lanes|=l,r=u.alternate,r!==null&&(r.lanes|=l),Pi(u.return,l,t),a||(i=null);break t}u=r.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(o(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),Pi(i,l,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function nn(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(o(387));if(i=i.memoizedProps,i!==null){var r=n.type;se(n.pendingProps.value,i.value)||(t!==null?t.push(r):t=[r])}}else if(n===vt.current){if(i=n.alternate,i===null)throw Error(o(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Cn):t=[Cn])}n=n.return}t!==null&&Ii(e,t,l,a),e.flags|=262144}function fu(t){for(t=t.firstContext;t!==null;){if(!se(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Xl(t){Gl=t,Ve=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function It(t){return $o(Gl,t)}function du(t,e){return Gl===null&&Xl(t),$o(t,e)}function $o(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Ve===null){if(t===null)throw Error(o(308));Ve=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ve=Ve.next=e;return l}var kh=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},Kh=c.unstable_scheduleCallback,Jh=c.unstable_NormalPriority,Lt={$$typeof:pt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function tc(){return{controller:new kh,data:new Map,refCount:0}}function un(t){t.refCount--,t.refCount===0&&Kh(Jh,function(){t.controller.abort()})}var cn=null,ec=0,ya=0,ga=null;function Wh(t,e){if(cn===null){var l=cn=[];ec=0,ya=ar(),ga={status:"pending",value:void 0,then:function(a){l.push(a)}}}return ec++,e.then(Fo,Fo),e}function Fo(){if(--ec===0&&cn!==null){ga!==null&&(ga.status="fulfilled");var t=cn;cn=null,ya=0,ga=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function $h(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Po=z.S;z.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Wh(t,e),Po!==null&&Po(t,e)};var Ll=U(null);function lc(){var t=Ll.current;return t!==null?t:Dt.pooledCache}function mu(t,e){e===null?B(Ll,Ll.current):B(Ll,e.pool)}function Io(){var t=lc();return t===null?null:{parent:Lt._currentValue,pool:t}}var rn=Error(o(460)),ts=Error(o(474)),hu=Error(o(542)),ac={then:function(){}};function es(t){return t=t.status,t==="fulfilled"||t==="rejected"}function vu(){}function ls(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(vu,vu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ns(t),t;default:if(typeof e.status=="string")e.then(vu,vu);else{if(t=Dt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ns(t),t}throw on=e,rn}}var on=null;function as(){if(on===null)throw Error(o(459));var t=on;return on=null,t}function ns(t){if(t===rn||t===hu)throw Error(o(483))}var ol=!1;function nc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function uc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function sl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function fl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(xt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=cu(t),Vo(t,null,l),e}return iu(t,a,e,l),cu(t)}function sn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,$r(t,l)}}function ic(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var cc=!1;function fn(){if(cc){var t=ga;if(t!==null)throw t}}function dn(t,e,l,a){cc=!1;var n=t.updateQueue;ol=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,r=n.shared.pending;if(r!==null){n.shared.pending=null;var d=r,x=d.next;d.next=null,i===null?u=x:i.next=x,i=d;var R=t.alternate;R!==null&&(R=R.updateQueue,r=R.lastBaseUpdate,r!==i&&(r===null?R.firstBaseUpdate=x:r.next=x,R.lastBaseUpdate=d))}if(u!==null){var D=n.baseState;i=0,R=x=d=null,r=u;do{var A=r.lane&-536870913,T=A!==r.lane;if(T?(ht&A)===A:(a&A)===A){A!==0&&A===ya&&(cc=!0),R!==null&&(R=R.next={lane:0,tag:r.tag,payload:r.payload,callback:null,next:null});t:{var F=t,W=r;A=e;var _t=l;switch(W.tag){case 1:if(F=W.payload,typeof F=="function"){D=F.call(_t,D,A);break t}D=F;break t;case 3:F.flags=F.flags&-65537|128;case 0:if(F=W.payload,A=typeof F=="function"?F.call(_t,D,A):F,A==null)break t;D=M({},D,A);break t;case 2:ol=!0}}A=r.callback,A!==null&&(t.flags|=64,T&&(t.flags|=8192),T=n.callbacks,T===null?n.callbacks=[A]:T.push(A))}else T={lane:A,tag:r.tag,payload:r.payload,callback:r.callback,next:null},R===null?(x=R=T,d=D):R=R.next=T,i|=A;if(r=r.next,r===null){if(r=n.shared.pending,r===null)break;T=r,r=T.next,T.next=null,n.lastBaseUpdate=T,n.shared.pending=null}}while(!0);R===null&&(d=D),n.baseState=d,n.firstBaseUpdate=x,n.lastBaseUpdate=R,u===null&&(n.shared.lanes=0),pl|=i,t.lanes=i,t.memoizedState=D}}function us(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function is(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)us(l[t],e)}var ba=U(null),yu=U(0);function cs(t,e){t=Pe,B(yu,t),B(ba,e),Pe=t|e.baseLanes}function rc(){B(yu,Pe),B(ba,ba.current)}function oc(){Pe=yu.current,Y(ba),Y(yu)}var dl=0,it=null,Et=null,Gt=null,gu=!1,pa=!1,Ql=!1,bu=0,mn=0,Sa=null,Fh=0;function qt(){throw Error(o(321))}function sc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!se(t[l],e[l]))return!1;return!0}function fc(t,e,l,a,n,u){return dl=u,it=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,z.H=t===null||t.memoizedState===null?Vs:Zs,Ql=!1,u=l(a,n),Ql=!1,pa&&(u=os(e,l,a,n)),rs(t),u}function rs(t){z.H=Eu;var e=Et!==null&&Et.next!==null;if(dl=0,Gt=Et=it=null,gu=!1,mn=0,Sa=null,e)throw Error(o(300));t===null||kt||(t=t.dependencies,t!==null&&fu(t)&&(kt=!0))}function os(t,e,l,a){it=t;var n=0;do{if(pa&&(Sa=null),mn=0,pa=!1,25<=n)throw Error(o(301));if(n+=1,Gt=Et=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}z.H=nv,u=e(l,a)}while(pa);return u}function Ph(){var t=z.H,e=t.useState()[0];return e=typeof e.then=="function"?hn(e):e,t=t.useState()[0],(Et!==null?Et.memoizedState:null)!==t&&(it.flags|=1024),e}function dc(){var t=bu!==0;return bu=0,t}function mc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function hc(t){if(gu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}gu=!1}dl=0,Gt=Et=it=null,pa=!1,mn=bu=0,Sa=null}function ne(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Gt===null?it.memoizedState=Gt=t:Gt=Gt.next=t,Gt}function Xt(){if(Et===null){var t=it.alternate;t=t!==null?t.memoizedState:null}else t=Et.next;var e=Gt===null?it.memoizedState:Gt.next;if(e!==null)Gt=e,Et=t;else{if(t===null)throw it.alternate===null?Error(o(467)):Error(o(310));Et=t,t={memoizedState:Et.memoizedState,baseState:Et.baseState,baseQueue:Et.baseQueue,queue:Et.queue,next:null},Gt===null?it.memoizedState=Gt=t:Gt=Gt.next=t}return Gt}function vc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function hn(t){var e=mn;return mn+=1,Sa===null&&(Sa=[]),t=ls(Sa,t,e),e=it,(Gt===null?e.memoizedState:Gt.next)===null&&(e=e.alternate,z.H=e===null||e.memoizedState===null?Vs:Zs),t}function pu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return hn(t);if(t.$$typeof===pt)return It(t)}throw Error(o(438,String(t)))}function yc(t){var e=null,l=it.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=it.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=vc(),it.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=Ut;return e.index++,l}function ke(t,e){return typeof e=="function"?e(t):e}function Su(t){var e=Xt();return gc(e,Et,t)}function gc(t,e,l){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var r=i=null,d=null,x=e,R=!1;do{var D=x.lane&-536870913;if(D!==x.lane?(ht&D)===D:(dl&D)===D){var A=x.revertLane;if(A===0)d!==null&&(d=d.next={lane:0,revertLane:0,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null}),D===ya&&(R=!0);else if((dl&A)===A){x=x.next,A===ya&&(R=!0);continue}else D={lane:0,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},d===null?(r=d=D,i=u):d=d.next=D,it.lanes|=A,pl|=A;D=x.action,Ql&&l(u,D),u=x.hasEagerState?x.eagerState:l(u,D)}else A={lane:D,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},d===null?(r=d=A,i=u):d=d.next=A,it.lanes|=D,pl|=D;x=x.next}while(x!==null&&x!==e);if(d===null?i=u:d.next=r,!se(u,t.memoizedState)&&(kt=!0,R&&(l=ga,l!==null)))throw l;t.memoizedState=u,t.baseState=i,t.baseQueue=d,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function bc(t){var e=Xt(),l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);se(u,e.memoizedState)||(kt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function ss(t,e,l){var a=it,n=Xt(),u=gt;if(u){if(l===void 0)throw Error(o(407));l=l()}else l=e();var i=!se((Et||n).memoizedState,l);i&&(n.memoizedState=l,kt=!0),n=n.queue;var r=ms.bind(null,a,n,t);if(vn(2048,8,r,[t]),n.getSnapshot!==e||i||Gt!==null&&Gt.memoizedState.tag&1){if(a.flags|=2048,xa(9,xu(),ds.bind(null,a,n,l,e),null),Dt===null)throw Error(o(349));u||(dl&124)!==0||fs(a,e,l)}return l}function fs(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=it.updateQueue,e===null?(e=vc(),it.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function ds(t,e,l,a){e.value=l,e.getSnapshot=a,hs(e)&&vs(t)}function ms(t,e,l){return l(function(){hs(e)&&vs(t)})}function hs(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!se(t,l)}catch{return!0}}function vs(t){var e=da(t,2);e!==null&&ye(e,t,2)}function pc(t){var e=ne();if(typeof t=="function"){var l=t;if(t=l(),Ql){ul(!0);try{l()}finally{ul(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:t},e}function ys(t,e,l,a){return t.baseState=l,gc(t,Et,typeof a=="function"?a:ke)}function Ih(t,e,l,a,n){if(Tu(t))throw Error(o(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};z.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,gs(e,u)):(u.next=l.next,e.pending=l.next=u)}}function gs(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=z.T,i={};z.T=i;try{var r=l(n,a),d=z.S;d!==null&&d(i,r),bs(t,e,r)}catch(x){Sc(t,e,x)}finally{z.T=u}}else try{u=l(n,a),bs(t,e,u)}catch(x){Sc(t,e,x)}}function bs(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){ps(t,e,a)},function(a){return Sc(t,e,a)}):ps(t,e,l)}function ps(t,e,l){e.status="fulfilled",e.value=l,Ss(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,gs(t,l)))}function Sc(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,Ss(e),e=e.next;while(e!==a)}t.action=null}function Ss(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function xs(t,e){return e}function As(t,e){if(gt){var l=Dt.formState;if(l!==null){t:{var a=it;if(gt){if(Ht){e:{for(var n=Ht,u=De;n.nodeType!==8;){if(!u){n=null;break e}if(n=Re(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Ht=Re(n.nextSibling),a=n.data==="F!";break t}}Yl(a)}a=!1}a&&(e=l[0])}}return l=ne(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xs,lastRenderedState:e},l.queue=a,l=Xs.bind(null,it,a),a.dispatch=l,a=pc(!1),u=zc.bind(null,it,!1,a.queue),a=ne(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=Ih.bind(null,it,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Ts(t){var e=Xt();return Es(e,Et,t)}function Es(t,e,l){if(e=gc(t,e,xs)[0],t=Su(ke)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=hn(e)}catch(i){throw i===rn?hu:i}else a=e;e=Xt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(it.flags|=2048,xa(9,xu(),tv.bind(null,n,l),null)),[a,u,t]}function tv(t,e){t.action=e}function zs(t){var e=Xt(),l=Et;if(l!==null)return Es(e,l,t);Xt(),e=e.memoizedState,l=Xt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function xa(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=it.updateQueue,e===null&&(e=vc(),it.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function xu(){return{destroy:void 0,resource:void 0}}function _s(){return Xt().memoizedState}function Au(t,e,l,a){var n=ne();a=a===void 0?null:a,it.flags|=t,n.memoizedState=xa(1|e,xu(),l,a)}function vn(t,e,l,a){var n=Xt();a=a===void 0?null:a;var u=n.memoizedState.inst;Et!==null&&a!==null&&sc(a,Et.memoizedState.deps)?n.memoizedState=xa(e,u,l,a):(it.flags|=t,n.memoizedState=xa(1|e,u,l,a))}function Ms(t,e){Au(8390656,8,t,e)}function Ns(t,e){vn(2048,8,t,e)}function Rs(t,e){return vn(4,2,t,e)}function Os(t,e){return vn(4,4,t,e)}function Ds(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function ws(t,e,l){l=l!=null?l.concat([t]):null,vn(4,4,Ds.bind(null,e,t),l)}function xc(){}function Us(t,e){var l=Xt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&sc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Cs(t,e){var l=Xt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&sc(e,a[1]))return a[0];if(a=t(),Ql){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a}function Ac(t,e,l){return l===void 0||(dl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=qf(),it.lanes|=t,pl|=t,l)}function js(t,e,l,a){return se(l,e)?l:ba.current!==null?(t=Ac(t,l,a),se(t,e)||(kt=!0),t):(dl&42)===0?(kt=!0,t.memoizedState=l):(t=qf(),it.lanes|=t,pl|=t,e)}function Hs(t,e,l,a,n){var u=q.p;q.p=u!==0&&8>u?u:8;var i=z.T,r={};z.T=r,zc(t,!1,e,l);try{var d=n(),x=z.S;if(x!==null&&x(r,d),d!==null&&typeof d=="object"&&typeof d.then=="function"){var R=$h(d,a);yn(t,e,R,ve(t))}else yn(t,e,a,ve(t))}catch(D){yn(t,e,{then:function(){},status:"rejected",reason:D},ve())}finally{q.p=u,z.T=i}}function ev(){}function Tc(t,e,l,a){if(t.tag!==5)throw Error(o(476));var n=Bs(t).queue;Hs(t,n,e,j,l===null?ev:function(){return qs(t),l(a)})}function Bs(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:j,baseState:j,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:j},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function qs(t){var e=Bs(t).next.queue;yn(t,e,{},ve())}function Ec(){return It(Cn)}function Ys(){return Xt().memoizedState}function Gs(){return Xt().memoizedState}function lv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=ve();t=sl(l);var a=fl(e,t,l);a!==null&&(ye(a,e,l),sn(a,e,l)),e={cache:tc()},t.payload=e;return}e=e.return}}function av(t,e,l){var a=ve();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Tu(t)?Ls(e,l):(l=Vi(t,e,l,a),l!==null&&(ye(l,t,a),Qs(l,e,a)))}function Xs(t,e,l){var a=ve();yn(t,e,l,a)}function yn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Tu(t))Ls(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var i=e.lastRenderedState,r=u(i,l);if(n.hasEagerState=!0,n.eagerState=r,se(r,i))return iu(t,e,n,0),Dt===null&&uu(),!1}catch{}finally{}if(l=Vi(t,e,n,a),l!==null)return ye(l,t,a),Qs(l,e,a),!0}return!1}function zc(t,e,l,a){if(a={lane:2,revertLane:ar(),action:a,hasEagerState:!1,eagerState:null,next:null},Tu(t)){if(e)throw Error(o(479))}else e=Vi(t,l,a,2),e!==null&&ye(e,t,2)}function Tu(t){var e=t.alternate;return t===it||e!==null&&e===it}function Ls(t,e){pa=gu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Qs(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,$r(t,l)}}var Eu={readContext:It,use:pu,useCallback:qt,useContext:qt,useEffect:qt,useImperativeHandle:qt,useLayoutEffect:qt,useInsertionEffect:qt,useMemo:qt,useReducer:qt,useRef:qt,useState:qt,useDebugValue:qt,useDeferredValue:qt,useTransition:qt,useSyncExternalStore:qt,useId:qt,useHostTransitionStatus:qt,useFormState:qt,useActionState:qt,useOptimistic:qt,useMemoCache:qt,useCacheRefresh:qt},Vs={readContext:It,use:pu,useCallback:function(t,e){return ne().memoizedState=[t,e===void 0?null:e],t},useContext:It,useEffect:Ms,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Au(4194308,4,Ds.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Au(4194308,4,t,e)},useInsertionEffect:function(t,e){Au(4,2,t,e)},useMemo:function(t,e){var l=ne();e=e===void 0?null:e;var a=t();if(Ql){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=ne();if(l!==void 0){var n=l(e);if(Ql){ul(!0);try{l(e)}finally{ul(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=av.bind(null,it,t),[a.memoizedState,t]},useRef:function(t){var e=ne();return t={current:t},e.memoizedState=t},useState:function(t){t=pc(t);var e=t.queue,l=Xs.bind(null,it,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:xc,useDeferredValue:function(t,e){var l=ne();return Ac(l,t,e)},useTransition:function(){var t=pc(!1);return t=Hs.bind(null,it,t.queue,!0,!1),ne().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=it,n=ne();if(gt){if(l===void 0)throw Error(o(407));l=l()}else{if(l=e(),Dt===null)throw Error(o(349));(ht&124)!==0||fs(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,Ms(ms.bind(null,a,u,t),[t]),a.flags|=2048,xa(9,xu(),ds.bind(null,a,u,l,e),null),l},useId:function(){var t=ne(),e=Dt.identifierPrefix;if(gt){var l=Qe,a=Le;l=(a&~(1<<32-oe(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=bu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=Fh++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Ec,useFormState:As,useActionState:As,useOptimistic:function(t){var e=ne();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=zc.bind(null,it,!0,l),l.dispatch=e,[t,e]},useMemoCache:yc,useCacheRefresh:function(){return ne().memoizedState=lv.bind(null,it)}},Zs={readContext:It,use:pu,useCallback:Us,useContext:It,useEffect:Ns,useImperativeHandle:ws,useInsertionEffect:Rs,useLayoutEffect:Os,useMemo:Cs,useReducer:Su,useRef:_s,useState:function(){return Su(ke)},useDebugValue:xc,useDeferredValue:function(t,e){var l=Xt();return js(l,Et.memoizedState,t,e)},useTransition:function(){var t=Su(ke)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:hn(t),e]},useSyncExternalStore:ss,useId:Ys,useHostTransitionStatus:Ec,useFormState:Ts,useActionState:Ts,useOptimistic:function(t,e){var l=Xt();return ys(l,Et,t,e)},useMemoCache:yc,useCacheRefresh:Gs},nv={readContext:It,use:pu,useCallback:Us,useContext:It,useEffect:Ns,useImperativeHandle:ws,useInsertionEffect:Rs,useLayoutEffect:Os,useMemo:Cs,useReducer:bc,useRef:_s,useState:function(){return bc(ke)},useDebugValue:xc,useDeferredValue:function(t,e){var l=Xt();return Et===null?Ac(l,t,e):js(l,Et.memoizedState,t,e)},useTransition:function(){var t=bc(ke)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:hn(t),e]},useSyncExternalStore:ss,useId:Ys,useHostTransitionStatus:Ec,useFormState:zs,useActionState:zs,useOptimistic:function(t,e){var l=Xt();return Et!==null?ys(l,Et,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:yc,useCacheRefresh:Gs},Aa=null,gn=0;function zu(t){var e=gn;return gn+=1,Aa===null&&(Aa=[]),ls(Aa,t,e)}function bn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function _u(t,e){throw e.$$typeof===C?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function ks(t){var e=t._init;return e(t._payload)}function Ks(t){function e(g,v){if(t){var S=g.deletions;S===null?(g.deletions=[v],g.flags|=16):S.push(v)}}function l(g,v){if(!t)return null;for(;v!==null;)e(g,v),v=v.sibling;return null}function a(g){for(var v=new Map;g!==null;)g.key!==null?v.set(g.key,g):v.set(g.index,g),g=g.sibling;return v}function n(g,v){return g=Xe(g,v),g.index=0,g.sibling=null,g}function u(g,v,S){return g.index=S,t?(S=g.alternate,S!==null?(S=S.index,S<v?(g.flags|=67108866,v):S):(g.flags|=67108866,v)):(g.flags|=1048576,v)}function i(g){return t&&g.alternate===null&&(g.flags|=67108866),g}function r(g,v,S,O){return v===null||v.tag!==6?(v=ki(S,g.mode,O),v.return=g,v):(v=n(v,S),v.return=g,v)}function d(g,v,S,O){var L=S.type;return L===J?R(g,v,S.props.children,O,S.key):v!==null&&(v.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Z&&ks(L)===v.type)?(v=n(v,S.props),bn(v,S),v.return=g,v):(v=ru(S.type,S.key,S.props,null,g.mode,O),bn(v,S),v.return=g,v)}function x(g,v,S,O){return v===null||v.tag!==4||v.stateNode.containerInfo!==S.containerInfo||v.stateNode.implementation!==S.implementation?(v=Ki(S,g.mode,O),v.return=g,v):(v=n(v,S.children||[]),v.return=g,v)}function R(g,v,S,O,L){return v===null||v.tag!==7?(v=jl(S,g.mode,O,L),v.return=g,v):(v=n(v,S),v.return=g,v)}function D(g,v,S){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=ki(""+v,g.mode,S),v.return=g,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case H:return S=ru(v.type,v.key,v.props,null,g.mode,S),bn(S,v),S.return=g,S;case k:return v=Ki(v,g.mode,S),v.return=g,v;case Z:var O=v._init;return v=O(v._payload),D(g,v,S)}if(st(v)||lt(v))return v=jl(v,g.mode,S,null),v.return=g,v;if(typeof v.then=="function")return D(g,zu(v),S);if(v.$$typeof===pt)return D(g,du(g,v),S);_u(g,v)}return null}function A(g,v,S,O){var L=v!==null?v.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return L!==null?null:r(g,v,""+S,O);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case H:return S.key===L?d(g,v,S,O):null;case k:return S.key===L?x(g,v,S,O):null;case Z:return L=S._init,S=L(S._payload),A(g,v,S,O)}if(st(S)||lt(S))return L!==null?null:R(g,v,S,O,null);if(typeof S.then=="function")return A(g,v,zu(S),O);if(S.$$typeof===pt)return A(g,v,du(g,S),O);_u(g,S)}return null}function T(g,v,S,O,L){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return g=g.get(S)||null,r(v,g,""+O,L);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case H:return g=g.get(O.key===null?S:O.key)||null,d(v,g,O,L);case k:return g=g.get(O.key===null?S:O.key)||null,x(v,g,O,L);case Z:var rt=O._init;return O=rt(O._payload),T(g,v,S,O,L)}if(st(O)||lt(O))return g=g.get(S)||null,R(v,g,O,L,null);if(typeof O.then=="function")return T(g,v,S,zu(O),L);if(O.$$typeof===pt)return T(g,v,S,du(v,O),L);_u(v,O)}return null}function F(g,v,S,O){for(var L=null,rt=null,K=v,$=v=0,Jt=null;K!==null&&$<S.length;$++){K.index>$?(Jt=K,K=null):Jt=K.sibling;var yt=A(g,K,S[$],O);if(yt===null){K===null&&(K=Jt);break}t&&K&&yt.alternate===null&&e(g,K),v=u(yt,v,$),rt===null?L=yt:rt.sibling=yt,rt=yt,K=Jt}if($===S.length)return l(g,K),gt&&Bl(g,$),L;if(K===null){for(;$<S.length;$++)K=D(g,S[$],O),K!==null&&(v=u(K,v,$),rt===null?L=K:rt.sibling=K,rt=K);return gt&&Bl(g,$),L}for(K=a(K);$<S.length;$++)Jt=T(K,g,$,S[$],O),Jt!==null&&(t&&Jt.alternate!==null&&K.delete(Jt.key===null?$:Jt.key),v=u(Jt,v,$),rt===null?L=Jt:rt.sibling=Jt,rt=Jt);return t&&K.forEach(function(Nl){return e(g,Nl)}),gt&&Bl(g,$),L}function W(g,v,S,O){if(S==null)throw Error(o(151));for(var L=null,rt=null,K=v,$=v=0,Jt=null,yt=S.next();K!==null&&!yt.done;$++,yt=S.next()){K.index>$?(Jt=K,K=null):Jt=K.sibling;var Nl=A(g,K,yt.value,O);if(Nl===null){K===null&&(K=Jt);break}t&&K&&Nl.alternate===null&&e(g,K),v=u(Nl,v,$),rt===null?L=Nl:rt.sibling=Nl,rt=Nl,K=Jt}if(yt.done)return l(g,K),gt&&Bl(g,$),L;if(K===null){for(;!yt.done;$++,yt=S.next())yt=D(g,yt.value,O),yt!==null&&(v=u(yt,v,$),rt===null?L=yt:rt.sibling=yt,rt=yt);return gt&&Bl(g,$),L}for(K=a(K);!yt.done;$++,yt=S.next())yt=T(K,g,$,yt.value,O),yt!==null&&(t&&yt.alternate!==null&&K.delete(yt.key===null?$:yt.key),v=u(yt,v,$),rt===null?L=yt:rt.sibling=yt,rt=yt);return t&&K.forEach(function(u0){return e(g,u0)}),gt&&Bl(g,$),L}function _t(g,v,S,O){if(typeof S=="object"&&S!==null&&S.type===J&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case H:t:{for(var L=S.key;v!==null;){if(v.key===L){if(L=S.type,L===J){if(v.tag===7){l(g,v.sibling),O=n(v,S.props.children),O.return=g,g=O;break t}}else if(v.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Z&&ks(L)===v.type){l(g,v.sibling),O=n(v,S.props),bn(O,S),O.return=g,g=O;break t}l(g,v);break}else e(g,v);v=v.sibling}S.type===J?(O=jl(S.props.children,g.mode,O,S.key),O.return=g,g=O):(O=ru(S.type,S.key,S.props,null,g.mode,O),bn(O,S),O.return=g,g=O)}return i(g);case k:t:{for(L=S.key;v!==null;){if(v.key===L)if(v.tag===4&&v.stateNode.containerInfo===S.containerInfo&&v.stateNode.implementation===S.implementation){l(g,v.sibling),O=n(v,S.children||[]),O.return=g,g=O;break t}else{l(g,v);break}else e(g,v);v=v.sibling}O=Ki(S,g.mode,O),O.return=g,g=O}return i(g);case Z:return L=S._init,S=L(S._payload),_t(g,v,S,O)}if(st(S))return F(g,v,S,O);if(lt(S)){if(L=lt(S),typeof L!="function")throw Error(o(150));return S=L.call(S),W(g,v,S,O)}if(typeof S.then=="function")return _t(g,v,zu(S),O);if(S.$$typeof===pt)return _t(g,v,du(g,S),O);_u(g,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,v!==null&&v.tag===6?(l(g,v.sibling),O=n(v,S),O.return=g,g=O):(l(g,v),O=ki(S,g.mode,O),O.return=g,g=O),i(g)):l(g,v)}return function(g,v,S,O){try{gn=0;var L=_t(g,v,S,O);return Aa=null,L}catch(K){if(K===rn||K===hu)throw K;var rt=fe(29,K,null,g.mode);return rt.lanes=O,rt.return=g,rt}finally{}}}var Ta=Ks(!0),Js=Ks(!1),Te=U(null),we=null;function ml(t){var e=t.alternate;B(Qt,Qt.current&1),B(Te,t),we===null&&(e===null||ba.current!==null||e.memoizedState!==null)&&(we=t)}function Ws(t){if(t.tag===22){if(B(Qt,Qt.current),B(Te,t),we===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(we=t)}}else hl()}function hl(){B(Qt,Qt.current),B(Te,Te.current)}function Ke(t){Y(Te),we===t&&(we=null),Y(Qt)}var Qt=U(0);function Mu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||vr(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function _c(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:M({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Mc={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=ve(),n=sl(a);n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(ye(e,t,a),sn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=ve(),n=sl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(ye(e,t,a),sn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=ve(),a=sl(l);a.tag=2,e!=null&&(a.callback=e),e=fl(t,a,l),e!==null&&(ye(e,t,l),sn(e,t,l))}};function $s(t,e,l,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):e.prototype&&e.prototype.isPureReactComponent?!Ia(l,a)||!Ia(n,u):!0}function Fs(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Mc.enqueueReplaceState(e,e.state,null)}function Vl(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=M({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Nu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ps(t){Nu(t)}function Is(t){console.error(t)}function tf(t){Nu(t)}function Ru(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function ef(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Nc(t,e,l){return l=sl(l),l.tag=3,l.payload={element:null},l.callback=function(){Ru(t,e)},l}function lf(t){return t=sl(t),t.tag=3,t}function af(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){ef(e,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){ef(e,l,a),typeof n!="function"&&(Sl===null?Sl=new Set([this]):Sl.add(this));var r=a.stack;this.componentDidCatch(a.value,{componentStack:r!==null?r:""})})}function uv(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&nn(e,l,n,!0),l=Te.current,l!==null){switch(l.tag){case 13:return we===null?Pc():l.alternate===null&&Bt===0&&(Bt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===ac?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),tr(t,a,n)),!1;case 22:return l.flags|=65536,a===ac?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),tr(t,a,n)),!1}throw Error(o(435,l.tag))}return tr(t,a,n),Pc(),!1}if(gt)return e=Te.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==$i&&(t=Error(o(422),{cause:a}),an(pe(t,l)))):(a!==$i&&(e=Error(o(423),{cause:a}),an(pe(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=pe(a,l),n=Nc(t.stateNode,a,n),ic(t,n),Bt!==4&&(Bt=2)),!1;var u=Error(o(520),{cause:a});if(u=pe(u,l),zn===null?zn=[u]:zn.push(u),Bt!==4&&(Bt=2),e===null)return!0;a=pe(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Nc(l.stateNode,a,t),ic(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Sl===null||!Sl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=lf(n),af(n,t,l,a),ic(l,n),!1}l=l.return}while(l!==null);return!1}var nf=Error(o(461)),kt=!1;function Wt(t,e,l,a){e.child=t===null?Js(e,null,l,a):Ta(e,t.child,l,a)}function uf(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var i={};for(var r in a)r!=="ref"&&(i[r]=a[r])}else i=a;return Xl(e),a=fc(t,e,l,i,u,n),r=dc(),t!==null&&!kt?(mc(t,e,n),Je(t,e,n)):(gt&&r&&Ji(e),e.flags|=1,Wt(t,e,a,n),e.child)}function cf(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!Zi(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,rf(t,e,u,a,n)):(t=ru(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Hc(t,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:Ia,l(i,a)&&t.ref===e.ref)return Je(t,e,n)}return e.flags|=1,t=Xe(u,a),t.ref=e.ref,t.return=e,e.child=t}function rf(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(Ia(u,a)&&t.ref===e.ref)if(kt=!1,e.pendingProps=a=u,Hc(t,n))(t.flags&131072)!==0&&(kt=!0);else return e.lanes=t.lanes,Je(t,e,n)}return Rc(t,e,l,a,n)}function of(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return sf(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&mu(e,u!==null?u.cachePool:null),u!==null?cs(e,u):rc(),Ws(e);else return e.lanes=e.childLanes=536870912,sf(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(mu(e,u.cachePool),cs(e,u),hl(),e.memoizedState=null):(t!==null&&mu(e,null),rc(),hl());return Wt(t,e,n,l),e.child}function sf(t,e,l,a){var n=lc();return n=n===null?null:{parent:Lt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&mu(e,null),rc(),Ws(e),t!==null&&nn(t,e,a,!0),null}function Ou(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(o(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function Rc(t,e,l,a,n){return Xl(e),l=fc(t,e,l,a,void 0,n),a=dc(),t!==null&&!kt?(mc(t,e,n),Je(t,e,n)):(gt&&a&&Ji(e),e.flags|=1,Wt(t,e,l,n),e.child)}function ff(t,e,l,a,n,u){return Xl(e),e.updateQueue=null,l=os(e,a,l,n),rs(t),a=dc(),t!==null&&!kt?(mc(t,e,u),Je(t,e,u)):(gt&&a&&Ji(e),e.flags|=1,Wt(t,e,l,u),e.child)}function df(t,e,l,a,n){if(Xl(e),e.stateNode===null){var u=ma,i=l.contextType;typeof i=="object"&&i!==null&&(u=It(i)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Mc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},nc(e),i=l.contextType,u.context=typeof i=="object"&&i!==null?It(i):ma,u.state=e.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(_c(e,l,i,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&Mc.enqueueReplaceState(u,u.state,null),dn(e,a,u,n),fn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var r=e.memoizedProps,d=Vl(l,r);u.props=d;var x=u.context,R=l.contextType;i=ma,typeof R=="object"&&R!==null&&(i=It(R));var D=l.getDerivedStateFromProps;R=typeof D=="function"||typeof u.getSnapshotBeforeUpdate=="function",r=e.pendingProps!==r,R||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r||x!==i)&&Fs(e,u,a,i),ol=!1;var A=e.memoizedState;u.state=A,dn(e,a,u,n),fn(),x=e.memoizedState,r||A!==x||ol?(typeof D=="function"&&(_c(e,l,D,a),x=e.memoizedState),(d=ol||$s(e,l,d,a,A,x,i))?(R||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=x),u.props=a,u.state=x,u.context=i,a=d):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,uc(t,e),i=e.memoizedProps,R=Vl(l,i),u.props=R,D=e.pendingProps,A=u.context,x=l.contextType,d=ma,typeof x=="object"&&x!==null&&(d=It(x)),r=l.getDerivedStateFromProps,(x=typeof r=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==D||A!==d)&&Fs(e,u,a,d),ol=!1,A=e.memoizedState,u.state=A,dn(e,a,u,n),fn();var T=e.memoizedState;i!==D||A!==T||ol||t!==null&&t.dependencies!==null&&fu(t.dependencies)?(typeof r=="function"&&(_c(e,l,r,a),T=e.memoizedState),(R=ol||$s(e,l,R,a,A,T,d)||t!==null&&t.dependencies!==null&&fu(t.dependencies))?(x||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,T,d),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,T,d)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=T),u.props=a,u.state=T,u.context=d,a=R):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Ou(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Ta(e,t.child,null,n),e.child=Ta(e,null,l,n)):Wt(t,e,l,n),e.memoizedState=u.state,t=e.child):t=Je(t,e,n),t}function mf(t,e,l,a){return ln(),e.flags|=256,Wt(t,e,l,a),e.child}var Oc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Dc(t){return{baseLanes:t,cachePool:Io()}}function wc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Ee),t}function hf(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(Qt.current&2)!==0),i&&(n=!0,e.flags&=-129),i=(e.flags&32)!==0,e.flags&=-33,t===null){if(gt){if(n?ml(e):hl(),gt){var r=Ht,d;if(d=r){t:{for(d=r,r=De;d.nodeType!==8;){if(!r){r=null;break t}if(d=Re(d.nextSibling),d===null){r=null;break t}}r=d}r!==null?(e.memoizedState={dehydrated:r,treeContext:Hl!==null?{id:Le,overflow:Qe}:null,retryLane:536870912,hydrationErrors:null},d=fe(18,null,null,0),d.stateNode=r,d.return=e,e.child=d,ee=e,Ht=null,d=!0):d=!1}d||Yl(e)}if(r=e.memoizedState,r!==null&&(r=r.dehydrated,r!==null))return vr(r)?e.lanes=32:e.lanes=536870912,null;Ke(e)}return r=a.children,a=a.fallback,n?(hl(),n=e.mode,r=Du({mode:"hidden",children:r},n),a=jl(a,n,l,null),r.return=e,a.return=e,r.sibling=a,e.child=r,n=e.child,n.memoizedState=Dc(l),n.childLanes=wc(t,i,l),e.memoizedState=Oc,a):(ml(e),Uc(e,r))}if(d=t.memoizedState,d!==null&&(r=d.dehydrated,r!==null)){if(u)e.flags&256?(ml(e),e.flags&=-257,e=Cc(t,e,l)):e.memoizedState!==null?(hl(),e.child=t.child,e.flags|=128,e=null):(hl(),n=a.fallback,r=e.mode,a=Du({mode:"visible",children:a.children},r),n=jl(n,r,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ta(e,t.child,null,l),a=e.child,a.memoizedState=Dc(l),a.childLanes=wc(t,i,l),e.memoizedState=Oc,e=n);else if(ml(e),vr(r)){if(i=r.nextSibling&&r.nextSibling.dataset,i)var x=i.dgst;i=x,a=Error(o(419)),a.stack="",a.digest=i,an({value:a,source:null,stack:null}),e=Cc(t,e,l)}else if(kt||nn(t,e,l,!1),i=(l&t.childLanes)!==0,kt||i){if(i=Dt,i!==null&&(a=l&-l,a=(a&42)!==0?1:yi(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==d.retryLane))throw d.retryLane=a,da(t,a),ye(i,t,a),nf;r.data==="$?"||Pc(),e=Cc(t,e,l)}else r.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=d.treeContext,Ht=Re(r.nextSibling),ee=e,gt=!0,ql=null,De=!1,t!==null&&(xe[Ae++]=Le,xe[Ae++]=Qe,xe[Ae++]=Hl,Le=t.id,Qe=t.overflow,Hl=e),e=Uc(e,a.children),e.flags|=4096);return e}return n?(hl(),n=a.fallback,r=e.mode,d=t.child,x=d.sibling,a=Xe(d,{mode:"hidden",children:a.children}),a.subtreeFlags=d.subtreeFlags&65011712,x!==null?n=Xe(x,n):(n=jl(n,r,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,r=t.child.memoizedState,r===null?r=Dc(l):(d=r.cachePool,d!==null?(x=Lt._currentValue,d=d.parent!==x?{parent:x,pool:x}:d):d=Io(),r={baseLanes:r.baseLanes|l,cachePool:d}),n.memoizedState=r,n.childLanes=wc(t,i,l),e.memoizedState=Oc,a):(ml(e),l=t.child,t=l.sibling,l=Xe(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(i=e.deletions,i===null?(e.deletions=[t],e.flags|=16):i.push(t)),e.child=l,e.memoizedState=null,l)}function Uc(t,e){return e=Du({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Du(t,e){return t=fe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Cc(t,e,l){return Ta(e,t.child,null,l),t=Uc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function vf(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Pi(t.return,e,l)}function jc(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function yf(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(Wt(t,e,a.children,l),a=Qt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&vf(t,l,e);else if(t.tag===19)vf(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(B(Qt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Mu(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),jc(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Mu(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}jc(e,!0,l,null,u);break;case"together":jc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Je(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),pl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(nn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,l=Xe(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Xe(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Hc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&fu(t)))}function iv(t,e,l){switch(e.tag){case 3:Rt(e,e.stateNode.containerInfo),rl(e,Lt,t.memoizedState.cache),ln();break;case 27:case 5:ll(e);break;case 4:Rt(e,e.stateNode.containerInfo);break;case 10:rl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(ml(e),e.flags|=128,null):(l&e.child.childLanes)!==0?hf(t,e,l):(ml(e),t=Je(t,e,l),t!==null?t.sibling:null);ml(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(nn(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return yf(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),B(Qt,Qt.current),a)break;return null;case 22:case 23:return e.lanes=0,of(t,e,l);case 24:rl(e,Lt,t.memoizedState.cache)}return Je(t,e,l)}function gf(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)kt=!0;else{if(!Hc(t,l)&&(e.flags&128)===0)return kt=!1,iv(t,e,l);kt=(t.flags&131072)!==0}else kt=!1,gt&&(e.flags&1048576)!==0&&ko(e,su,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Zi(a)?(t=Vl(a,t),e.tag=1,e=df(null,e,a,t,l)):(e.tag=0,e=Rc(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===et){e.tag=11,e=uf(null,e,a,t,l);break t}else if(n===Mt){e.tag=14,e=cf(null,e,a,t,l);break t}}throw e=Nt(a)||a,Error(o(306,e,""))}}return e;case 0:return Rc(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Vl(a,e.pendingProps),df(t,e,a,n,l);case 3:t:{if(Rt(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,uc(t,e),dn(e,a,null,l);var i=e.memoizedState;if(a=i.cache,rl(e,Lt,a),a!==u.cache&&Ii(e,[Lt],l,!0),fn(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=mf(t,e,a,l);break t}else if(a!==n){n=pe(Error(o(424)),e),an(n),e=mf(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ht=Re(t.firstChild),ee=e,gt=!0,ql=null,De=!0,l=Js(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(ln(),a===n){e=Je(t,e,l);break t}Wt(t,e,a,l)}e=e.child}return e;case 26:return Ou(t,e),t===null?(l=xd(e.type,null,e.pendingProps,null))?e.memoizedState=l:gt||(l=e.type,t=e.pendingProps,a=Zu(I.current).createElement(l),a[Pt]=e,a[le]=t,Ft(a,l,t),Zt(a),e.stateNode=a):e.memoizedState=xd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return ll(e),t===null&&gt&&(a=e.stateNode=bd(e.type,e.pendingProps,I.current),ee=e,De=!0,n=Ht,Tl(e.type)?(yr=n,Ht=Re(a.firstChild)):Ht=n),Wt(t,e,e.pendingProps.children,l),Ou(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&gt&&((n=a=Ht)&&(a=Cv(a,e.type,e.pendingProps,De),a!==null?(e.stateNode=a,ee=e,Ht=Re(a.firstChild),De=!1,n=!0):n=!1),n||Yl(e)),ll(e),n=e.type,u=e.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,dr(n,u)?a=null:i!==null&&dr(n,i)&&(e.flags|=32),e.memoizedState!==null&&(n=fc(t,e,Ph,null,null,l),Cn._currentValue=n),Ou(t,e),Wt(t,e,a,l),e.child;case 6:return t===null&&gt&&((t=l=Ht)&&(l=jv(l,e.pendingProps,De),l!==null?(e.stateNode=l,ee=e,Ht=null,t=!0):t=!1),t||Yl(e)),null;case 13:return hf(t,e,l);case 4:return Rt(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ta(e,null,a,l):Wt(t,e,a,l),e.child;case 11:return uf(t,e,e.type,e.pendingProps,l);case 7:return Wt(t,e,e.pendingProps,l),e.child;case 8:return Wt(t,e,e.pendingProps.children,l),e.child;case 12:return Wt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,rl(e,e.type,a.value),Wt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Xl(e),n=It(n),a=a(n),e.flags|=1,Wt(t,e,a,l),e.child;case 14:return cf(t,e,e.type,e.pendingProps,l);case 15:return rf(t,e,e.type,e.pendingProps,l);case 19:return yf(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Du(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Xe(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return of(t,e,l);case 24:return Xl(e),a=It(Lt),t===null?(n=lc(),n===null&&(n=Dt,u=tc(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},nc(e),rl(e,Lt,n)):((t.lanes&l)!==0&&(uc(t,e),dn(e,null,null,l),fn()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),rl(e,Lt,a)):(a=u.cache,rl(e,Lt,a),a!==n.cache&&Ii(e,[Lt],l,!0))),Wt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function We(t){t.flags|=4}function bf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!_d(e)){if(e=Te.current,e!==null&&((ht&4194048)===ht?we!==null:(ht&62914560)!==ht&&(ht&536870912)===0||e!==we))throw on=ac,ts;t.flags|=8192}}function wu(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Jr():536870912,t.lanes|=e,Ma|=e)}function pn(t,e){if(!gt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Ct(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function cv(t,e,l){var a=e.pendingProps;switch(Wi(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ct(e),null;case 1:return Ct(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Ze(Lt),ce(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(en(e)?We(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Wo())),Ct(e),null;case 26:return l=e.memoizedState,t===null?(We(e),l!==null?(Ct(e),bf(e,l)):(Ct(e),e.flags&=-16777217)):l?l!==t.memoizedState?(We(e),Ct(e),bf(e,l)):(Ct(e),e.flags&=-16777217):(t.memoizedProps!==a&&We(e),Ct(e),e.flags&=-16777217),null;case 27:al(e),l=I.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Ct(e),null}t=G.current,en(e)?Ko(e):(t=bd(n,a,l),e.stateNode=t,We(e))}return Ct(e),null;case 5:if(al(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Ct(e),null}if(t=G.current,en(e))Ko(e);else{switch(n=Zu(I.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[Pt]=e,t[le]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(Ft(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&We(e)}}return Ct(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&We(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=I.current,en(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=ee,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[Pt]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||fd(t.nodeValue,l)),t||Yl(e)}else t=Zu(t).createTextNode(a),t[Pt]=e,e.stateNode=t}return Ct(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=en(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(o(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(o(317));n[Pt]=e}else ln(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ct(e),n=!1}else n=Wo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ke(e),e):(Ke(e),null)}if(Ke(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),wu(e,e.updateQueue),Ct(e),null;case 4:return ce(),t===null&&cr(e.stateNode.containerInfo),Ct(e),null;case 10:return Ze(e.type),Ct(e),null;case 19:if(Y(Qt),n=e.memoizedState,n===null)return Ct(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)pn(n,!1);else{if(Bt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Mu(t),u!==null){for(e.flags|=128,pn(n,!1),t=u.updateQueue,e.updateQueue=t,wu(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Zo(l,t),l=l.sibling;return B(Qt,Qt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Oe()>ju&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Mu(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,wu(e,t),pn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!gt)return Ct(e),null}else 2*Oe()-n.renderingStartTime>ju&&l!==536870912&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Oe(),e.sibling=null,t=Qt.current,B(Qt,a?t&1|2:t&1),e):(Ct(e),null);case 22:case 23:return Ke(e),oc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Ct(e),e.subtreeFlags&6&&(e.flags|=8192)):Ct(e),l=e.updateQueue,l!==null&&wu(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Y(Ll),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ze(Lt),Ct(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function rv(t,e){switch(Wi(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ze(Lt),ce(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return al(e),null;case 13:if(Ke(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));ln()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(Qt),null;case 4:return ce(),null;case 10:return Ze(e.type),null;case 22:case 23:return Ke(e),oc(),t!==null&&Y(Ll),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ze(Lt),null;case 25:return null;default:return null}}function pf(t,e){switch(Wi(e),e.tag){case 3:Ze(Lt),ce();break;case 26:case 27:case 5:al(e);break;case 4:ce();break;case 13:Ke(e);break;case 19:Y(Qt);break;case 10:Ze(e.type);break;case 22:case 23:Ke(e),oc(),t!==null&&Y(Ll);break;case 24:Ze(Lt)}}function Sn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(r){Ot(e,e.return,r)}}function vl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,r=i.destroy;if(r!==void 0){i.destroy=void 0,n=e;var d=l,x=r;try{x()}catch(R){Ot(n,d,R)}}}a=a.next}while(a!==u)}}catch(R){Ot(e,e.return,R)}}function Sf(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{is(e,l)}catch(a){Ot(t,t.return,a)}}}function xf(t,e,l){l.props=Vl(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Ot(t,e,a)}}function xn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Ot(t,e,n)}}function Ue(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Ot(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Ot(t,e,n)}else l.current=null}function Af(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Ot(t,t.return,n)}}function Bc(t,e,l){try{var a=t.stateNode;Rv(a,t.type,l,e),a[le]=e}catch(n){Ot(t,t.return,n)}}function Tf(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Tl(t.type)||t.tag===4}function qc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Tf(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Tl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Yc(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Vu));else if(a!==4&&(a===27&&Tl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Yc(t,e,l),t=t.sibling;t!==null;)Yc(t,e,l),t=t.sibling}function Uu(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&Tl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Uu(t,e,l),t=t.sibling;t!==null;)Uu(t,e,l),t=t.sibling}function Ef(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Ft(e,a,l),e[Pt]=t,e[le]=l}catch(u){Ot(t,t.return,u)}}var $e=!1,Yt=!1,Gc=!1,zf=typeof WeakSet=="function"?WeakSet:Set,Kt=null;function ov(t,e){if(t=t.containerInfo,sr=Fu,t=jo(t),qi(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var i=0,r=-1,d=-1,x=0,R=0,D=t,A=null;e:for(;;){for(var T;D!==l||n!==0&&D.nodeType!==3||(r=i+n),D!==u||a!==0&&D.nodeType!==3||(d=i+a),D.nodeType===3&&(i+=D.nodeValue.length),(T=D.firstChild)!==null;)A=D,D=T;for(;;){if(D===t)break e;if(A===l&&++x===n&&(r=i),A===u&&++R===a&&(d=i),(T=D.nextSibling)!==null)break;D=A,A=D.parentNode}D=T}l=r===-1||d===-1?null:{start:r,end:d}}else l=null}l=l||{start:0,end:0}}else l=null;for(fr={focusedElem:t,selectionRange:l},Fu=!1,Kt=e;Kt!==null;)if(e=Kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Kt=t;else for(;Kt!==null;){switch(e=Kt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var F=Vl(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(F,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(W){Ot(l,l.return,W)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)hr(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":hr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,Kt=t;break}Kt=e.return}}function _f(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:yl(t,l),a&4&&Sn(5,l);break;case 1:if(yl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(i){Ot(l,l.return,i)}else{var n=Vl(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(i){Ot(l,l.return,i)}}a&64&&Sf(l),a&512&&xn(l,l.return);break;case 3:if(yl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{is(t,e)}catch(i){Ot(l,l.return,i)}}break;case 27:e===null&&a&4&&Ef(l);case 26:case 5:yl(t,l),e===null&&a&4&&Af(l),a&512&&xn(l,l.return);break;case 12:yl(t,l);break;case 13:yl(t,l),a&4&&Rf(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=bv.bind(null,l),Hv(t,l))));break;case 22:if(a=l.memoizedState!==null||$e,!a){e=e!==null&&e.memoizedState!==null||Yt,n=$e;var u=Yt;$e=a,(Yt=e)&&!u?gl(t,l,(l.subtreeFlags&8772)!==0):yl(t,l),$e=n,Yt=u}break;case 30:break;default:yl(t,l)}}function Mf(t){var e=t.alternate;e!==null&&(t.alternate=null,Mf(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&pi(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var wt=null,ue=!1;function Fe(t,e,l){for(l=l.child;l!==null;)Nf(t,e,l),l=l.sibling}function Nf(t,e,l){if(re&&typeof re.onCommitFiberUnmount=="function")try{re.onCommitFiberUnmount(Xa,l)}catch{}switch(l.tag){case 26:Yt||Ue(l,e),Fe(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Yt||Ue(l,e);var a=wt,n=ue;Tl(l.type)&&(wt=l.stateNode,ue=!1),Fe(t,e,l),On(l.stateNode),wt=a,ue=n;break;case 5:Yt||Ue(l,e);case 6:if(a=wt,n=ue,wt=null,Fe(t,e,l),wt=a,ue=n,wt!==null)if(ue)try{(wt.nodeType===9?wt.body:wt.nodeName==="HTML"?wt.ownerDocument.body:wt).removeChild(l.stateNode)}catch(u){Ot(l,e,u)}else try{wt.removeChild(l.stateNode)}catch(u){Ot(l,e,u)}break;case 18:wt!==null&&(ue?(t=wt,yd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),qn(t)):yd(wt,l.stateNode));break;case 4:a=wt,n=ue,wt=l.stateNode.containerInfo,ue=!0,Fe(t,e,l),wt=a,ue=n;break;case 0:case 11:case 14:case 15:Yt||vl(2,l,e),Yt||vl(4,l,e),Fe(t,e,l);break;case 1:Yt||(Ue(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&xf(l,e,a)),Fe(t,e,l);break;case 21:Fe(t,e,l);break;case 22:Yt=(a=Yt)||l.memoizedState!==null,Fe(t,e,l),Yt=a;break;default:Fe(t,e,l)}}function Rf(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{qn(t)}catch(l){Ot(e,e.return,l)}}function sv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new zf),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new zf),e;default:throw Error(o(435,t.tag))}}function Xc(t,e){var l=sv(t);e.forEach(function(a){var n=pv.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function de(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,i=e,r=i;t:for(;r!==null;){switch(r.tag){case 27:if(Tl(r.type)){wt=r.stateNode,ue=!1;break t}break;case 5:wt=r.stateNode,ue=!1;break t;case 3:case 4:wt=r.stateNode.containerInfo,ue=!0;break t}r=r.return}if(wt===null)throw Error(o(160));Nf(u,i,n),wt=null,ue=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Of(e,t),e=e.sibling}var Ne=null;function Of(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:de(e,t),me(t),a&4&&(vl(3,t,t.return),Sn(3,t),vl(5,t,t.return));break;case 1:de(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),a&64&&$e&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Ne;if(de(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Va]||u[Pt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Ft(u,a,l),u[Pt]=t,Zt(u),a=u;break t;case"link":var i=Ed("link","href",n).get(a+(l.href||""));if(i){for(var r=0;r<i.length;r++)if(u=i[r],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(r,1);break e}}u=n.createElement(a),Ft(u,a,l),n.head.appendChild(u);break;case"meta":if(i=Ed("meta","content",n).get(a+(l.content||""))){for(r=0;r<i.length;r++)if(u=i[r],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(r,1);break e}}u=n.createElement(a),Ft(u,a,l),n.head.appendChild(u);break;default:throw Error(o(468,a))}u[Pt]=t,Zt(u),a=u}t.stateNode=a}else zd(n,t.type,t.stateNode);else t.stateNode=Td(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?zd(n,t.type,t.stateNode):Td(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Bc(t,t.memoizedProps,l.memoizedProps)}break;case 27:de(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),l!==null&&a&4&&Bc(t,t.memoizedProps,l.memoizedProps);break;case 5:if(de(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),t.flags&32){n=t.stateNode;try{ua(n,"")}catch(T){Ot(t,t.return,T)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Bc(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Gc=!0);break;case 6:if(de(e,t),me(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(T){Ot(t,t.return,T)}}break;case 3:if(Ju=null,n=Ne,Ne=ku(e.containerInfo),de(e,t),Ne=n,me(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{qn(e.containerInfo)}catch(T){Ot(t,t.return,T)}Gc&&(Gc=!1,Df(t));break;case 4:a=Ne,Ne=ku(t.stateNode.containerInfo),de(e,t),me(t),Ne=a;break;case 12:de(e,t),me(t);break;case 13:de(e,t),me(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Kc=Oe()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Xc(t,a)));break;case 22:n=t.memoizedState!==null;var d=l!==null&&l.memoizedState!==null,x=$e,R=Yt;if($e=x||n,Yt=R||d,de(e,t),Yt=R,$e=x,me(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||d||$e||Yt||Zl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){d=l=e;try{if(u=d.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{r=d.stateNode;var D=d.memoizedProps.style,A=D!=null&&D.hasOwnProperty("display")?D.display:null;r.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(T){Ot(d,d.return,T)}}}else if(e.tag===6){if(l===null){d=e;try{d.stateNode.nodeValue=n?"":d.memoizedProps}catch(T){Ot(d,d.return,T)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Xc(t,l))));break;case 19:de(e,t),me(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Xc(t,a)));break;case 30:break;case 21:break;default:de(e,t),me(t)}}function me(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Tf(a)){l=a;break}a=a.return}if(l==null)throw Error(o(160));switch(l.tag){case 27:var n=l.stateNode,u=qc(t);Uu(t,u,n);break;case 5:var i=l.stateNode;l.flags&32&&(ua(i,""),l.flags&=-33);var r=qc(t);Uu(t,r,i);break;case 3:case 4:var d=l.stateNode.containerInfo,x=qc(t);Yc(t,x,d);break;default:throw Error(o(161))}}catch(R){Ot(t,t.return,R)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Df(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Df(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function yl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)_f(t,e.alternate,e),e=e.sibling}function Zl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:vl(4,e,e.return),Zl(e);break;case 1:Ue(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&xf(e,e.return,l),Zl(e);break;case 27:On(e.stateNode);case 26:case 5:Ue(e,e.return),Zl(e);break;case 22:e.memoizedState===null&&Zl(e);break;case 30:Zl(e);break;default:Zl(e)}t=t.sibling}}function gl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,i=u.flags;switch(u.tag){case 0:case 11:case 15:gl(n,u,l),Sn(4,u);break;case 1:if(gl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(x){Ot(a,a.return,x)}if(a=u,n=a.updateQueue,n!==null){var r=a.stateNode;try{var d=n.shared.hiddenCallbacks;if(d!==null)for(n.shared.hiddenCallbacks=null,n=0;n<d.length;n++)us(d[n],r)}catch(x){Ot(a,a.return,x)}}l&&i&64&&Sf(u),xn(u,u.return);break;case 27:Ef(u);case 26:case 5:gl(n,u,l),l&&a===null&&i&4&&Af(u),xn(u,u.return);break;case 12:gl(n,u,l);break;case 13:gl(n,u,l),l&&i&4&&Rf(n,u);break;case 22:u.memoizedState===null&&gl(n,u,l),xn(u,u.return);break;case 30:break;default:gl(n,u,l)}e=e.sibling}}function Lc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&un(l))}function Qc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t))}function Ce(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)wf(t,e,l,a),e=e.sibling}function wf(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:Ce(t,e,l,a),n&2048&&Sn(9,e);break;case 1:Ce(t,e,l,a);break;case 3:Ce(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t)));break;case 12:if(n&2048){Ce(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,i=u.id,r=u.onPostCommit;typeof r=="function"&&r(i,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(d){Ot(e,e.return,d)}}else Ce(t,e,l,a);break;case 13:Ce(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,i=e.alternate,e.memoizedState!==null?u._visibility&2?Ce(t,e,l,a):An(t,e):u._visibility&2?Ce(t,e,l,a):(u._visibility|=2,Ea(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Lc(i,e);break;case 24:Ce(t,e,l,a),n&2048&&Qc(e.alternate,e);break;default:Ce(t,e,l,a)}}function Ea(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,i=e,r=l,d=a,x=i.flags;switch(i.tag){case 0:case 11:case 15:Ea(u,i,r,d,n),Sn(8,i);break;case 23:break;case 22:var R=i.stateNode;i.memoizedState!==null?R._visibility&2?Ea(u,i,r,d,n):An(u,i):(R._visibility|=2,Ea(u,i,r,d,n)),n&&x&2048&&Lc(i.alternate,i);break;case 24:Ea(u,i,r,d,n),n&&x&2048&&Qc(i.alternate,i);break;default:Ea(u,i,r,d,n)}e=e.sibling}}function An(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:An(l,a),n&2048&&Lc(a.alternate,a);break;case 24:An(l,a),n&2048&&Qc(a.alternate,a);break;default:An(l,a)}e=e.sibling}}var Tn=8192;function za(t){if(t.subtreeFlags&Tn)for(t=t.child;t!==null;)Uf(t),t=t.sibling}function Uf(t){switch(t.tag){case 26:za(t),t.flags&Tn&&t.memoizedState!==null&&Wv(Ne,t.memoizedState,t.memoizedProps);break;case 5:za(t);break;case 3:case 4:var e=Ne;Ne=ku(t.stateNode.containerInfo),za(t),Ne=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Tn,Tn=16777216,za(t),Tn=e):za(t));break;default:za(t)}}function Cf(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function En(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Kt=a,Hf(a,t)}Cf(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)jf(t),t=t.sibling}function jf(t){switch(t.tag){case 0:case 11:case 15:En(t),t.flags&2048&&vl(9,t,t.return);break;case 3:En(t);break;case 12:En(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Cu(t)):En(t);break;default:En(t)}}function Cu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Kt=a,Hf(a,t)}Cf(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:vl(8,e,e.return),Cu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Cu(e));break;default:Cu(e)}t=t.sibling}}function Hf(t,e){for(;Kt!==null;){var l=Kt;switch(l.tag){case 0:case 11:case 15:vl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:un(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Kt=a;else t:for(l=t;Kt!==null;){a=Kt;var n=a.sibling,u=a.return;if(Mf(a),a===l){Kt=null;break t}if(n!==null){n.return=u,Kt=n;break t}Kt=u}}}var fv={getCacheForType:function(t){var e=It(Lt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},dv=typeof WeakMap=="function"?WeakMap:Map,xt=0,Dt=null,dt=null,ht=0,At=0,he=null,bl=!1,_a=!1,Vc=!1,Pe=0,Bt=0,pl=0,kl=0,Zc=0,Ee=0,Ma=0,zn=null,ie=null,kc=!1,Kc=0,ju=1/0,Hu=null,Sl=null,$t=0,xl=null,Na=null,Ra=0,Jc=0,Wc=null,Bf=null,_n=0,$c=null;function ve(){if((xt&2)!==0&&ht!==0)return ht&-ht;if(z.T!==null){var t=ya;return t!==0?t:ar()}return Fr()}function qf(){Ee===0&&(Ee=(ht&536870912)===0||gt?Kr():536870912);var t=Te.current;return t!==null&&(t.flags|=32),Ee}function ye(t,e,l){(t===Dt&&(At===2||At===9)||t.cancelPendingCommit!==null)&&(Oa(t,0),Al(t,ht,Ee,!1)),Qa(t,l),((xt&2)===0||t!==Dt)&&(t===Dt&&((xt&2)===0&&(kl|=l),Bt===4&&Al(t,ht,Ee,!1)),je(t))}function Yf(t,e,l){if((xt&6)!==0)throw Error(o(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||La(t,e),n=a?vv(t,e):Ic(t,e,!0),u=a;do{if(n===0){_a&&!a&&Al(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!mv(l)){n=Ic(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){e=i;t:{var r=t;n=zn;var d=r.current.memoizedState.isDehydrated;if(d&&(Oa(r,i).flags|=256),i=Ic(r,i,!1),i!==2){if(Vc&&!d){r.errorRecoveryDisabledLanes|=u,kl|=u,n=4;break t}u=ie,ie=n,u!==null&&(ie===null?ie=u:ie.push.apply(ie,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){Oa(t,0),Al(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Al(a,e,Ee,!bl);break t;case 2:ie=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(n=Kc+300-Oe(),10<n)){if(Al(a,e,Ee,!bl),Kn(a,0,!0)!==0)break t;a.timeoutHandle=hd(Gf.bind(null,a,l,ie,Hu,kc,e,Ee,kl,Ma,bl,u,2,-0,0),n);break t}Gf(a,l,ie,Hu,kc,e,Ee,kl,Ma,bl,u,0,-0,0)}}break}while(!0);je(t)}function Gf(t,e,l,a,n,u,i,r,d,x,R,D,A,T){if(t.timeoutHandle=-1,D=e.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(Un={stylesheets:null,count:0,unsuspend:Jv},Uf(e),D=$v(),D!==null)){t.cancelPendingCommit=D(Kf.bind(null,t,e,u,l,a,n,i,r,d,R,1,A,T)),Al(t,u,i,!x);return}Kf(t,e,u,l,a,n,i,r,d)}function mv(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!se(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Al(t,e,l,a){e&=~Zc,e&=~kl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-oe(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&Wr(t,l,e)}function Bu(){return(xt&6)===0?(Mn(0),!1):!0}function Fc(){if(dt!==null){if(At===0)var t=dt.return;else t=dt,Ve=Gl=null,hc(t),Aa=null,gn=0,t=dt;for(;t!==null;)pf(t.alternate,t),t=t.return;dt=null}}function Oa(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Dv(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Fc(),Dt=t,dt=l=Xe(t.current,null),ht=e,At=0,he=null,bl=!1,_a=La(t,e),Vc=!1,Ma=Ee=Zc=kl=pl=Bt=0,ie=zn=null,kc=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-oe(a),u=1<<n;e|=t[n],a&=~u}return Pe=e,uu(),l}function Xf(t,e){it=null,z.H=Eu,e===rn||e===hu?(e=as(),At=3):e===ts?(e=as(),At=4):At=e===nf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,he=e,dt===null&&(Bt=1,Ru(t,pe(e,t.current)))}function Lf(){var t=z.H;return z.H=Eu,t===null?Eu:t}function Qf(){var t=z.A;return z.A=fv,t}function Pc(){Bt=4,bl||(ht&4194048)!==ht&&Te.current!==null||(_a=!0),(pl&134217727)===0&&(kl&134217727)===0||Dt===null||Al(Dt,ht,Ee,!1)}function Ic(t,e,l){var a=xt;xt|=2;var n=Lf(),u=Qf();(Dt!==t||ht!==e)&&(Hu=null,Oa(t,e)),e=!1;var i=Bt;t:do try{if(At!==0&&dt!==null){var r=dt,d=he;switch(At){case 8:Fc(),i=6;break t;case 3:case 2:case 9:case 6:Te.current===null&&(e=!0);var x=At;if(At=0,he=null,Da(t,r,d,x),l&&_a){i=0;break t}break;default:x=At,At=0,he=null,Da(t,r,d,x)}}hv(),i=Bt;break}catch(R){Xf(t,R)}while(!0);return e&&t.shellSuspendCounter++,Ve=Gl=null,xt=a,z.H=n,z.A=u,dt===null&&(Dt=null,ht=0,uu()),i}function hv(){for(;dt!==null;)Vf(dt)}function vv(t,e){var l=xt;xt|=2;var a=Lf(),n=Qf();Dt!==t||ht!==e?(Hu=null,ju=Oe()+500,Oa(t,e)):_a=La(t,e);t:do try{if(At!==0&&dt!==null){e=dt;var u=he;e:switch(At){case 1:At=0,he=null,Da(t,e,u,1);break;case 2:case 9:if(es(u)){At=0,he=null,Zf(e);break}e=function(){At!==2&&At!==9||Dt!==t||(At=7),je(t)},u.then(e,e);break t;case 3:At=7;break t;case 4:At=5;break t;case 7:es(u)?(At=0,he=null,Zf(e)):(At=0,he=null,Da(t,e,u,7));break;case 5:var i=null;switch(dt.tag){case 26:i=dt.memoizedState;case 5:case 27:var r=dt;if(!i||_d(i)){At=0,he=null;var d=r.sibling;if(d!==null)dt=d;else{var x=r.return;x!==null?(dt=x,qu(x)):dt=null}break e}}At=0,he=null,Da(t,e,u,5);break;case 6:At=0,he=null,Da(t,e,u,6);break;case 8:Fc(),Bt=6;break t;default:throw Error(o(462))}}yv();break}catch(R){Xf(t,R)}while(!0);return Ve=Gl=null,z.H=a,z.A=n,xt=l,dt!==null?0:(Dt=null,ht=0,uu(),Bt)}function yv(){for(;dt!==null&&!qm();)Vf(dt)}function Vf(t){var e=gf(t.alternate,t,Pe);t.memoizedProps=t.pendingProps,e===null?qu(t):dt=e}function Zf(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=ff(l,e,e.pendingProps,e.type,void 0,ht);break;case 11:e=ff(l,e,e.pendingProps,e.type.render,e.ref,ht);break;case 5:hc(e);default:pf(l,e),e=dt=Zo(e,Pe),e=gf(l,e,Pe)}t.memoizedProps=t.pendingProps,e===null?qu(t):dt=e}function Da(t,e,l,a){Ve=Gl=null,hc(e),Aa=null,gn=0;var n=e.return;try{if(uv(t,n,e,l,ht)){Bt=1,Ru(t,pe(l,t.current)),dt=null;return}}catch(u){if(n!==null)throw dt=n,u;Bt=1,Ru(t,pe(l,t.current)),dt=null;return}e.flags&32768?(gt||a===1?t=!0:_a||(ht&536870912)!==0?t=!1:(bl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Te.current,a!==null&&a.tag===13&&(a.flags|=16384))),kf(e,t)):qu(e)}function qu(t){var e=t;do{if((e.flags&32768)!==0){kf(e,bl);return}t=e.return;var l=cv(e.alternate,e,Pe);if(l!==null){dt=l;return}if(e=e.sibling,e!==null){dt=e;return}dt=e=t}while(e!==null);Bt===0&&(Bt=5)}function kf(t,e){do{var l=rv(t.alternate,t);if(l!==null){l.flags&=32767,dt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){dt=t;return}dt=t=l}while(t!==null);Bt=6,dt=null}function Kf(t,e,l,a,n,u,i,r,d){t.cancelPendingCommit=null;do Yu();while($t!==0);if((xt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(u=e.lanes|e.childLanes,u|=Qi,Jm(t,l,u,i,r,d),t===Dt&&(dt=Dt=null,ht=0),Na=e,xl=t,Ra=l,Jc=u,Wc=n,Bf=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Sv(Vn,function(){return Pf(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=z.T,z.T=null,n=q.p,q.p=2,i=xt,xt|=4;try{ov(t,e,l)}finally{xt=i,q.p=n,z.T=a}}$t=1,Jf(),Wf(),$f()}}function Jf(){if($t===1){$t=0;var t=xl,e=Na,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=z.T,z.T=null;var a=q.p;q.p=2;var n=xt;xt|=4;try{Of(e,t);var u=fr,i=jo(t.containerInfo),r=u.focusedElem,d=u.selectionRange;if(i!==r&&r&&r.ownerDocument&&Co(r.ownerDocument.documentElement,r)){if(d!==null&&qi(r)){var x=d.start,R=d.end;if(R===void 0&&(R=x),"selectionStart"in r)r.selectionStart=x,r.selectionEnd=Math.min(R,r.value.length);else{var D=r.ownerDocument||document,A=D&&D.defaultView||window;if(A.getSelection){var T=A.getSelection(),F=r.textContent.length,W=Math.min(d.start,F),_t=d.end===void 0?W:Math.min(d.end,F);!T.extend&&W>_t&&(i=_t,_t=W,W=i);var g=Uo(r,W),v=Uo(r,_t);if(g&&v&&(T.rangeCount!==1||T.anchorNode!==g.node||T.anchorOffset!==g.offset||T.focusNode!==v.node||T.focusOffset!==v.offset)){var S=D.createRange();S.setStart(g.node,g.offset),T.removeAllRanges(),W>_t?(T.addRange(S),T.extend(v.node,v.offset)):(S.setEnd(v.node,v.offset),T.addRange(S))}}}}for(D=[],T=r;T=T.parentNode;)T.nodeType===1&&D.push({element:T,left:T.scrollLeft,top:T.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<D.length;r++){var O=D[r];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}Fu=!!sr,fr=sr=null}finally{xt=n,q.p=a,z.T=l}}t.current=e,$t=2}}function Wf(){if($t===2){$t=0;var t=xl,e=Na,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=z.T,z.T=null;var a=q.p;q.p=2;var n=xt;xt|=4;try{_f(t,e.alternate,e)}finally{xt=n,q.p=a,z.T=l}}$t=3}}function $f(){if($t===4||$t===3){$t=0,Ym();var t=xl,e=Na,l=Ra,a=Bf;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?$t=5:($t=0,Na=xl=null,Ff(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Sl=null),gi(l),e=e.stateNode,re&&typeof re.onCommitFiberRoot=="function")try{re.onCommitFiberRoot(Xa,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=z.T,n=q.p,q.p=2,z.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var r=a[i];u(r.value,{componentStack:r.stack})}}finally{z.T=e,q.p=n}}(Ra&3)!==0&&Yu(),je(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===$c?_n++:(_n=0,$c=t):_n=0,Mn(0)}}function Ff(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,un(e)))}function Yu(t){return Jf(),Wf(),$f(),Pf()}function Pf(){if($t!==5)return!1;var t=xl,e=Jc;Jc=0;var l=gi(Ra),a=z.T,n=q.p;try{q.p=32>l?32:l,z.T=null,l=Wc,Wc=null;var u=xl,i=Ra;if($t=0,Na=xl=null,Ra=0,(xt&6)!==0)throw Error(o(331));var r=xt;if(xt|=4,jf(u.current),wf(u,u.current,i,l),xt=r,Mn(0,!1),re&&typeof re.onPostCommitFiberRoot=="function")try{re.onPostCommitFiberRoot(Xa,u)}catch{}return!0}finally{q.p=n,z.T=a,Ff(t,e)}}function If(t,e,l){e=pe(l,e),e=Nc(t.stateNode,e,2),t=fl(t,e,2),t!==null&&(Qa(t,2),je(t))}function Ot(t,e,l){if(t.tag===3)If(t,t,l);else for(;e!==null;){if(e.tag===3){If(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Sl===null||!Sl.has(a))){t=pe(l,t),l=lf(2),a=fl(e,l,2),a!==null&&(af(l,a,e,t),Qa(a,2),je(a));break}}e=e.return}}function tr(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new dv;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Vc=!0,n.add(l),t=gv.bind(null,t,e,l),e.then(t,t))}function gv(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Dt===t&&(ht&l)===l&&(Bt===4||Bt===3&&(ht&62914560)===ht&&300>Oe()-Kc?(xt&2)===0&&Oa(t,0):Zc|=l,Ma===ht&&(Ma=0)),je(t)}function td(t,e){e===0&&(e=Jr()),t=da(t,e),t!==null&&(Qa(t,e),je(t))}function bv(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),td(t,l)}function pv(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),td(t,l)}function Sv(t,e){return mi(t,e)}var Gu=null,wa=null,er=!1,Xu=!1,lr=!1,Kl=0;function je(t){t!==wa&&t.next===null&&(wa===null?Gu=wa=t:wa=wa.next=t),Xu=!0,er||(er=!0,Av())}function Mn(t,e){if(!lr&&Xu){lr=!0;do for(var l=!1,a=Gu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,r=a.pingedLanes;u=(1<<31-oe(42|t)+1)-1,u&=n&~(i&~r),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,nd(a,u))}else u=ht,u=Kn(a,a===Dt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||La(a,u)||(l=!0,nd(a,u));a=a.next}while(l);lr=!1}}function xv(){ed()}function ed(){Xu=er=!1;var t=0;Kl!==0&&(Ov()&&(t=Kl),Kl=0);for(var e=Oe(),l=null,a=Gu;a!==null;){var n=a.next,u=ld(a,e);u===0?(a.next=null,l===null?Gu=n:l.next=n,n===null&&(wa=l)):(l=a,(t!==0||(u&3)!==0)&&(Xu=!0)),a=n}Mn(t)}function ld(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-oe(u),r=1<<i,d=n[i];d===-1?((r&l)===0||(r&a)!==0)&&(n[i]=Km(r,e)):d<=e&&(t.expiredLanes|=r),u&=~r}if(e=Dt,l=ht,l=Kn(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(At===2||At===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&hi(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||La(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&hi(a),gi(l)){case 2:case 8:l=Zr;break;case 32:l=Vn;break;case 268435456:l=kr;break;default:l=Vn}return a=ad.bind(null,t),l=mi(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&hi(a),t.callbackPriority=2,t.callbackNode=null,2}function ad(t,e){if($t!==0&&$t!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Yu()&&t.callbackNode!==l)return null;var a=ht;return a=Kn(t,t===Dt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Yf(t,a,e),ld(t,Oe()),t.callbackNode!=null&&t.callbackNode===l?ad.bind(null,t):null)}function nd(t,e){if(Yu())return null;Yf(t,e,!0)}function Av(){wv(function(){(xt&6)!==0?mi(Vr,xv):ed()})}function ar(){return Kl===0&&(Kl=Kr()),Kl}function ud(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Pn(""+t)}function id(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Tv(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=ud((n[le]||null).action),i=a.submitter;i&&(e=(e=i[le]||null)?ud(e.formAction):i.getAttribute("formAction"),e!==null&&(u=e,i=null));var r=new lu("action","action",null,a,n);t.push({event:r,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Kl!==0){var d=i?id(n,i):new FormData(n);Tc(l,{pending:!0,data:d,method:n.method,action:u},null,d)}}else typeof u=="function"&&(r.preventDefault(),d=i?id(n,i):new FormData(n),Tc(l,{pending:!0,data:d,method:n.method,action:u},u,d))},currentTarget:n}]})}}for(var nr=0;nr<Li.length;nr++){var ur=Li[nr],Ev=ur.toLowerCase(),zv=ur[0].toUpperCase()+ur.slice(1);Me(Ev,"on"+zv)}Me(qo,"onAnimationEnd"),Me(Yo,"onAnimationIteration"),Me(Go,"onAnimationStart"),Me("dblclick","onDoubleClick"),Me("focusin","onFocus"),Me("focusout","onBlur"),Me(Lh,"onTransitionRun"),Me(Qh,"onTransitionStart"),Me(Vh,"onTransitionCancel"),Me(Xo,"onTransitionEnd"),la("onMouseEnter",["mouseout","mouseover"]),la("onMouseLeave",["mouseout","mouseover"]),la("onPointerEnter",["pointerout","pointerover"]),la("onPointerLeave",["pointerout","pointerover"]),Dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Nn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_v=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Nn));function cd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var i=a.length-1;0<=i;i--){var r=a[i],d=r.instance,x=r.currentTarget;if(r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=x;try{u(n)}catch(R){Nu(R)}n.currentTarget=null,u=d}else for(i=0;i<a.length;i++){if(r=a[i],d=r.instance,x=r.currentTarget,r=r.listener,d!==u&&n.isPropagationStopped())break t;u=r,n.currentTarget=x;try{u(n)}catch(R){Nu(R)}n.currentTarget=null,u=d}}}}function mt(t,e){var l=e[bi];l===void 0&&(l=e[bi]=new Set);var a=t+"__bubble";l.has(a)||(rd(e,t,2,!1),l.add(a))}function ir(t,e,l){var a=0;e&&(a|=4),rd(l,t,a,e)}var Lu="_reactListening"+Math.random().toString(36).slice(2);function cr(t){if(!t[Lu]){t[Lu]=!0,Ir.forEach(function(l){l!=="selectionchange"&&(_v.has(l)||ir(l,!1,t),ir(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Lu]||(e[Lu]=!0,ir("selectionchange",!1,e))}}function rd(t,e,l,a){switch(wd(e)){case 2:var n=Iv;break;case 8:n=t0;break;default:n=xr}l=n.bind(null,e,l,t),n=void 0,!Ri||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function rr(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var r=a.stateNode.containerInfo;if(r===n)break;if(i===4)for(i=a.return;i!==null;){var d=i.tag;if((d===3||d===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;r!==null;){if(i=Il(r),i===null)return;if(d=i.tag,d===5||d===6||d===26||d===27){a=u=i;continue t}r=r.parentNode}}a=a.return}ho(function(){var x=u,R=Mi(l),D=[];t:{var A=Lo.get(t);if(A!==void 0){var T=lu,F=t;switch(t){case"keypress":if(tu(l)===0)break t;case"keydown":case"keyup":T=Sh;break;case"focusin":F="focus",T=Ui;break;case"focusout":F="blur",T=Ui;break;case"beforeblur":case"afterblur":T=Ui;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=go;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=rh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=Th;break;case qo:case Yo:case Go:T=fh;break;case Xo:T=zh;break;case"scroll":case"scrollend":T=ih;break;case"wheel":T=Mh;break;case"copy":case"cut":case"paste":T=mh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=po;break;case"toggle":case"beforetoggle":T=Rh}var W=(e&4)!==0,_t=!W&&(t==="scroll"||t==="scrollend"),g=W?A!==null?A+"Capture":null:A;W=[];for(var v=x,S;v!==null;){var O=v;if(S=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||S===null||g===null||(O=ka(v,g),O!=null&&W.push(Rn(v,O,S))),_t)break;v=v.return}0<W.length&&(A=new T(A,F,null,l,R),D.push({event:A,listeners:W}))}}if((e&7)===0){t:{if(A=t==="mouseover"||t==="pointerover",T=t==="mouseout"||t==="pointerout",A&&l!==_i&&(F=l.relatedTarget||l.fromElement)&&(Il(F)||F[Pl]))break t;if((T||A)&&(A=R.window===R?R:(A=R.ownerDocument)?A.defaultView||A.parentWindow:window,T?(F=l.relatedTarget||l.toElement,T=x,F=F?Il(F):null,F!==null&&(_t=b(F),W=F.tag,F!==_t||W!==5&&W!==27&&W!==6)&&(F=null)):(T=null,F=x),T!==F)){if(W=go,O="onMouseLeave",g="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(W=po,O="onPointerLeave",g="onPointerEnter",v="pointer"),_t=T==null?A:Za(T),S=F==null?A:Za(F),A=new W(O,v+"leave",T,l,R),A.target=_t,A.relatedTarget=S,O=null,Il(R)===x&&(W=new W(g,v+"enter",F,l,R),W.target=S,W.relatedTarget=_t,O=W),_t=O,T&&F)e:{for(W=T,g=F,v=0,S=W;S;S=Ua(S))v++;for(S=0,O=g;O;O=Ua(O))S++;for(;0<v-S;)W=Ua(W),v--;for(;0<S-v;)g=Ua(g),S--;for(;v--;){if(W===g||g!==null&&W===g.alternate)break e;W=Ua(W),g=Ua(g)}W=null}else W=null;T!==null&&od(D,A,T,W,!1),F!==null&&_t!==null&&od(D,_t,F,W,!0)}}t:{if(A=x?Za(x):window,T=A.nodeName&&A.nodeName.toLowerCase(),T==="select"||T==="input"&&A.type==="file")var L=Mo;else if(zo(A))if(No)L=Yh;else{L=Bh;var rt=Hh}else T=A.nodeName,!T||T.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?x&&zi(x.elementType)&&(L=Mo):L=qh;if(L&&(L=L(t,x))){_o(D,L,l,R);break t}rt&&rt(t,A,x),t==="focusout"&&x&&A.type==="number"&&x.memoizedProps.value!=null&&Ei(A,"number",A.value)}switch(rt=x?Za(x):window,t){case"focusin":(zo(rt)||rt.contentEditable==="true")&&(oa=rt,Yi=x,tn=null);break;case"focusout":tn=Yi=oa=null;break;case"mousedown":Gi=!0;break;case"contextmenu":case"mouseup":case"dragend":Gi=!1,Ho(D,l,R);break;case"selectionchange":if(Xh)break;case"keydown":case"keyup":Ho(D,l,R)}var K;if(ji)t:{switch(t){case"compositionstart":var $="onCompositionStart";break t;case"compositionend":$="onCompositionEnd";break t;case"compositionupdate":$="onCompositionUpdate";break t}$=void 0}else ra?To(t,l)&&($="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&($="onCompositionStart");$&&(So&&l.locale!=="ko"&&(ra||$!=="onCompositionStart"?$==="onCompositionEnd"&&ra&&(K=vo()):(cl=R,Oi="value"in cl?cl.value:cl.textContent,ra=!0)),rt=Qu(x,$),0<rt.length&&($=new bo($,t,null,l,R),D.push({event:$,listeners:rt}),K?$.data=K:(K=Eo(l),K!==null&&($.data=K)))),(K=Dh?wh(t,l):Uh(t,l))&&($=Qu(x,"onBeforeInput"),0<$.length&&(rt=new bo("onBeforeInput","beforeinput",null,l,R),D.push({event:rt,listeners:$}),rt.data=K)),Tv(D,t,x,l,R)}cd(D,e)})}function Rn(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Qu(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=ka(t,l),n!=null&&a.unshift(Rn(t,n,u)),n=ka(t,e),n!=null&&a.push(Rn(t,n,u))),t.tag===3)return a;t=t.return}return[]}function Ua(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function od(t,e,l,a,n){for(var u=e._reactName,i=[];l!==null&&l!==a;){var r=l,d=r.alternate,x=r.stateNode;if(r=r.tag,d!==null&&d===a)break;r!==5&&r!==26&&r!==27||x===null||(d=x,n?(x=ka(l,u),x!=null&&i.unshift(Rn(l,x,d))):n||(x=ka(l,u),x!=null&&i.push(Rn(l,x,d)))),l=l.return}i.length!==0&&t.push({event:e,listeners:i})}var Mv=/\r\n?/g,Nv=/\u0000|\uFFFD/g;function sd(t){return(typeof t=="string"?t:""+t).replace(Mv,`
`).replace(Nv,"")}function fd(t,e){return e=sd(e),sd(t)===e}function Vu(){}function zt(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ua(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ua(t,""+a);break;case"className":Wn(t,"class",a);break;case"tabIndex":Wn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Wn(t,l,a);break;case"style":fo(t,a,u);break;case"data":if(e!=="object"){Wn(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&zt(t,e,"name",n.name,n,null),zt(t,e,"formEncType",n.formEncType,n,null),zt(t,e,"formMethod",n.formMethod,n,null),zt(t,e,"formTarget",n.formTarget,n,null)):(zt(t,e,"encType",n.encType,n,null),zt(t,e,"method",n.method,n,null),zt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"onScroll":a!=null&&mt("scroll",t);break;case"onScrollEnd":a!=null&&mt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Pn(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":mt("beforetoggle",t),mt("toggle",t),Jn(t,"popover",a);break;case"xlinkActuate":Ye(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Ye(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Ye(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Ye(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Ye(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Ye(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Ye(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Jn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=nh.get(l)||l,Jn(t,l,a))}}function or(t,e,l,a,n,u){switch(l){case"style":fo(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"children":typeof a=="string"?ua(t,a):(typeof a=="number"||typeof a=="bigint")&&ua(t,""+a);break;case"onScroll":a!=null&&mt("scroll",t);break;case"onScrollEnd":a!=null&&mt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!to.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[le]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Jn(t,l,a)}}}function Ft(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":mt("error",t),mt("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:zt(t,e,u,i,l,null)}}n&&zt(t,e,"srcSet",l.srcSet,l,null),a&&zt(t,e,"src",l.src,l,null);return;case"input":mt("invalid",t);var r=u=i=n=null,d=null,x=null;for(a in l)if(l.hasOwnProperty(a)){var R=l[a];if(R!=null)switch(a){case"name":n=R;break;case"type":i=R;break;case"checked":d=R;break;case"defaultChecked":x=R;break;case"value":u=R;break;case"defaultValue":r=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(o(137,e));break;default:zt(t,e,a,R,l,null)}}co(t,u,r,d,x,i,n,!1),$n(t);return;case"select":mt("invalid",t),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(r=l[n],r!=null))switch(n){case"value":u=r;break;case"defaultValue":i=r;break;case"multiple":a=r;default:zt(t,e,n,r,l,null)}e=u,l=i,t.multiple=!!a,e!=null?na(t,!!a,e,!1):l!=null&&na(t,!!a,l,!0);return;case"textarea":mt("invalid",t),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(r=l[i],r!=null))switch(i){case"value":a=r;break;case"defaultValue":n=r;break;case"children":u=r;break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(o(91));break;default:zt(t,e,i,r,l,null)}oo(t,a,n,u),$n(t);return;case"option":for(d in l)if(l.hasOwnProperty(d)&&(a=l[d],a!=null))switch(d){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:zt(t,e,d,a,l,null)}return;case"dialog":mt("beforetoggle",t),mt("toggle",t),mt("cancel",t),mt("close",t);break;case"iframe":case"object":mt("load",t);break;case"video":case"audio":for(a=0;a<Nn.length;a++)mt(Nn[a],t);break;case"image":mt("error",t),mt("load",t);break;case"details":mt("toggle",t);break;case"embed":case"source":case"link":mt("error",t),mt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(x in l)if(l.hasOwnProperty(x)&&(a=l[x],a!=null))switch(x){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:zt(t,e,x,a,l,null)}return;default:if(zi(e)){for(R in l)l.hasOwnProperty(R)&&(a=l[R],a!==void 0&&or(t,e,R,a,l,void 0));return}}for(r in l)l.hasOwnProperty(r)&&(a=l[r],a!=null&&zt(t,e,r,a,l,null))}function Rv(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,r=null,d=null,x=null,R=null;for(T in l){var D=l[T];if(l.hasOwnProperty(T)&&D!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":d=D;default:a.hasOwnProperty(T)||zt(t,e,T,null,a,D)}}for(var A in a){var T=a[A];if(D=l[A],a.hasOwnProperty(A)&&(T!=null||D!=null))switch(A){case"type":u=T;break;case"name":n=T;break;case"checked":x=T;break;case"defaultChecked":R=T;break;case"value":i=T;break;case"defaultValue":r=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(o(137,e));break;default:T!==D&&zt(t,e,A,T,a,D)}}Ti(t,i,r,d,x,R,u,n);return;case"select":T=i=r=A=null;for(u in l)if(d=l[u],l.hasOwnProperty(u)&&d!=null)switch(u){case"value":break;case"multiple":T=d;default:a.hasOwnProperty(u)||zt(t,e,u,null,a,d)}for(n in a)if(u=a[n],d=l[n],a.hasOwnProperty(n)&&(u!=null||d!=null))switch(n){case"value":A=u;break;case"defaultValue":r=u;break;case"multiple":i=u;default:u!==d&&zt(t,e,n,u,a,d)}e=r,l=i,a=T,A!=null?na(t,!!l,A,!1):!!a!=!!l&&(e!=null?na(t,!!l,e,!0):na(t,!!l,l?[]:"",!1));return;case"textarea":T=A=null;for(r in l)if(n=l[r],l.hasOwnProperty(r)&&n!=null&&!a.hasOwnProperty(r))switch(r){case"value":break;case"children":break;default:zt(t,e,r,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":A=n;break;case"defaultValue":T=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(o(91));break;default:n!==u&&zt(t,e,i,n,a,u)}ro(t,A,T);return;case"option":for(var F in l)if(A=l[F],l.hasOwnProperty(F)&&A!=null&&!a.hasOwnProperty(F))switch(F){case"selected":t.selected=!1;break;default:zt(t,e,F,null,a,A)}for(d in a)if(A=a[d],T=l[d],a.hasOwnProperty(d)&&A!==T&&(A!=null||T!=null))switch(d){case"selected":t.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:zt(t,e,d,A,a,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in l)A=l[W],l.hasOwnProperty(W)&&A!=null&&!a.hasOwnProperty(W)&&zt(t,e,W,null,a,A);for(x in a)if(A=a[x],T=l[x],a.hasOwnProperty(x)&&A!==T&&(A!=null||T!=null))switch(x){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(o(137,e));break;default:zt(t,e,x,A,a,T)}return;default:if(zi(e)){for(var _t in l)A=l[_t],l.hasOwnProperty(_t)&&A!==void 0&&!a.hasOwnProperty(_t)&&or(t,e,_t,void 0,a,A);for(R in a)A=a[R],T=l[R],!a.hasOwnProperty(R)||A===T||A===void 0&&T===void 0||or(t,e,R,A,a,T);return}}for(var g in l)A=l[g],l.hasOwnProperty(g)&&A!=null&&!a.hasOwnProperty(g)&&zt(t,e,g,null,a,A);for(D in a)A=a[D],T=l[D],!a.hasOwnProperty(D)||A===T||A==null&&T==null||zt(t,e,D,A,a,T)}var sr=null,fr=null;function Zu(t){return t.nodeType===9?t:t.ownerDocument}function dd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function md(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function dr(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var mr=null;function Ov(){var t=window.event;return t&&t.type==="popstate"?t===mr?!1:(mr=t,!0):(mr=null,!1)}var hd=typeof setTimeout=="function"?setTimeout:void 0,Dv=typeof clearTimeout=="function"?clearTimeout:void 0,vd=typeof Promise=="function"?Promise:void 0,wv=typeof queueMicrotask=="function"?queueMicrotask:typeof vd<"u"?function(t){return vd.resolve(null).then(t).catch(Uv)}:hd;function Uv(t){setTimeout(function(){throw t})}function Tl(t){return t==="head"}function yd(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var i=t.ownerDocument;if(l&1&&On(i.documentElement),l&2&&On(i.body),l&4)for(l=i.head,On(l),i=l.firstChild;i;){var r=i.nextSibling,d=i.nodeName;i[Va]||d==="SCRIPT"||d==="STYLE"||d==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=r}}if(n===0){t.removeChild(u),qn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);qn(e)}function hr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":hr(l),pi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Cv(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Va])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Re(t.nextSibling),t===null)break}return null}function jv(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Re(t.nextSibling),t===null))return null;return t}function vr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Hv(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Re(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var yr=null;function gd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function bd(t,e,l){switch(e=Zu(l),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function On(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);pi(t)}var ze=new Map,pd=new Set;function ku(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ie=q.d;q.d={f:Bv,r:qv,D:Yv,C:Gv,L:Xv,m:Lv,X:Vv,S:Qv,M:Zv};function Bv(){var t=Ie.f(),e=Bu();return t||e}function qv(t){var e=ta(t);e!==null&&e.tag===5&&e.type==="form"?qs(e):Ie.r(t)}var Ca=typeof document>"u"?null:document;function Sd(t,e,l){var a=Ca;if(a&&typeof e=="string"&&e){var n=be(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),pd.has(n)||(pd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),Ft(e,"link",t),Zt(e),a.head.appendChild(e)))}}function Yv(t){Ie.D(t),Sd("dns-prefetch",t,null)}function Gv(t,e){Ie.C(t,e),Sd("preconnect",t,e)}function Xv(t,e,l){Ie.L(t,e,l);var a=Ca;if(a&&t&&e){var n='link[rel="preload"][as="'+be(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+be(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+be(l.imageSizes)+'"]')):n+='[href="'+be(t)+'"]';var u=n;switch(e){case"style":u=ja(t);break;case"script":u=Ha(t)}ze.has(u)||(t=M({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),ze.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Dn(u))||e==="script"&&a.querySelector(wn(u))||(e=a.createElement("link"),Ft(e,"link",t),Zt(e),a.head.appendChild(e)))}}function Lv(t,e){Ie.m(t,e);var l=Ca;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+be(a)+'"][href="'+be(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ha(t)}if(!ze.has(u)&&(t=M({rel:"modulepreload",href:t},e),ze.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(wn(u)))return}a=l.createElement("link"),Ft(a,"link",t),Zt(a),l.head.appendChild(a)}}}function Qv(t,e,l){Ie.S(t,e,l);var a=Ca;if(a&&t){var n=ea(a).hoistableStyles,u=ja(t);e=e||"default";var i=n.get(u);if(!i){var r={loading:0,preload:null};if(i=a.querySelector(Dn(u)))r.loading=5;else{t=M({rel:"stylesheet",href:t,"data-precedence":e},l),(l=ze.get(u))&&gr(t,l);var d=i=a.createElement("link");Zt(d),Ft(d,"link",t),d._p=new Promise(function(x,R){d.onload=x,d.onerror=R}),d.addEventListener("load",function(){r.loading|=1}),d.addEventListener("error",function(){r.loading|=2}),r.loading|=4,Ku(i,e,a)}i={type:"stylesheet",instance:i,count:1,state:r},n.set(u,i)}}}function Vv(t,e){Ie.X(t,e);var l=Ca;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(wn(n)),u||(t=M({src:t,async:!0},e),(e=ze.get(n))&&br(t,e),u=l.createElement("script"),Zt(u),Ft(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Zv(t,e){Ie.M(t,e);var l=Ca;if(l&&t){var a=ea(l).hoistableScripts,n=Ha(t),u=a.get(n);u||(u=l.querySelector(wn(n)),u||(t=M({src:t,async:!0,type:"module"},e),(e=ze.get(n))&&br(t,e),u=l.createElement("script"),Zt(u),Ft(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function xd(t,e,l,a){var n=(n=I.current)?ku(n):null;if(!n)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=ja(l.href),l=ea(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=ja(l.href);var u=ea(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(Dn(t)))&&!u._p&&(i.instance=u,i.state.loading=5),ze.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ze.set(t,l),u||kv(n,t,l,i.state))),e&&a===null)throw Error(o(528,""));return i}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ha(l),l=ea(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function ja(t){return'href="'+be(t)+'"'}function Dn(t){return'link[rel="stylesheet"]['+t+"]"}function Ad(t){return M({},t,{"data-precedence":t.precedence,precedence:null})}function kv(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Ft(e,"link",l),Zt(e),t.head.appendChild(e))}function Ha(t){return'[src="'+be(t)+'"]'}function wn(t){return"script[async]"+t}function Td(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+be(l.href)+'"]');if(a)return e.instance=a,Zt(a),a;var n=M({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Zt(a),Ft(a,"style",n),Ku(a,l.precedence,t),e.instance=a;case"stylesheet":n=ja(l.href);var u=t.querySelector(Dn(n));if(u)return e.state.loading|=4,e.instance=u,Zt(u),u;a=Ad(l),(n=ze.get(n))&&gr(a,n),u=(t.ownerDocument||t).createElement("link"),Zt(u);var i=u;return i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),Ft(u,"link",a),e.state.loading|=4,Ku(u,l.precedence,t),e.instance=u;case"script":return u=Ha(l.src),(n=t.querySelector(wn(u)))?(e.instance=n,Zt(n),n):(a=l,(n=ze.get(u))&&(a=M({},l),br(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Zt(n),Ft(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ku(a,l.precedence,t));return e.instance}function Ku(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var r=a[i];if(r.dataset.precedence===e)u=r;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function gr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function br(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ju=null;function Ed(t,e,l){if(Ju===null){var a=new Map,n=Ju=new Map;n.set(l,a)}else n=Ju,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[Va]||u[Pt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(e)||"";i=t+i;var r=a.get(i);r?r.push(u):a.set(i,[u])}}return a}function zd(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function Kv(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function _d(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Un=null;function Jv(){}function Wv(t,e,l){if(Un===null)throw Error(o(475));var a=Un;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=ja(l.href),u=t.querySelector(Dn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Wu.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,Zt(u);return}u=t.ownerDocument||t,l=Ad(l),(n=ze.get(n))&&gr(l,n),u=u.createElement("link"),Zt(u);var i=u;i._p=new Promise(function(r,d){i.onload=r,i.onerror=d}),Ft(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Wu.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function $v(){if(Un===null)throw Error(o(475));var t=Un;return t.stylesheets&&t.count===0&&pr(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&pr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Wu(){if(this.count--,this.count===0){if(this.stylesheets)pr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var $u=null;function pr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,$u=new Map,e.forEach(Fv,t),$u=null,Wu.call(t))}function Fv(t,e){if(!(e.state.loading&4)){var l=$u.get(t);if(l)var a=l.get(null);else{l=new Map,$u.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=e.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=Wu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Cn={$$typeof:pt,Provider:null,Consumer:null,_currentValue:j,_currentValue2:j,_threadCount:0};function Pv(t,e,l,a,n,u,i,r){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=vi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vi(0),this.hiddenUpdates=vi(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=r,this.incompleteTransitions=new Map}function Md(t,e,l,a,n,u,i,r,d,x,R,D){return t=new Pv(t,e,l,i,r,d,x,D),e=1,u===!0&&(e|=24),u=fe(3,null,null,e),t.current=u,u.stateNode=t,e=tc(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},nc(u),t}function Nd(t){return t?(t=ma,t):ma}function Rd(t,e,l,a,n,u){n=Nd(n),a.context===null?a.context=n:a.pendingContext=n,a=sl(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=fl(t,a,e),l!==null&&(ye(l,t,e),sn(l,t,e))}function Od(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function Sr(t,e){Od(t,e),(t=t.alternate)&&Od(t,e)}function Dd(t){if(t.tag===13){var e=da(t,67108864);e!==null&&ye(e,t,67108864),Sr(t,67108864)}}var Fu=!0;function Iv(t,e,l,a){var n=z.T;z.T=null;var u=q.p;try{q.p=2,xr(t,e,l,a)}finally{q.p=u,z.T=n}}function t0(t,e,l,a){var n=z.T;z.T=null;var u=q.p;try{q.p=8,xr(t,e,l,a)}finally{q.p=u,z.T=n}}function xr(t,e,l,a){if(Fu){var n=Ar(a);if(n===null)rr(t,e,a,Pu,l),Ud(t,a);else if(l0(n,t,e,l,a))a.stopPropagation();else if(Ud(t,a),e&4&&-1<e0.indexOf(t)){for(;n!==null;){var u=ta(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=Ol(u.pendingLanes);if(i!==0){var r=u;for(r.pendingLanes|=2,r.entangledLanes|=2;i;){var d=1<<31-oe(i);r.entanglements[1]|=d,i&=~d}je(u),(xt&6)===0&&(ju=Oe()+500,Mn(0))}}break;case 13:r=da(u,2),r!==null&&ye(r,u,2),Bu(),Sr(u,2)}if(u=Ar(a),u===null&&rr(t,e,a,Pu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else rr(t,e,a,null,l)}}function Ar(t){return t=Mi(t),Tr(t)}var Pu=null;function Tr(t){if(Pu=null,t=Il(t),t!==null){var e=b(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=E(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Pu=t,null}function wd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Gm()){case Vr:return 2;case Zr:return 8;case Vn:case Xm:return 32;case kr:return 268435456;default:return 32}default:return 32}}var Er=!1,El=null,zl=null,_l=null,jn=new Map,Hn=new Map,Ml=[],e0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Ud(t,e){switch(t){case"focusin":case"focusout":El=null;break;case"dragenter":case"dragleave":zl=null;break;case"mouseover":case"mouseout":_l=null;break;case"pointerover":case"pointerout":jn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hn.delete(e.pointerId)}}function Bn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=ta(e),e!==null&&Dd(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function l0(t,e,l,a,n){switch(e){case"focusin":return El=Bn(El,t,e,l,a,n),!0;case"dragenter":return zl=Bn(zl,t,e,l,a,n),!0;case"mouseover":return _l=Bn(_l,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return jn.set(u,Bn(jn.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Hn.set(u,Bn(Hn.get(u)||null,t,e,l,a,n)),!0}return!1}function Cd(t){var e=Il(t.target);if(e!==null){var l=b(e);if(l!==null){if(e=l.tag,e===13){if(e=E(l),e!==null){t.blockedOn=e,Wm(t.priority,function(){if(l.tag===13){var a=ve();a=yi(a);var n=da(l,a);n!==null&&ye(n,l,a),Sr(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Iu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Ar(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);_i=a,l.target.dispatchEvent(a),_i=null}else return e=ta(l),e!==null&&Dd(e),t.blockedOn=l,!1;e.shift()}return!0}function jd(t,e,l){Iu(t)&&l.delete(e)}function a0(){Er=!1,El!==null&&Iu(El)&&(El=null),zl!==null&&Iu(zl)&&(zl=null),_l!==null&&Iu(_l)&&(_l=null),jn.forEach(jd),Hn.forEach(jd)}function ti(t,e){t.blockedOn===e&&(t.blockedOn=null,Er||(Er=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,a0)))}var ei=null;function Hd(t){ei!==t&&(ei=t,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){ei===t&&(ei=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(Tr(a||l)===null)continue;break}var u=ta(l);u!==null&&(t.splice(e,3),e-=3,Tc(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function qn(t){function e(d){return ti(d,t)}El!==null&&ti(El,t),zl!==null&&ti(zl,t),_l!==null&&ti(_l,t),jn.forEach(e),Hn.forEach(e);for(var l=0;l<Ml.length;l++){var a=Ml[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Ml.length&&(l=Ml[0],l.blockedOn===null);)Cd(l),l.blockedOn===null&&Ml.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[le]||null;if(typeof u=="function")i||Hd(l);else if(i){var r=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[le]||null)r=i.formAction;else if(Tr(n)!==null)continue}else r=i.action;typeof r=="function"?l[a+1]=r:(l.splice(a,3),a-=3),Hd(l)}}}function zr(t){this._internalRoot=t}li.prototype.render=zr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var l=e.current,a=ve();Rd(l,a,t,e,null,null)},li.prototype.unmount=zr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Rd(t.current,2,null,t,null,null),Bu(),e[Pl]=null}};function li(t){this._internalRoot=t}li.prototype.unstable_scheduleHydration=function(t){if(t){var e=Fr();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Ml.length&&e!==0&&e<Ml[l].priority;l++);Ml.splice(l,0,t),l===0&&Cd(t)}};var Bd=s.version;if(Bd!=="19.1.0")throw Error(o(527,Bd,"19.1.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=p(e),t=t!==null?y(t):null,t=t===null?null:t.stateNode,t};var n0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ai=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ai.isDisabled&&ai.supportsFiber)try{Xa=ai.inject(n0),re=ai}catch{}}return Gn.createRoot=function(t,e){if(!m(t))throw Error(o(299));var l=!1,a="",n=Ps,u=Is,i=tf,r=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(i=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(r=e.unstable_transitionCallbacks)),e=Md(t,1,!1,null,null,l,a,n,u,i,r,null),t[Pl]=e.current,cr(t),new zr(e)},Gn.hydrateRoot=function(t,e,l){if(!m(t))throw Error(o(299));var a=!1,n="",u=Ps,i=Is,r=tf,d=null,x=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(r=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(d=l.unstable_transitionCallbacks),l.formState!==void 0&&(x=l.formState)),e=Md(t,1,!0,e,l??null,a,n,u,i,r,d,x),e.context=Nd(null),l=e.current,a=ve(),a=yi(a),n=sl(a),n.callback=null,fl(l,n,a),l=a,e.current.lanes=l,Qa(e,l),je(e),t[Pl]=e.current,cr(t),new li(e)},Gn.version="19.1.0",Gn}var Kd;function m0(){if(Kd)return Nr.exports;Kd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Nr.exports=d0(),Nr.exports}var h0=m0();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v0=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),y0=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(s,f,o)=>o?o.toUpperCase():f.toLowerCase()),Jd=c=>{const s=y0(c);return s.charAt(0).toUpperCase()+s.slice(1)},rm=(...c)=>c.filter((s,f,o)=>!!s&&s.trim()!==""&&o.indexOf(s)===f).join(" ").trim(),g0=c=>{for(const s in c)if(s.startsWith("aria-")||s==="role"||s==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var b0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=w.forwardRef(({color:c="currentColor",size:s=24,strokeWidth:f=2,absoluteStrokeWidth:o,className:m="",children:b,iconNode:E,...N},p)=>w.createElement("svg",{ref:p,...b0,width:s,height:s,stroke:c,strokeWidth:o?Number(f)*24/Number(s):f,className:rm("lucide",m),...!b&&!g0(N)&&{"aria-hidden":"true"},...N},[...E.map(([y,M])=>w.createElement(y,M)),...Array.isArray(b)?b:[b]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=(c,s)=>{const f=w.forwardRef(({className:o,...m},b)=>w.createElement(p0,{ref:b,iconNode:s,className:rm(`lucide-${v0(Jd(c))}`,`lucide-${c}`,o),...m}));return f.displayName=Jd(c),f};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S0=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],Wd=Be("book",S0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]],A0=Be("corner-down-left",x0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T0=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],E0=Be("globe",T0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z0=[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]],_0=Be("maximize-2",z0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M0=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],wr=Be("message-square",M0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N0=[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]],R0=Be("minimize-2",N0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],D0=Be("search",O0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w0=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],U0=Be("tag",w0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C0=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],j0=Be("user",C0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H0=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],B0=Be("x",H0);function $d(c,s){if(typeof c=="function")return c(s);c!=null&&(c.current=s)}function om(...c){return s=>{let f=!1;const o=c.map(m=>{const b=$d(m,s);return!f&&typeof b=="function"&&(f=!0),b});if(f)return()=>{for(let m=0;m<o.length;m++){const b=o[m];typeof b=="function"?b():$d(c[m],null)}}}}function Fl(...c){return w.useCallback(om(...c),c)}function sm(c){const s=q0(c),f=w.forwardRef((o,m)=>{const{children:b,...E}=o,N=w.Children.toArray(b),p=N.find(G0);if(p){const y=p.props.children,M=N.map(C=>C===p?w.Children.count(y)>1?w.Children.only(null):w.isValidElement(y)?y.props.children:null:C);return _.jsx(s,{...E,ref:m,children:w.isValidElement(y)?w.cloneElement(y,void 0,M):null})}return _.jsx(s,{...E,ref:m,children:b})});return f.displayName=`${c}.Slot`,f}var fm=sm("Slot");function q0(c){const s=w.forwardRef((f,o)=>{const{children:m,...b}=f;if(w.isValidElement(m)){const E=L0(m),N=X0(b,m.props);return m.type!==w.Fragment&&(N.ref=o?om(o,E):E),w.cloneElement(m,N)}return w.Children.count(m)>1?w.Children.only(null):null});return s.displayName=`${c}.SlotClone`,s}var Y0=Symbol("radix.slottable");function G0(c){return w.isValidElement(c)&&typeof c.type=="function"&&"__radixId"in c.type&&c.type.__radixId===Y0}function X0(c,s){const f={...s};for(const o in s){const m=c[o],b=s[o];/^on[A-Z]/.test(o)?m&&b?f[o]=(...N)=>{const p=b(...N);return m(...N),p}:m&&(f[o]=m):o==="style"?f[o]={...m,...b}:o==="className"&&(f[o]=[m,b].filter(Boolean).join(" "))}return{...c,...f}}function L0(c){var o,m;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,f=s&&"isReactWarning"in s&&s.isReactWarning;return f?c.ref:(s=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,f=s&&"isReactWarning"in s&&s.isReactWarning,f?c.props.ref:c.props.ref||c.ref)}function dm(c){var s,f,o="";if(typeof c=="string"||typeof c=="number")o+=c;else if(typeof c=="object")if(Array.isArray(c)){var m=c.length;for(s=0;s<m;s++)c[s]&&(f=dm(c[s]))&&(o&&(o+=" "),o+=f)}else for(f in c)c[f]&&(o&&(o+=" "),o+=f);return o}function mm(){for(var c,s,f=0,o="",m=arguments.length;f<m;f++)(c=arguments[f])&&(s=dm(c))&&(o&&(o+=" "),o+=s);return o}const Fd=c=>typeof c=="boolean"?`${c}`:c===0?"0":c,Pd=mm,hm=(c,s)=>f=>{var o;if((s==null?void 0:s.variants)==null)return Pd(c,f==null?void 0:f.class,f==null?void 0:f.className);const{variants:m,defaultVariants:b}=s,E=Object.keys(m).map(y=>{const M=f==null?void 0:f[y],C=b==null?void 0:b[y];if(M===null)return null;const H=Fd(M)||Fd(C);return m[y][H]}),N=f&&Object.entries(f).reduce((y,M)=>{let[C,H]=M;return H===void 0||(y[C]=H),y},{}),p=s==null||(o=s.compoundVariants)===null||o===void 0?void 0:o.reduce((y,M)=>{let{class:C,className:H,...k}=M;return Object.entries(k).every(J=>{let[tt,ot]=J;return Array.isArray(ot)?ot.includes({...b,...N}[tt]):{...b,...N}[tt]===ot})?[...y,C,H]:y},[]);return Pd(c,E,p,f==null?void 0:f.class,f==null?void 0:f.className)},Gr="-",Q0=c=>{const s=Z0(c),{conflictingClassGroups:f,conflictingClassGroupModifiers:o}=c;return{getClassGroupId:E=>{const N=E.split(Gr);return N[0]===""&&N.length!==1&&N.shift(),vm(N,s)||V0(E)},getConflictingClassGroupIds:(E,N)=>{const p=f[E]||[];return N&&o[E]?[...p,...o[E]]:p}}},vm=(c,s)=>{var E;if(c.length===0)return s.classGroupId;const f=c[0],o=s.nextPart.get(f),m=o?vm(c.slice(1),o):void 0;if(m)return m;if(s.validators.length===0)return;const b=c.join(Gr);return(E=s.validators.find(({validator:N})=>N(b)))==null?void 0:E.classGroupId},Id=/^\[(.+)\]$/,V0=c=>{if(Id.test(c)){const s=Id.exec(c)[1],f=s==null?void 0:s.substring(0,s.indexOf(":"));if(f)return"arbitrary.."+f}},Z0=c=>{const{theme:s,classGroups:f}=c,o={nextPart:new Map,validators:[]};for(const m in f)jr(f[m],o,m,s);return o},jr=(c,s,f,o)=>{c.forEach(m=>{if(typeof m=="string"){const b=m===""?s:tm(s,m);b.classGroupId=f;return}if(typeof m=="function"){if(k0(m)){jr(m(o),s,f,o);return}s.validators.push({validator:m,classGroupId:f});return}Object.entries(m).forEach(([b,E])=>{jr(E,tm(s,b),f,o)})})},tm=(c,s)=>{let f=c;return s.split(Gr).forEach(o=>{f.nextPart.has(o)||f.nextPart.set(o,{nextPart:new Map,validators:[]}),f=f.nextPart.get(o)}),f},k0=c=>c.isThemeGetter,K0=c=>{if(c<1)return{get:()=>{},set:()=>{}};let s=0,f=new Map,o=new Map;const m=(b,E)=>{f.set(b,E),s++,s>c&&(s=0,o=f,f=new Map)};return{get(b){let E=f.get(b);if(E!==void 0)return E;if((E=o.get(b))!==void 0)return m(b,E),E},set(b,E){f.has(b)?f.set(b,E):m(b,E)}}},Hr="!",Br=":",J0=Br.length,W0=c=>{const{prefix:s,experimentalParseClassName:f}=c;let o=m=>{const b=[];let E=0,N=0,p=0,y;for(let J=0;J<m.length;J++){let tt=m[J];if(E===0&&N===0){if(tt===Br){b.push(m.slice(p,J)),p=J+J0;continue}if(tt==="/"){y=J;continue}}tt==="["?E++:tt==="]"?E--:tt==="("?N++:tt===")"&&N--}const M=b.length===0?m:m.substring(p),C=$0(M),H=C!==M,k=y&&y>p?y-p:void 0;return{modifiers:b,hasImportantModifier:H,baseClassName:C,maybePostfixModifierPosition:k}};if(s){const m=s+Br,b=o;o=E=>E.startsWith(m)?b(E.substring(m.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:E,maybePostfixModifierPosition:void 0}}if(f){const m=o;o=b=>f({className:b,parseClassName:m})}return o},$0=c=>c.endsWith(Hr)?c.substring(0,c.length-1):c.startsWith(Hr)?c.substring(1):c,F0=c=>{const s=Object.fromEntries(c.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const m=[];let b=[];return o.forEach(E=>{E[0]==="["||s[E]?(m.push(...b.sort(),E),b=[]):b.push(E)}),m.push(...b.sort()),m}},P0=c=>({cache:K0(c.cacheSize),parseClassName:W0(c),sortModifiers:F0(c),...Q0(c)}),I0=/\s+/,ty=(c,s)=>{const{parseClassName:f,getClassGroupId:o,getConflictingClassGroupIds:m,sortModifiers:b}=s,E=[],N=c.trim().split(I0);let p="";for(let y=N.length-1;y>=0;y-=1){const M=N[y],{isExternal:C,modifiers:H,hasImportantModifier:k,baseClassName:J,maybePostfixModifierPosition:tt}=f(M);if(C){p=M+(p.length>0?" "+p:p);continue}let ot=!!tt,Tt=o(ot?J.substring(0,tt):J);if(!Tt){if(!ot){p=M+(p.length>0?" "+p:p);continue}if(Tt=o(J),!Tt){p=M+(p.length>0?" "+p:p);continue}ot=!1}const jt=b(H).join(":"),pt=k?jt+Hr:jt,et=pt+Tt;if(E.includes(et))continue;E.push(et);const P=m(Tt,ot);for(let St=0;St<P.length;++St){const Mt=P[St];E.push(pt+Mt)}p=M+(p.length>0?" "+p:p)}return p};function ey(){let c=0,s,f,o="";for(;c<arguments.length;)(s=arguments[c++])&&(f=ym(s))&&(o&&(o+=" "),o+=f);return o}const ym=c=>{if(typeof c=="string")return c;let s,f="";for(let o=0;o<c.length;o++)c[o]&&(s=ym(c[o]))&&(f&&(f+=" "),f+=s);return f};function ly(c,...s){let f,o,m,b=E;function E(p){const y=s.reduce((M,C)=>C(M),c());return f=P0(y),o=f.cache.get,m=f.cache.set,b=N,N(p)}function N(p){const y=o(p);if(y)return y;const M=ty(p,f);return m(p,M),M}return function(){return b(ey.apply(null,arguments))}}const Vt=c=>{const s=f=>f[c]||[];return s.isThemeGetter=!0,s},gm=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,bm=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ay=/^\d+\/\d+$/,ny=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,uy=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,iy=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,cy=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ry=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ba=c=>ay.test(c),ct=c=>!!c&&!Number.isNaN(Number(c)),Rl=c=>!!c&&Number.isInteger(Number(c)),Ur=c=>c.endsWith("%")&&ct(c.slice(0,-1)),tl=c=>ny.test(c),oy=()=>!0,sy=c=>uy.test(c)&&!iy.test(c),pm=()=>!1,fy=c=>cy.test(c),dy=c=>ry.test(c),my=c=>!Q(c)&&!V(c),hy=c=>Ya(c,Am,pm),Q=c=>gm.test(c),Jl=c=>Ya(c,Tm,sy),Cr=c=>Ya(c,py,ct),em=c=>Ya(c,Sm,pm),vy=c=>Ya(c,xm,dy),ni=c=>Ya(c,Em,fy),V=c=>bm.test(c),Xn=c=>Ga(c,Tm),yy=c=>Ga(c,Sy),lm=c=>Ga(c,Sm),gy=c=>Ga(c,Am),by=c=>Ga(c,xm),ui=c=>Ga(c,Em,!0),Ya=(c,s,f)=>{const o=gm.exec(c);return o?o[1]?s(o[1]):f(o[2]):!1},Ga=(c,s,f=!1)=>{const o=bm.exec(c);return o?o[1]?s(o[1]):f:!1},Sm=c=>c==="position"||c==="percentage",xm=c=>c==="image"||c==="url",Am=c=>c==="length"||c==="size"||c==="bg-size",Tm=c=>c==="length",py=c=>c==="number",Sy=c=>c==="family-name",Em=c=>c==="shadow",xy=()=>{const c=Vt("color"),s=Vt("font"),f=Vt("text"),o=Vt("font-weight"),m=Vt("tracking"),b=Vt("leading"),E=Vt("breakpoint"),N=Vt("container"),p=Vt("spacing"),y=Vt("radius"),M=Vt("shadow"),C=Vt("inset-shadow"),H=Vt("text-shadow"),k=Vt("drop-shadow"),J=Vt("blur"),tt=Vt("perspective"),ot=Vt("aspect"),Tt=Vt("ease"),jt=Vt("animate"),pt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],et=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...et(),V,Q],St=()=>["auto","hidden","clip","visible","scroll"],Mt=()=>["auto","contain","none"],Z=()=>[V,Q,p],at=()=>[Ba,"full","auto",...Z()],Ut=()=>[Rl,"none","subgrid",V,Q],X=()=>["auto",{span:["full",Rl,V,Q]},Rl,V,Q],lt=()=>[Rl,"auto",V,Q],ut=()=>["auto","min","max","fr",V,Q],Nt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],st=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...Z()],q=()=>[Ba,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Z()],j=()=>[c,V,Q],bt=()=>[...et(),lm,em,{position:[V,Q]}],h=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",gy,hy,{size:[V,Q]}],Y=()=>[Ur,Xn,Jl],B=()=>["","none","full",y,V,Q],G=()=>["",ct,Xn,Jl],ft=()=>["solid","dashed","dotted","double"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],vt=()=>[ct,Ur,lm,em],Rt=()=>["","none",J,V,Q],ce=()=>["none",ct,V,Q],ll=()=>["none",ct,V,Q],al=()=>[ct,V,Q],nl=()=>[Ba,"full",...Z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[tl],breakpoint:[tl],color:[oy],container:[tl],"drop-shadow":[tl],ease:["in","out","in-out"],font:[my],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[tl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[tl],shadow:[tl],spacing:["px",ct],text:[tl],"text-shadow":[tl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ba,Q,V,ot]}],container:["container"],columns:[{columns:[ct,Q,V,N]}],"break-after":[{"break-after":pt()}],"break-before":[{"break-before":pt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:St()}],"overflow-x":[{"overflow-x":St()}],"overflow-y":[{"overflow-y":St()}],overscroll:[{overscroll:Mt()}],"overscroll-x":[{"overscroll-x":Mt()}],"overscroll-y":[{"overscroll-y":Mt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:at()}],"inset-x":[{"inset-x":at()}],"inset-y":[{"inset-y":at()}],start:[{start:at()}],end:[{end:at()}],top:[{top:at()}],right:[{right:at()}],bottom:[{bottom:at()}],left:[{left:at()}],visibility:["visible","invisible","collapse"],z:[{z:[Rl,"auto",V,Q]}],basis:[{basis:[Ba,"full","auto",N,...Z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ct,Ba,"auto","initial","none",Q]}],grow:[{grow:["",ct,V,Q]}],shrink:[{shrink:["",ct,V,Q]}],order:[{order:[Rl,"first","last","none",V,Q]}],"grid-cols":[{"grid-cols":Ut()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":lt()}],"col-end":[{"col-end":lt()}],"grid-rows":[{"grid-rows":Ut()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":lt()}],"row-end":[{"row-end":lt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ut()}],"auto-rows":[{"auto-rows":ut()}],gap:[{gap:Z()}],"gap-x":[{"gap-x":Z()}],"gap-y":[{"gap-y":Z()}],"justify-content":[{justify:[...Nt(),"normal"]}],"justify-items":[{"justify-items":[...st(),"normal"]}],"justify-self":[{"justify-self":["auto",...st()]}],"align-content":[{content:["normal",...Nt()]}],"align-items":[{items:[...st(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...st(),{baseline:["","last"]}]}],"place-content":[{"place-content":Nt()}],"place-items":[{"place-items":[...st(),"baseline"]}],"place-self":[{"place-self":["auto",...st()]}],p:[{p:Z()}],px:[{px:Z()}],py:[{py:Z()}],ps:[{ps:Z()}],pe:[{pe:Z()}],pt:[{pt:Z()}],pr:[{pr:Z()}],pb:[{pb:Z()}],pl:[{pl:Z()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":Z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Z()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[N,"screen",...q()]}],"min-w":[{"min-w":[N,"screen","none",...q()]}],"max-w":[{"max-w":[N,"screen","none","prose",{screen:[E]},...q()]}],h:[{h:["screen","lh",...q()]}],"min-h":[{"min-h":["screen","lh","none",...q()]}],"max-h":[{"max-h":["screen","lh",...q()]}],"font-size":[{text:["base",f,Xn,Jl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,V,Cr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ur,Q]}],"font-family":[{font:[yy,Q,s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[m,V,Q]}],"line-clamp":[{"line-clamp":[ct,"none",V,Cr]}],leading:[{leading:[b,...Z()]}],"list-image":[{"list-image":["none",V,Q]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",V,Q]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:j()}],"text-color":[{text:j()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ft(),"wavy"]}],"text-decoration-thickness":[{decoration:[ct,"from-font","auto",V,Jl]}],"text-decoration-color":[{decoration:j()}],"underline-offset":[{"underline-offset":[ct,"auto",V,Q]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V,Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V,Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:bt()}],"bg-repeat":[{bg:h()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Rl,V,Q],radial:["",V,Q],conic:[Rl,V,Q]},by,vy]}],"bg-color":[{bg:j()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:j()}],"gradient-via":[{via:j()}],"gradient-to":[{to:j()}],rounded:[{rounded:B()}],"rounded-s":[{"rounded-s":B()}],"rounded-e":[{"rounded-e":B()}],"rounded-t":[{"rounded-t":B()}],"rounded-r":[{"rounded-r":B()}],"rounded-b":[{"rounded-b":B()}],"rounded-l":[{"rounded-l":B()}],"rounded-ss":[{"rounded-ss":B()}],"rounded-se":[{"rounded-se":B()}],"rounded-ee":[{"rounded-ee":B()}],"rounded-es":[{"rounded-es":B()}],"rounded-tl":[{"rounded-tl":B()}],"rounded-tr":[{"rounded-tr":B()}],"rounded-br":[{"rounded-br":B()}],"rounded-bl":[{"rounded-bl":B()}],"border-w":[{border:G()}],"border-w-x":[{"border-x":G()}],"border-w-y":[{"border-y":G()}],"border-w-s":[{"border-s":G()}],"border-w-e":[{"border-e":G()}],"border-w-t":[{"border-t":G()}],"border-w-r":[{"border-r":G()}],"border-w-b":[{"border-b":G()}],"border-w-l":[{"border-l":G()}],"divide-x":[{"divide-x":G()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":G()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ft(),"hidden","none"]}],"divide-style":[{divide:[...ft(),"hidden","none"]}],"border-color":[{border:j()}],"border-color-x":[{"border-x":j()}],"border-color-y":[{"border-y":j()}],"border-color-s":[{"border-s":j()}],"border-color-e":[{"border-e":j()}],"border-color-t":[{"border-t":j()}],"border-color-r":[{"border-r":j()}],"border-color-b":[{"border-b":j()}],"border-color-l":[{"border-l":j()}],"divide-color":[{divide:j()}],"outline-style":[{outline:[...ft(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ct,V,Q]}],"outline-w":[{outline:["",ct,Xn,Jl]}],"outline-color":[{outline:j()}],shadow:[{shadow:["","none",M,ui,ni]}],"shadow-color":[{shadow:j()}],"inset-shadow":[{"inset-shadow":["none",C,ui,ni]}],"inset-shadow-color":[{"inset-shadow":j()}],"ring-w":[{ring:G()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:j()}],"ring-offset-w":[{"ring-offset":[ct,Jl]}],"ring-offset-color":[{"ring-offset":j()}],"inset-ring-w":[{"inset-ring":G()}],"inset-ring-color":[{"inset-ring":j()}],"text-shadow":[{"text-shadow":["none",H,ui,ni]}],"text-shadow-color":[{"text-shadow":j()}],opacity:[{opacity:[ct,V,Q]}],"mix-blend":[{"mix-blend":[...I(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":I()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ct]}],"mask-image-linear-from-pos":[{"mask-linear-from":vt()}],"mask-image-linear-to-pos":[{"mask-linear-to":vt()}],"mask-image-linear-from-color":[{"mask-linear-from":j()}],"mask-image-linear-to-color":[{"mask-linear-to":j()}],"mask-image-t-from-pos":[{"mask-t-from":vt()}],"mask-image-t-to-pos":[{"mask-t-to":vt()}],"mask-image-t-from-color":[{"mask-t-from":j()}],"mask-image-t-to-color":[{"mask-t-to":j()}],"mask-image-r-from-pos":[{"mask-r-from":vt()}],"mask-image-r-to-pos":[{"mask-r-to":vt()}],"mask-image-r-from-color":[{"mask-r-from":j()}],"mask-image-r-to-color":[{"mask-r-to":j()}],"mask-image-b-from-pos":[{"mask-b-from":vt()}],"mask-image-b-to-pos":[{"mask-b-to":vt()}],"mask-image-b-from-color":[{"mask-b-from":j()}],"mask-image-b-to-color":[{"mask-b-to":j()}],"mask-image-l-from-pos":[{"mask-l-from":vt()}],"mask-image-l-to-pos":[{"mask-l-to":vt()}],"mask-image-l-from-color":[{"mask-l-from":j()}],"mask-image-l-to-color":[{"mask-l-to":j()}],"mask-image-x-from-pos":[{"mask-x-from":vt()}],"mask-image-x-to-pos":[{"mask-x-to":vt()}],"mask-image-x-from-color":[{"mask-x-from":j()}],"mask-image-x-to-color":[{"mask-x-to":j()}],"mask-image-y-from-pos":[{"mask-y-from":vt()}],"mask-image-y-to-pos":[{"mask-y-to":vt()}],"mask-image-y-from-color":[{"mask-y-from":j()}],"mask-image-y-to-color":[{"mask-y-to":j()}],"mask-image-radial":[{"mask-radial":[V,Q]}],"mask-image-radial-from-pos":[{"mask-radial-from":vt()}],"mask-image-radial-to-pos":[{"mask-radial-to":vt()}],"mask-image-radial-from-color":[{"mask-radial-from":j()}],"mask-image-radial-to-color":[{"mask-radial-to":j()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":et()}],"mask-image-conic-pos":[{"mask-conic":[ct]}],"mask-image-conic-from-pos":[{"mask-conic-from":vt()}],"mask-image-conic-to-pos":[{"mask-conic-to":vt()}],"mask-image-conic-from-color":[{"mask-conic-from":j()}],"mask-image-conic-to-color":[{"mask-conic-to":j()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:bt()}],"mask-repeat":[{mask:h()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",V,Q]}],filter:[{filter:["","none",V,Q]}],blur:[{blur:Rt()}],brightness:[{brightness:[ct,V,Q]}],contrast:[{contrast:[ct,V,Q]}],"drop-shadow":[{"drop-shadow":["","none",k,ui,ni]}],"drop-shadow-color":[{"drop-shadow":j()}],grayscale:[{grayscale:["",ct,V,Q]}],"hue-rotate":[{"hue-rotate":[ct,V,Q]}],invert:[{invert:["",ct,V,Q]}],saturate:[{saturate:[ct,V,Q]}],sepia:[{sepia:["",ct,V,Q]}],"backdrop-filter":[{"backdrop-filter":["","none",V,Q]}],"backdrop-blur":[{"backdrop-blur":Rt()}],"backdrop-brightness":[{"backdrop-brightness":[ct,V,Q]}],"backdrop-contrast":[{"backdrop-contrast":[ct,V,Q]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ct,V,Q]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ct,V,Q]}],"backdrop-invert":[{"backdrop-invert":["",ct,V,Q]}],"backdrop-opacity":[{"backdrop-opacity":[ct,V,Q]}],"backdrop-saturate":[{"backdrop-saturate":[ct,V,Q]}],"backdrop-sepia":[{"backdrop-sepia":["",ct,V,Q]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Z()}],"border-spacing-x":[{"border-spacing-x":Z()}],"border-spacing-y":[{"border-spacing-y":Z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",V,Q]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ct,"initial",V,Q]}],ease:[{ease:["linear","initial",Tt,V,Q]}],delay:[{delay:[ct,V,Q]}],animate:[{animate:["none",jt,V,Q]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[tt,V,Q]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:ce()}],"rotate-x":[{"rotate-x":ce()}],"rotate-y":[{"rotate-y":ce()}],"rotate-z":[{"rotate-z":ce()}],scale:[{scale:ll()}],"scale-x":[{"scale-x":ll()}],"scale-y":[{"scale-y":ll()}],"scale-z":[{"scale-z":ll()}],"scale-3d":["scale-3d"],skew:[{skew:al()}],"skew-x":[{"skew-x":al()}],"skew-y":[{"skew-y":al()}],transform:[{transform:[V,Q,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:nl()}],"translate-x":[{"translate-x":nl()}],"translate-y":[{"translate-y":nl()}],"translate-z":[{"translate-z":nl()}],"translate-none":["translate-none"],accent:[{accent:j()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:j()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V,Q]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Z()}],"scroll-mx":[{"scroll-mx":Z()}],"scroll-my":[{"scroll-my":Z()}],"scroll-ms":[{"scroll-ms":Z()}],"scroll-me":[{"scroll-me":Z()}],"scroll-mt":[{"scroll-mt":Z()}],"scroll-mr":[{"scroll-mr":Z()}],"scroll-mb":[{"scroll-mb":Z()}],"scroll-ml":[{"scroll-ml":Z()}],"scroll-p":[{"scroll-p":Z()}],"scroll-px":[{"scroll-px":Z()}],"scroll-py":[{"scroll-py":Z()}],"scroll-ps":[{"scroll-ps":Z()}],"scroll-pe":[{"scroll-pe":Z()}],"scroll-pt":[{"scroll-pt":Z()}],"scroll-pr":[{"scroll-pr":Z()}],"scroll-pb":[{"scroll-pb":Z()}],"scroll-pl":[{"scroll-pl":Z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V,Q]}],fill:[{fill:["none",...j()]}],"stroke-w":[{stroke:[ct,Xn,Jl,Cr]}],stroke:[{stroke:["none",...j()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ay=ly(xy);function el(...c){return Ay(mm(c))}const Ty=hm("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function He({className:c,variant:s,size:f,asChild:o=!1,...m}){const b=o?fm:"button";return _.jsx(b,{"data-slot":"button",className:el(Ty({variant:s,size:f,className:c})),...m})}function am({className:c,type:s,...f}){return _.jsx("input",{type:s,"data-slot":"input",className:el("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",c),...f})}function ii({className:c,...s}){return _.jsx("div",{"data-slot":"card",className:el("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",c),...s})}function nm({className:c,...s}){return _.jsx("div",{"data-slot":"card-header",className:el("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",c),...s})}function um({className:c,...s}){return _.jsx("div",{"data-slot":"card-title",className:el("leading-none font-semibold",c),...s})}function ci({className:c,...s}){return _.jsx("div",{"data-slot":"card-content",className:el("px-6",c),...s})}const Ey=hm("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function zy({className:c,variant:s,asChild:f=!1,...o}){const m=f?fm:"span";return _.jsx(m,{"data-slot":"badge",className:el(Ey({variant:s}),c),...o})}cm();var _y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ln=_y.reduce((c,s)=>{const f=sm(`Primitive.${s}`),o=w.forwardRef((m,b)=>{const{asChild:E,...N}=m,p=E?f:s;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),_.jsx(p,{...N,ref:b})});return o.displayName=`Primitive.${s}`,{...c,[s]:o}},{}),qr=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{};function My(c,s){return w.useReducer((f,o)=>s[f][o]??f,c)}var Qn=c=>{const{present:s,children:f}=c,o=Ny(s),m=typeof f=="function"?f({present:o.isPresent}):w.Children.only(f),b=Fl(o.ref,Ry(m));return typeof f=="function"||o.isPresent?w.cloneElement(m,{ref:b}):null};Qn.displayName="Presence";function Ny(c){const[s,f]=w.useState(),o=w.useRef(null),m=w.useRef(c),b=w.useRef("none"),E=c?"mounted":"unmounted",[N,p]=My(E,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const y=ri(o.current);b.current=N==="mounted"?y:"none"},[N]),qr(()=>{const y=o.current,M=m.current;if(M!==c){const H=b.current,k=ri(y);c?p("MOUNT"):k==="none"||(y==null?void 0:y.display)==="none"?p("UNMOUNT"):p(M&&H!==k?"ANIMATION_OUT":"UNMOUNT"),m.current=c}},[c,p]),qr(()=>{if(s){let y;const M=s.ownerDocument.defaultView??window,C=k=>{const tt=ri(o.current).includes(k.animationName);if(k.target===s&&tt&&(p("ANIMATION_END"),!m.current)){const ot=s.style.animationFillMode;s.style.animationFillMode="forwards",y=M.setTimeout(()=>{s.style.animationFillMode==="forwards"&&(s.style.animationFillMode=ot)})}},H=k=>{k.target===s&&(b.current=ri(o.current))};return s.addEventListener("animationstart",H),s.addEventListener("animationcancel",C),s.addEventListener("animationend",C),()=>{M.clearTimeout(y),s.removeEventListener("animationstart",H),s.removeEventListener("animationcancel",C),s.removeEventListener("animationend",C)}}else p("ANIMATION_END")},[s,p]),{isPresent:["mounted","unmountSuspended"].includes(N),ref:w.useCallback(y=>{o.current=y?getComputedStyle(y):null,f(y)},[])}}function ri(c){return(c==null?void 0:c.animationName)||"none"}function Ry(c){var o,m;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,f=s&&"isReactWarning"in s&&s.isReactWarning;return f?c.ref:(s=(m=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:m.get,f=s&&"isReactWarning"in s&&s.isReactWarning,f?c.props.ref:c.props.ref||c.ref)}function Oy(c,s=[]){let f=[];function o(b,E){const N=w.createContext(E),p=f.length;f=[...f,E];const y=C=>{var Tt;const{scope:H,children:k,...J}=C,tt=((Tt=H==null?void 0:H[c])==null?void 0:Tt[p])||N,ot=w.useMemo(()=>J,Object.values(J));return _.jsx(tt.Provider,{value:ot,children:k})};y.displayName=b+"Provider";function M(C,H){var tt;const k=((tt=H==null?void 0:H[c])==null?void 0:tt[p])||N,J=w.useContext(k);if(J)return J;if(E!==void 0)return E;throw new Error(`\`${C}\` must be used within \`${b}\``)}return[y,M]}const m=()=>{const b=f.map(E=>w.createContext(E));return function(N){const p=(N==null?void 0:N[c])||b;return w.useMemo(()=>({[`__scope${c}`]:{...N,[c]:p}}),[N,p])}};return m.scopeName=c,[o,Dy(m,...s)]}function Dy(...c){const s=c[0];if(c.length===1)return s;const f=()=>{const o=c.map(m=>({useScope:m(),scopeName:m.scopeName}));return function(b){const E=o.reduce((N,{useScope:p,scopeName:y})=>{const C=p(b)[`__scope${y}`];return{...N,...C}},{});return w.useMemo(()=>({[`__scope${s.scopeName}`]:E}),[E])}};return f.scopeName=s.scopeName,f}function Wl(c){const s=w.useRef(c);return w.useEffect(()=>{s.current=c}),w.useMemo(()=>(...f)=>{var o;return(o=s.current)==null?void 0:o.call(s,...f)},[])}var wy=w.createContext(void 0);function Uy(c){const s=w.useContext(wy);return c||s||"ltr"}function Cy(c,[s,f]){return Math.min(f,Math.max(s,c))}function $l(c,s,{checkForDefaultPrevented:f=!0}={}){return function(m){if(c==null||c(m),f===!1||!m.defaultPrevented)return s==null?void 0:s(m)}}function jy(c,s){return w.useReducer((f,o)=>s[f][o]??f,c)}var Xr="ScrollArea",[zm,Py]=Oy(Xr),[Hy,_e]=zm(Xr),_m=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,type:o="hover",dir:m,scrollHideDelay:b=600,...E}=c,[N,p]=w.useState(null),[y,M]=w.useState(null),[C,H]=w.useState(null),[k,J]=w.useState(null),[tt,ot]=w.useState(null),[Tt,jt]=w.useState(0),[pt,et]=w.useState(0),[P,St]=w.useState(!1),[Mt,Z]=w.useState(!1),at=Fl(s,X=>p(X)),Ut=Uy(m);return _.jsx(Hy,{scope:f,type:o,dir:Ut,scrollHideDelay:b,scrollArea:N,viewport:y,onViewportChange:M,content:C,onContentChange:H,scrollbarX:k,onScrollbarXChange:J,scrollbarXEnabled:P,onScrollbarXEnabledChange:St,scrollbarY:tt,onScrollbarYChange:ot,scrollbarYEnabled:Mt,onScrollbarYEnabledChange:Z,onCornerWidthChange:jt,onCornerHeightChange:et,children:_.jsx(Ln.div,{dir:Ut,...E,ref:at,style:{position:"relative","--radix-scroll-area-corner-width":Tt+"px","--radix-scroll-area-corner-height":pt+"px",...c.style}})})});_m.displayName=Xr;var Mm="ScrollAreaViewport",Nm=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,children:o,nonce:m,...b}=c,E=_e(Mm,f),N=w.useRef(null),p=Fl(s,N,E.onViewportChange);return _.jsxs(_.Fragment,{children:[_.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:m}),_.jsx(Ln.div,{"data-radix-scroll-area-viewport":"",...b,ref:p,style:{overflowX:E.scrollbarXEnabled?"scroll":"hidden",overflowY:E.scrollbarYEnabled?"scroll":"hidden",...c.style},children:_.jsx("div",{ref:E.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});Nm.displayName=Mm;var qe="ScrollAreaScrollbar",Rm=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=_e(qe,c.__scopeScrollArea),{onScrollbarXEnabledChange:b,onScrollbarYEnabledChange:E}=m,N=c.orientation==="horizontal";return w.useEffect(()=>(N?b(!0):E(!0),()=>{N?b(!1):E(!1)}),[N,b,E]),m.type==="hover"?_.jsx(By,{...o,ref:s,forceMount:f}):m.type==="scroll"?_.jsx(qy,{...o,ref:s,forceMount:f}):m.type==="auto"?_.jsx(Om,{...o,ref:s,forceMount:f}):m.type==="always"?_.jsx(Lr,{...o,ref:s}):null});Rm.displayName=qe;var By=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=_e(qe,c.__scopeScrollArea),[b,E]=w.useState(!1);return w.useEffect(()=>{const N=m.scrollArea;let p=0;if(N){const y=()=>{window.clearTimeout(p),E(!0)},M=()=>{p=window.setTimeout(()=>E(!1),m.scrollHideDelay)};return N.addEventListener("pointerenter",y),N.addEventListener("pointerleave",M),()=>{window.clearTimeout(p),N.removeEventListener("pointerenter",y),N.removeEventListener("pointerleave",M)}}},[m.scrollArea,m.scrollHideDelay]),_.jsx(Qn,{present:f||b,children:_.jsx(Om,{"data-state":b?"visible":"hidden",...o,ref:s})})}),qy=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=_e(qe,c.__scopeScrollArea),b=c.orientation==="horizontal",E=di(()=>p("SCROLL_END"),100),[N,p]=jy("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return w.useEffect(()=>{if(N==="idle"){const y=window.setTimeout(()=>p("HIDE"),m.scrollHideDelay);return()=>window.clearTimeout(y)}},[N,m.scrollHideDelay,p]),w.useEffect(()=>{const y=m.viewport,M=b?"scrollLeft":"scrollTop";if(y){let C=y[M];const H=()=>{const k=y[M];C!==k&&(p("SCROLL"),E()),C=k};return y.addEventListener("scroll",H),()=>y.removeEventListener("scroll",H)}},[m.viewport,b,p,E]),_.jsx(Qn,{present:f||N!=="hidden",children:_.jsx(Lr,{"data-state":N==="hidden"?"hidden":"visible",...o,ref:s,onPointerEnter:$l(c.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:$l(c.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),Om=w.forwardRef((c,s)=>{const f=_e(qe,c.__scopeScrollArea),{forceMount:o,...m}=c,[b,E]=w.useState(!1),N=c.orientation==="horizontal",p=di(()=>{if(f.viewport){const y=f.viewport.offsetWidth<f.viewport.scrollWidth,M=f.viewport.offsetHeight<f.viewport.scrollHeight;E(N?y:M)}},10);return qa(f.viewport,p),qa(f.content,p),_.jsx(Qn,{present:o||b,children:_.jsx(Lr,{"data-state":b?"visible":"hidden",...m,ref:s})})}),Lr=w.forwardRef((c,s)=>{const{orientation:f="vertical",...o}=c,m=_e(qe,c.__scopeScrollArea),b=w.useRef(null),E=w.useRef(0),[N,p]=w.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),y=jm(N.viewport,N.content),M={...o,sizes:N,onSizesChange:p,hasThumb:y>0&&y<1,onThumbChange:H=>b.current=H,onThumbPointerUp:()=>E.current=0,onThumbPointerDown:H=>E.current=H};function C(H,k){return Vy(H,E.current,N,k)}return f==="horizontal"?_.jsx(Yy,{...M,ref:s,onThumbPositionChange:()=>{if(m.viewport&&b.current){const H=m.viewport.scrollLeft,k=im(H,N,m.dir);b.current.style.transform=`translate3d(${k}px, 0, 0)`}},onWheelScroll:H=>{m.viewport&&(m.viewport.scrollLeft=H)},onDragScroll:H=>{m.viewport&&(m.viewport.scrollLeft=C(H,m.dir))}}):f==="vertical"?_.jsx(Gy,{...M,ref:s,onThumbPositionChange:()=>{if(m.viewport&&b.current){const H=m.viewport.scrollTop,k=im(H,N);b.current.style.transform=`translate3d(0, ${k}px, 0)`}},onWheelScroll:H=>{m.viewport&&(m.viewport.scrollTop=H)},onDragScroll:H=>{m.viewport&&(m.viewport.scrollTop=C(H))}}):null}),Yy=w.forwardRef((c,s)=>{const{sizes:f,onSizesChange:o,...m}=c,b=_e(qe,c.__scopeScrollArea),[E,N]=w.useState(),p=w.useRef(null),y=Fl(s,p,b.onScrollbarXChange);return w.useEffect(()=>{p.current&&N(getComputedStyle(p.current))},[p]),_.jsx(wm,{"data-orientation":"horizontal",...m,ref:y,sizes:f,style:{bottom:0,left:b.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:b.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":fi(f)+"px",...c.style},onThumbPointerDown:M=>c.onThumbPointerDown(M.x),onDragScroll:M=>c.onDragScroll(M.x),onWheelScroll:(M,C)=>{if(b.viewport){const H=b.viewport.scrollLeft+M.deltaX;c.onWheelScroll(H),Bm(H,C)&&M.preventDefault()}},onResize:()=>{p.current&&b.viewport&&E&&o({content:b.viewport.scrollWidth,viewport:b.viewport.offsetWidth,scrollbar:{size:p.current.clientWidth,paddingStart:si(E.paddingLeft),paddingEnd:si(E.paddingRight)}})}})}),Gy=w.forwardRef((c,s)=>{const{sizes:f,onSizesChange:o,...m}=c,b=_e(qe,c.__scopeScrollArea),[E,N]=w.useState(),p=w.useRef(null),y=Fl(s,p,b.onScrollbarYChange);return w.useEffect(()=>{p.current&&N(getComputedStyle(p.current))},[p]),_.jsx(wm,{"data-orientation":"vertical",...m,ref:y,sizes:f,style:{top:0,right:b.dir==="ltr"?0:void 0,left:b.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":fi(f)+"px",...c.style},onThumbPointerDown:M=>c.onThumbPointerDown(M.y),onDragScroll:M=>c.onDragScroll(M.y),onWheelScroll:(M,C)=>{if(b.viewport){const H=b.viewport.scrollTop+M.deltaY;c.onWheelScroll(H),Bm(H,C)&&M.preventDefault()}},onResize:()=>{p.current&&b.viewport&&E&&o({content:b.viewport.scrollHeight,viewport:b.viewport.offsetHeight,scrollbar:{size:p.current.clientHeight,paddingStart:si(E.paddingTop),paddingEnd:si(E.paddingBottom)}})}})}),[Xy,Dm]=zm(qe),wm=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,sizes:o,hasThumb:m,onThumbChange:b,onThumbPointerUp:E,onThumbPointerDown:N,onThumbPositionChange:p,onDragScroll:y,onWheelScroll:M,onResize:C,...H}=c,k=_e(qe,f),[J,tt]=w.useState(null),ot=Fl(s,at=>tt(at)),Tt=w.useRef(null),jt=w.useRef(""),pt=k.viewport,et=o.content-o.viewport,P=Wl(M),St=Wl(p),Mt=di(C,10);function Z(at){if(Tt.current){const Ut=at.clientX-Tt.current.left,X=at.clientY-Tt.current.top;y({x:Ut,y:X})}}return w.useEffect(()=>{const at=Ut=>{const X=Ut.target;(J==null?void 0:J.contains(X))&&P(Ut,et)};return document.addEventListener("wheel",at,{passive:!1}),()=>document.removeEventListener("wheel",at,{passive:!1})},[pt,J,et,P]),w.useEffect(St,[o,St]),qa(J,Mt),qa(k.content,Mt),_.jsx(Xy,{scope:f,scrollbar:J,hasThumb:m,onThumbChange:Wl(b),onThumbPointerUp:Wl(E),onThumbPositionChange:St,onThumbPointerDown:Wl(N),children:_.jsx(Ln.div,{...H,ref:ot,style:{position:"absolute",...H.style},onPointerDown:$l(c.onPointerDown,at=>{at.button===0&&(at.target.setPointerCapture(at.pointerId),Tt.current=J.getBoundingClientRect(),jt.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",k.viewport&&(k.viewport.style.scrollBehavior="auto"),Z(at))}),onPointerMove:$l(c.onPointerMove,Z),onPointerUp:$l(c.onPointerUp,at=>{const Ut=at.target;Ut.hasPointerCapture(at.pointerId)&&Ut.releasePointerCapture(at.pointerId),document.body.style.webkitUserSelect=jt.current,k.viewport&&(k.viewport.style.scrollBehavior=""),Tt.current=null})})})}),oi="ScrollAreaThumb",Um=w.forwardRef((c,s)=>{const{forceMount:f,...o}=c,m=Dm(oi,c.__scopeScrollArea);return _.jsx(Qn,{present:f||m.hasThumb,children:_.jsx(Ly,{ref:s,...o})})}),Ly=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,style:o,...m}=c,b=_e(oi,f),E=Dm(oi,f),{onThumbPositionChange:N}=E,p=Fl(s,C=>E.onThumbChange(C)),y=w.useRef(void 0),M=di(()=>{y.current&&(y.current(),y.current=void 0)},100);return w.useEffect(()=>{const C=b.viewport;if(C){const H=()=>{if(M(),!y.current){const k=Zy(C,N);y.current=k,N()}};return N(),C.addEventListener("scroll",H),()=>C.removeEventListener("scroll",H)}},[b.viewport,M,N]),_.jsx(Ln.div,{"data-state":E.hasThumb?"visible":"hidden",...m,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:$l(c.onPointerDownCapture,C=>{const k=C.target.getBoundingClientRect(),J=C.clientX-k.left,tt=C.clientY-k.top;E.onThumbPointerDown({x:J,y:tt})}),onPointerUp:$l(c.onPointerUp,E.onThumbPointerUp)})});Um.displayName=oi;var Qr="ScrollAreaCorner",Cm=w.forwardRef((c,s)=>{const f=_e(Qr,c.__scopeScrollArea),o=!!(f.scrollbarX&&f.scrollbarY);return f.type!=="scroll"&&o?_.jsx(Qy,{...c,ref:s}):null});Cm.displayName=Qr;var Qy=w.forwardRef((c,s)=>{const{__scopeScrollArea:f,...o}=c,m=_e(Qr,f),[b,E]=w.useState(0),[N,p]=w.useState(0),y=!!(b&&N);return qa(m.scrollbarX,()=>{var C;const M=((C=m.scrollbarX)==null?void 0:C.offsetHeight)||0;m.onCornerHeightChange(M),p(M)}),qa(m.scrollbarY,()=>{var C;const M=((C=m.scrollbarY)==null?void 0:C.offsetWidth)||0;m.onCornerWidthChange(M),E(M)}),y?_.jsx(Ln.div,{...o,ref:s,style:{width:b,height:N,position:"absolute",right:m.dir==="ltr"?0:void 0,left:m.dir==="rtl"?0:void 0,bottom:0,...c.style}}):null});function si(c){return c?parseInt(c,10):0}function jm(c,s){const f=c/s;return isNaN(f)?0:f}function fi(c){const s=jm(c.viewport,c.content),f=c.scrollbar.paddingStart+c.scrollbar.paddingEnd,o=(c.scrollbar.size-f)*s;return Math.max(o,18)}function Vy(c,s,f,o="ltr"){const m=fi(f),b=m/2,E=s||b,N=m-E,p=f.scrollbar.paddingStart+E,y=f.scrollbar.size-f.scrollbar.paddingEnd-N,M=f.content-f.viewport,C=o==="ltr"?[0,M]:[M*-1,0];return Hm([p,y],C)(c)}function im(c,s,f="ltr"){const o=fi(s),m=s.scrollbar.paddingStart+s.scrollbar.paddingEnd,b=s.scrollbar.size-m,E=s.content-s.viewport,N=b-o,p=f==="ltr"?[0,E]:[E*-1,0],y=Cy(c,p);return Hm([0,E],[0,N])(y)}function Hm(c,s){return f=>{if(c[0]===c[1]||s[0]===s[1])return s[0];const o=(s[1]-s[0])/(c[1]-c[0]);return s[0]+o*(f-c[0])}}function Bm(c,s){return c>0&&c<s}var Zy=(c,s=()=>{})=>{let f={left:c.scrollLeft,top:c.scrollTop},o=0;return function m(){const b={left:c.scrollLeft,top:c.scrollTop},E=f.left!==b.left,N=f.top!==b.top;(E||N)&&s(),f=b,o=window.requestAnimationFrame(m)}(),()=>window.cancelAnimationFrame(o)};function di(c,s){const f=Wl(c),o=w.useRef(0);return w.useEffect(()=>()=>window.clearTimeout(o.current),[]),w.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(f,s)},[f,s])}function qa(c,s){const f=Wl(s);qr(()=>{let o=0;if(c){const m=new ResizeObserver(()=>{cancelAnimationFrame(o),o=window.requestAnimationFrame(f)});return m.observe(c),()=>{window.cancelAnimationFrame(o),m.unobserve(c)}}},[c,f])}var ky=_m,Ky=Nm,Jy=Cm;function Wy({className:c,children:s,...f}){return _.jsxs(ky,{"data-slot":"scroll-area",className:el("relative",c),...f,children:[_.jsx(Ky,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:s}),_.jsx($y,{}),_.jsx(Jy,{})]})}function $y({className:c,orientation:s="vertical",...f}){return _.jsx(Rm,{"data-slot":"scroll-area-scrollbar",orientation:s,className:el("flex touch-none p-px transition-colors select-none",s==="vertical"&&"h-full w-2.5 border-l border-l-transparent",s==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",c),...f,children:_.jsx(Um,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function Fy(){const[c,s]=w.useState(""),[f,o]=w.useState("en"),[m,b]=w.useState([]),[E,N]=w.useState(!1),[p,y]=w.useState(""),[M,C]=w.useState([]),[H,k]=w.useState(""),[J,tt]=w.useState(!1),[ot,Tt]=w.useState(!1),[jt,pt]=w.useState(!0),et={en:{title:"Book Information Retrieval System",searchPlaceholder:"Enter book name or ask a question...",searchButton:"Search Books",categories:"Categories",downloadPdf:"Download PDF",publicDomain:"Public Domain - Free to Download",noResults:"No books found. Try a different search term.",error:"An error occurred while searching. Please try again.",loading:"Searching for books...",pdfLinks:"PDF Downloads",convert:"Convert",download:"Download",chatTitle:"AI Assistant",chatPlaceholder:"Ask me anything about books...",sendMessage:"Send",llmLoading:"AI is thinking...",relatedBooks:"Related Books",askAboutBook:"Ask AI about this book",askAboutRelated:"Get related books from AI"},ar:{title:"نظام استرجاع معلومات الكتب",searchPlaceholder:"أدخل اسم الكتاب أو اطرح سؤالاً...",searchButton:"البحث عن الكتب",categories:"التصنيفات",downloadPdf:"تحميل PDF",publicDomain:"ملكية عامة - مجاني للتحميل",noResults:"لم يتم العثور على كتب. جرب مصطلح بحث مختلف.",error:"حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.",loading:"البحث عن الكتب...",pdfLinks:"تحميلات PDF",convert:"تحويل",download:"تحميل",chatTitle:"مساعد الذكاء الاصطناعي",chatPlaceholder:"اسألني أي شيء عن الكتب...",sendMessage:"إرسال",llmLoading:"الذكاء الاصطناعي يفكر...",relatedBooks:"كتب ذات صلة",askAboutBook:"اسأل الذكاء الاصطناعي عن هذا الكتاب",askAboutRelated:"احصل على كتب ذات صلة من الذكاء الاصطناعي"}}[f];w.useEffect(()=>{document.documentElement.dir=f==="ar"?"rtl":"ltr"},[f]);const P=async()=>{if(c.trim()){N(!0),y(""),b([]);try{const X=await fetch("/api/books/enhanced-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:c,lang:f})});if(!X.ok)throw new Error("Search failed");const lt=await X.json();b(lt.results||[])}catch(X){y("Failed to search books. Please try again."),console.error("Search error:",X)}finally{N(!1)}}},St=async(X,lt)=>{try{const ut=await fetch("/api/books/convert-to-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_url:X,output_filename:`converted_book.${lt}.pdf`})});if(ut.ok){const Nt=await ut.blob(),st=window.URL.createObjectURL(Nt),z=document.createElement("a");z.href=st,z.download=`converted_book.${lt}.pdf`,document.body.appendChild(z),z.click(),window.URL.revokeObjectURL(st),document.body.removeChild(z)}else alert("Conversion failed. Please try again.")}catch(ut){console.error("Conversion error:",ut),alert("Conversion failed. Please try again.")}},Mt=async()=>{if(!H.trim())return;const X=H;C(lt=>[...lt,{sender:"user",text:X}]),k(""),tt(!0);try{const lt=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:X})});if(!lt.ok)throw new Error("LLM chat failed");const ut=await lt.json();C(Nt=>[...Nt,{sender:"ai",text:ut.response}])}catch(lt){console.error("LLM chat error:",lt),C(ut=>[...ut,{sender:"ai",text:et.error}])}finally{tt(!1)}},Z=async(X,lt)=>{tt(!0),C(ut=>[...ut,{sender:"user",text:`${et.askAboutRelated}: ${X} by ${lt}`}]);try{const ut=await fetch("/api/llm/related-books",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:X,author:lt})});if(!ut.ok)throw new Error("Related books failed");const Nt=await ut.json();if(Nt.related_books&&Nt.related_books.length>0){const st=Nt.related_books.map(z=>`${z.title} - ${z.author}`).join(`
`);C(z=>[...z,{sender:"ai",text:`${et.relatedBooks}:
${st}`}])}else C(st=>[...st,{sender:"ai",text:"No related books found."}])}catch(ut){console.error("Related books error:",ut),C(Nt=>[...Nt,{sender:"ai",text:et.error}])}finally{tt(!1)}},at=async(X,lt,ut)=>{const Nt=`Tell me about the book: ${X} by ${lt}. Here is a brief description: ${ut}.`;C(st=>[...st,{sender:"user",text:`${et.askAboutBook}: ${X}`}]),tt(!0);try{const st=await fetch("/api/llm/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:Nt})});if(!st.ok)throw new Error("LLM chat failed");const z=await st.json();C(q=>[...q,{sender:"ai",text:z.response}])}catch(st){console.error("LLM chat error:",st),C(z=>[...z,{sender:"ai",text:et.error}])}finally{tt(!1)}},Ut=()=>{o(f==="en"?"ar":"en")};return _.jsx("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${f==="ar"?"rtl":"ltr"}`,children:_.jsxs("div",{className:"container mx-auto px-4 py-8",children:[_.jsxs("div",{className:"text-center mb-8",children:[_.jsxs("div",{className:"flex justify-between items-center mb-4",children:[_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsx(Wd,{className:"h-8 w-8 text-blue-600"}),_.jsx("span",{className:"text-xl font-bold text-blue-600",children:"BookFinder AI"})]}),_.jsxs(He,{variant:"outline",size:"sm",onClick:Ut,className:"flex items-center gap-2",children:[_.jsx(E0,{className:"h-4 w-4"}),f==="en"?"العربية":"English"]})]}),_.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:et.title}),_.jsx("p",{className:"text-lg text-gray-600",children:et.subtitle})]}),_.jsx(ii,{className:"max-w-2xl mx-auto mb-8",children:_.jsx(ci,{className:"p-6",children:_.jsxs("div",{className:"flex gap-4",children:[_.jsx("div",{className:"flex-1",children:_.jsx(am,{type:"text",placeholder:et.searchPlaceholder,value:c,onChange:X=>s(X.target.value),onKeyPress:X=>X.key==="Enter"&&P(),className:"text-lg",dir:f==="ar"?"rtl":"ltr"})}),_.jsxs(He,{onClick:P,disabled:E||!c.trim(),className:"px-6",children:[_.jsx(D0,{className:"h-4 w-4 mr-2"}),et.searchButton]})]})})}),E&&_.jsxs("div",{className:"text-center py-8",children:[_.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),_.jsx("p",{className:"text-gray-600",children:et.loading})]}),p&&_.jsx("div",{className:"max-w-2xl mx-auto mb-8",children:_.jsx(ii,{className:"border-red-200 bg-red-50",children:_.jsx(ci,{className:"p-4",children:_.jsx("p",{className:"text-red-600 text-center",children:p})})})}),m.length>0&&_.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:m.map((X,lt)=>_.jsxs(ii,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[_.jsx(nm,{className:"pb-4",children:_.jsxs("div",{className:"flex gap-4",children:[_.jsx("img",{src:X.thumbnail||"/api/placeholder/120/180",alt:X.title,className:"w-20 h-30 object-cover rounded",onError:ut=>{ut.target.src="/api/placeholder/120/180"}}),_.jsxs("div",{className:"flex-1",children:[_.jsx(um,{className:"text-lg mb-2 line-clamp-2",children:X.title}),_.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-2",children:[_.jsx(j0,{className:"h-4 w-4"}),_.jsx("span",{children:X.author})]}),X.description&&_.jsx("p",{className:"text-xs text-gray-500 line-clamp-3",children:X.description})]})]})}),_.jsx(ci,{children:_.jsxs("div",{className:"space-y-3",children:[_.jsxs("div",{children:[_.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[_.jsx(U0,{className:"h-4 w-4 text-gray-500"}),_.jsx("span",{className:"text-sm font-medium",children:et.categories})]}),_.jsx("div",{className:"flex flex-wrap gap-1",children:X.categories.map((ut,Nt)=>_.jsx(zy,{variant:"secondary",className:"text-xs",children:ut},Nt))})]}),X.pdf_links&&X.pdf_links.length>0&&_.jsx("div",{className:"pt-2",children:_.jsx("div",{className:"space-y-2",children:X.pdf_links.map((ut,Nt)=>_.jsxs("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded text-sm",children:[_.jsxs("span",{className:"text-gray-600",children:[ut.source," ",ut.type==="convertible"&&`(${ut.format.toUpperCase()})`]}),ut.type==="convertible"?_.jsx(He,{onClick:()=>St(ut.url,ut.format),size:"sm",variant:"outline",className:"text-xs",children:et.convert}):_.jsx(He,{asChild:!0,size:"sm",className:"text-xs",children:_.jsx("a",{href:ut.url,target:"_blank",rel:"noopener noreferrer",children:et.download})})]},Nt))})}),_.jsxs("div",{className:"flex flex-col gap-2 pt-2",children:[_.jsx(He,{variant:"outline",size:"sm",onClick:()=>at(X.title,X.author,X.description),children:et.askAboutBook}),_.jsx(He,{variant:"outline",size:"sm",onClick:()=>Z(X.title,X.author),children:et.askAboutRelated})]})]})})]},X.id||lt))}),!E&&!p&&m.length===0&&c&&_.jsxs("div",{className:"text-center py-8",children:[_.jsx(Wd,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),_.jsx("p",{className:"text-gray-600",children:et.noResults})]}),jt&&_.jsxs(ii,{className:`fixed ${ot?"inset-4":"bottom-4 right-4 w-96 h-[500px]"} flex flex-col shadow-xl z-50 transition-all duration-300`,children:[_.jsxs(nm,{className:"flex flex-row items-center justify-between space-y-0 p-4 border-b bg-blue-50",children:[_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsx(wr,{className:"h-5 w-5 text-blue-600"}),_.jsx(um,{className:"text-lg font-semibold text-blue-800",children:et.chatTitle})]}),_.jsxs("div",{className:"flex items-center gap-1",children:[_.jsx(He,{size:"icon",variant:"ghost",onClick:()=>Tt(!ot),className:"h-8 w-8 hover:bg-blue-100",children:ot?_.jsx(R0,{className:"h-4 w-4"}):_.jsx(_0,{className:"h-4 w-4"})}),_.jsx(He,{size:"icon",variant:"ghost",onClick:()=>pt(!1),className:"h-8 w-8 hover:bg-red-100",children:_.jsx(B0,{className:"h-4 w-4"})})]})]}),_.jsx(ci,{className:"flex-1 p-4 overflow-hidden bg-white",children:_.jsx(Wy,{className:"h-full pr-4",children:_.jsxs("div",{className:"space-y-4",children:[M.length===0&&_.jsxs("div",{className:"text-center text-gray-500 py-8",children:[_.jsx(wr,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),_.jsx("p",{className:"text-sm",children:"Ask me anything about books!"}),_.jsx("p",{className:"text-xs mt-2",children:'Try: "Tell me about Rich Dad Poor Dad" or "Recommend finance books"'})]}),M.map((X,lt)=>_.jsx("div",{className:`flex ${X.sender==="user"?"justify-end":"justify-start"}`,children:_.jsx("div",{className:`max-w-[85%] p-3 rounded-lg shadow-sm ${X.sender==="user"?"bg-blue-500 text-white":"bg-gray-100 text-gray-800 border"}`,children:_.jsx("div",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:X.text})})},lt)),J&&_.jsx("div",{className:"flex justify-start",children:_.jsx("div",{className:"max-w-[85%] p-3 rounded-lg bg-gray-100 text-gray-800 border animate-pulse",children:_.jsxs("div",{className:"flex items-center gap-2",children:[_.jsxs("div",{className:"flex space-x-1",children:[_.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),_.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),_.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),_.jsx("span",{className:"text-sm",children:et.llmLoading})]})})})]})})}),_.jsxs("div",{className:"p-4 border-t bg-gray-50 flex items-center gap-2",children:[_.jsx(am,{placeholder:et.chatPlaceholder,value:H,onChange:X=>k(X.target.value),onKeyPress:X=>X.key==="Enter"&&Mt(),className:"flex-1 bg-white",dir:f==="ar"?"rtl":"ltr"}),_.jsx(He,{size:"icon",onClick:Mt,disabled:J||!H.trim(),className:"bg-blue-600 hover:bg-blue-700",children:_.jsx(A0,{className:"h-4 w-4"})})]})]}),!jt&&_.jsx(He,{onClick:()=>pt(!0),className:"fixed bottom-4 right-4 h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg z-50",size:"icon",children:_.jsx(wr,{className:"h-6 w-6"})})]})})}h0.createRoot(document.getElementById("root")).render(_.jsx(w.StrictMode,{children:_.jsx(Fy,{})}));
