<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .error { background-color: #ffebee; color: #c62828; }
        .success { background-color: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 10px 0; }
        input { padding: 8px; width: 300px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Book Search Frontend Test</h1>
    
    <div>
        <input type="text" id="searchInput" placeholder="Enter book name (e.g., rich dad poor dad)" value="rich dad poor dad">
        <br>
        <button onclick="testSearch()">Test Search</button>
        <button onclick="testAPI()">Test API Direct</button>
    </div>
    
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div>Testing API...</div>';
            
            try {
                const response = await fetch('/api/books/enhanced-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'rich dad poor dad',
                        lang: 'en'
                    }),
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="result success">
                        <h3>API Test Success!</h3>
                        <p>Found ${data.results.length} books</p>
                        <p>Status: ${response.status}</p>
                        <details>
                            <summary>Raw Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>API Test Failed!</h3>
                        <p>Error: ${error.message}</p>
                        <p>Check browser console for more details</p>
                    </div>
                `;
                console.error('API test error:', error);
            }
        }
        
        async function testSearch() {
            const searchInput = document.getElementById('searchInput');
            const resultsDiv = document.getElementById('results');
            const query = searchInput.value.trim();
            
            if (!query) {
                resultsDiv.innerHTML = '<div class="result error">Please enter a search query</div>';
                return;
            }
            
            resultsDiv.innerHTML = '<div>Searching...</div>';
            
            try {
                const response = await fetch('/api/books/enhanced-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        lang: 'en'
                    }),
                });
                
                if (!response.ok) {
                    throw new Error(`Search failed with status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.results && data.results.length > 0) {
                    let html = `<div class="result success"><h3>Found ${data.results.length} books:</h3>`;
                    
                    data.results.slice(0, 3).forEach((book, index) => {
                        html += `
                            <div style="margin: 10px 0; padding: 10px; border-left: 3px solid #2196F3;">
                                <h4>${book.title}</h4>
                                <p><strong>Author:</strong> ${book.author}</p>
                                <p><strong>Description:</strong> ${book.description ? book.description.substring(0, 200) + '...' : 'No description'}</p>
                                <p><strong>PDF Links:</strong> ${book.pdf_links.length} available</p>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = '<div class="result">No books found for this search</div>';
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>Search Failed!</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                console.error('Search error:', error);
            }
        }
        
        // Test on page load
        window.onload = function() {
            console.log('Page loaded, testing API...');
            testAPI();
        };
    </script>
</body>
</html>
